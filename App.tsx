import React, {useEffect, useState} from 'react';
import {AuthProvider} from './src/context/AuthContext';
import {Alert, LogBox} from 'react-native';
import StackNavigationScreen from './src/Navigation/StackNavigationScreen';
import {store} from './src/Redux/Store';
import {Provider} from 'react-redux';
import NetInfo from '@react-native-community/netinfo';
import notifee, {EventType} from '@notifee/react-native';

const App = () => {
  LogBox.ignoreAllLogs(); //Ignore all log notifications
  const [isConnected, setIsConnected] = useState<boolean | null>(true);
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected);
    });

    return () => {
      unsubscribe();
    };
  }, []);
  const renderConnectionStatus = () => {
    if (isConnected) {
      return;
    } else {
      return Alert.alert('Thông báo', '<PERSON>ui lòng kiểm tra kết nối mạng');
    }
  };
  useEffect(() => {
    renderConnectionStatus();
  });
  // useEffect(() => {
  //   notifee.onBackgroundEvent(async ({type, detail}) => {
  // Check if the user pressed the "Mark as read" action
  // if (type === EventType.ACTION_PRESS && pressAction.id === 'mark-as-read') {
  //   // Update external API
  //   await fetch(`https://my-api.com/chat/${notification.data.chatId}/read`, {
  //     method: 'POST',
  //   });
  //   // Remove the notification
  //   await notifee.cancelNotification(notification.id);
  // }
  //});
  // }, []);
  return (
    <Provider store={store}>
      <AuthProvider>
        <StackNavigationScreen />
      </AuthProvider>
    </Provider>
  );
};

export default App;
