<?xml version="1.0" encoding="UTF-8"?>
<lang type="vi">

    <screen >
        <maincolor>#FF6D22</maincolor>
        <backgroundcolor>#FFFFFF</backgroundcolor>
        <blackcolor>#000000</blackcolor>
        <graycolor>#808080</graycolor>
        <useroffline>#FF6347</useroffline>
        <useronline>#00FA9A</useronline>
        <userstop>#DCDCDC</userstop>
        <modalbackground>rgba(0,0,0,0.5)</modalbackground>
        <sivermember>#A0A0A0</sivermember>
        <goldmember>#EEAD0E</goldmember>
        <platiummember>#DA70D6</platiummember>
        <diamondmember>#66CDAA</diamondmember>
        <matchNumberPhone>^(?:(?:\+84|0)(?:2|3|5|7|8|9|1[2689])\d{7,8}|(?:\+81|0)(?:[1-9]\d{0,3}|70|80|90|50)\d{7,8}|(?:\+63|0)(?:9\d{2}|2)\d{7,8})(?:[-.\s]?\d{3,4})?\b</matchNumberPhone>
        <minValidNumber>2</minValidNumber>
        <maxValidNumberPhone>11</maxValidNumberPhone>
        <maxValidNumber>40</maxValidNumber>
        <minValidPassword>4</minValidPassword>
        <maxValidPassword>20</maxValidPassword>
        <maxValidSendInfo>200</maxValidSendInfo>
    </screen>

    <Viet>
        <titleApp>Phần mềm</titleApp>
        <qlbh>Quản lý bán hàng</qlbh>
        <nameApp>Delta POS</nameApp>
        <phone>Số điện thoại</phone>
        <pass>Mật khẩu</pass>
        <login>Đăng nhập</login>
        <registry>Đăng ký đối tác kinh doanh</registry>
        <contact>Liên hệ với chúng tôi</contact>
        <phoneContact>SĐT:***********</phoneContact>
        <email>Email:<EMAIL></email>
        <minValidMessageNumberPhone>Số điện thoại quá ngắn</minValidMessageNumberPhone>
        <maxValidMessageNumberPhone>Số điện thoại quá dài</maxValidMessageNumberPhone>
        <fieldIsBlank>Không được bỏ trống</fieldIsBlank>
        <matchesNumberPhone>Nhập đúng định dạng số điện thoại</matchesNumberPhone>
        <minValidMessagePassword>Mật khẩu quá ngắn</minValidMessagePassword>
        <maxValidMessagePassword>Mật khẩu quá dài</maxValidMessagePassword>
        <confirmPasswordMessage>Mật khẩu xác thực phải trùng khớp với mật khẩu</confirmPasswordMessage>
        <minValidMessageName>Họ và tên lớn hơn 2 kí tự</minValidMessageName>
        <maxValidMessageName>Họ và tên bé hơn 40 kí tự</maxValidMessageName>
        <minValidMessageAddress>Địa chỉ lớn hơn 4 kí tự</minValidMessageAddress>
        <maxValidMessageAddress>Địa chỉ bé hơn 40 kí tự</maxValidMessageAddress>
        <minValidMessagePSendInfo>Nội dung liên lạc quá ngắn</minValidMessagePSendInfo>
        <maxValidMessagePSendInfo>Nội dung liên lạc quá dài</maxValidMessagePSendInfo>
        <send>Gửi</send>
        <currencySymbol>đ</currencySymbol>
        <backToPage>Bạn muốn quay lại trang trước</backToPage>
        <close>Đóng</close>
        <tokenExpiresed>Phiên đăng nhập của bạn đã hết hạn .Vui lòng đăng nhập lại</tokenExpiresed>
        <messageApp>Thông báo</messageApp>
        <createCustomersFail>Gửi thông tin liên hệ thất bại</createCustomersFail>
        <createCustomersSuccess>Thêm khách hàng thành công</createCustomersSuccess>
        <createCustomersNumberPhoneExist>Số điện thoại khách hàng đã tồn tại</createCustomersNumberPhoneExist>
        <createCustomerNameFillInBlank>Vui lòng điền tên khách hàng</createCustomerNameFillInBlank>
        <vietVangPhoneNumber>***********</vietVangPhoneNumber>
        <agree>Đồng ý</agree>
        <cancel>Huỷ bỏ</cancel>
        <networkIsError>Có lỗi xảy ra</networkIsError>
        <upLoadImgFail>Có lỗi xảy ra vui lòng thử lại sau</upLoadImgFail>
        <loginFail>Sai mật khẩu hoặc tài khoản</loginFail>
        <fillInBlank>Không đc bỏ trống</fillInBlank>
        <networkError>Không có mạng vui lòng đăng nhập lại sau</networkError>
        <messageDisable>Gửi yêu cầu xoá tài khoản admin thành công .Bạn đã bị đăng xuất</messageDisable>
        <addCategorySuccess>Thêm danh mục thành công</addCategorySuccess>
        <fillInBlankCategory>Chưa điền tên danh mục</fillInBlankCategory>
        <categoryNameExist>Tên danh mục đã tồn tại</categoryNameExist>
        <addCategoryFail>Lỗi thêm danh mục</addCategoryFail>
        <editCategoryFail>Cập nhật danh mục không thành công</editCategoryFail>
        <addCategoryExistButDelete>Danh mục này đã tồn tại và hiện tại đang bị xóa bạn có muốn khôi phục</addCategoryExistButDelete>
        <deleteCategorySuccess>Xoá danh mục thành công</deleteCategorySuccess>
        <categoryNameNotValid>Danh mục không hợp lệ</categoryNameNotValid>
        <deleteCategoryFail>Xoá danh mục không thành công</deleteCategoryFail>
        <editCategorySuccess>Cập nhật tên danh mục thành công</editCategorySuccess>
        <editCategoryBlank>Không được bỏ trống tên danh mục</editCategoryBlank>
        <addProductSuccess>Thêm sản phẩm thành công</addProductSuccess>
        <productNameExist>Tên sản phẩm đã tồn tại</productNameExist>
        <notFullValid>Chưa điền đầy đủ thông tin</notFullValid>
        <findNotCategory>Không tìm thấy danh mục</findNotCategory>
        <notSelectedCategory>Vui lòng thêm danh mục</notSelectedCategory>
        <addProductFail>Tạo sản phẩm thất bại</addProductFail>
        <alertVat>Nhập VAT từ 0 đến 100</alertVat>
        <addProductExistButDeleted>Sản phẩm này đã tồn tại và hiện tại đang bị xóa bạn có muốn khôi phục</addProductExistButDeleted>
        <deleteProductSuccess>Xoá sản phẩm thành công</deleteProductSuccess>
        <deleteProductFail>Có lỗi xảy ra vui lòng thử lại sau</deleteProductFail>
        <editProductSuccess>Cập nhật thông tin sản phẩm thành công</editProductSuccess>
        <editNotFullValid>Chưa điền đầy đủ hoặc sai thông tin</editNotFullValid>
        <editProductFail>Cập nhật thông tin sản phẩm thất bại</editProductFail>
        <sendInfoSuccess>Cảm ơn bạn đã gửi thông tin. Chúng tôi sẽ liên lạc lại sau</sendInfoSuccess>
        <sendInfoFail>Gửi thông tin thất bại, không được bỏ trống</sendInfoFail>
        <addStaffSuccess>Thêm nhân viên thành công</addStaffSuccess>
        <addStaffFaill>Thêm nhân viên thất bại</addStaffFaill>
        <infoStaffExist>Số điện thoại này đã được đăng ký vui lòng đăng ký số điện thoại khác</infoStaffExist>
        <staffExistButDeleted>Thông tin nhân viên đã tồn tại nhưng hiện tại đã bị xoá bạn có muốn khôi phục</staffExistButDeleted>
        <notFullValidStaff>Không được bỏ trống thông tin</notFullValidStaff>
        <notFullValidNameStaff>Không được bỏ trống tên nhân viên</notFullValidNameStaff>
        <changeInfoStaffSuccess>Cập nhật thông tin nhân viên thành công</changeInfoStaffSuccess>
        <phoneNumberExist>Số điện thoại đã tồn tại</phoneNumberExist>
        <phoneNumberNotValid>Số điện thoại phải là 10 số</phoneNumberNotValid>
        <notFullValidAddress>Không được bỏ trống địa chỉ</notFullValidAddress>
        <changeInfoStaffFail>Có lỗi xảy ra vui lòng thử lại sau</changeInfoStaffFail>
        <deleteStaffSuccess>Xoá nhân viên thành công</deleteStaffSuccess>
        <deleteStaffFail>Nhân viên đang trực tuyến không thể xoá. Vui lòng đổi thành offline để xoá</deleteStaffFail>
        <changePasswordSuccess>Thay đổi mật khẩu nhân viên thành công</changePasswordSuccess>
        <changePasswordFail>Thay đổi mật khẩu nhân viên thất bại</changePasswordFail>
        <addTableSuccess>Thêm bàn thành công</addTableSuccess>
        <addTableExist>Bàn đã tồn tại</addTableExist>
        <addTableExistButDeleted>Bàn này đã tồn tại và hiện tại đang bị xóa bạn có muốn khôi phục lại</addTableExistButDeleted>
        <addTableFail>Thêm bàn thất bại</addTableFail>
        <cancelOrderSuccess>Huỷ bàn thành công</cancelOrderSuccess>
        <cancelOrderFail>Huỷ bàn thất bại</cancelOrderFail>
        <deleteTableSuccess>Xoá bàn thành công</deleteTableSuccess>
        <deleteTableFail>Xoá bàn thất bại</deleteTableFail>
        <deleteMutilTableSuccess>Xoá các bàn thành công</deleteMutilTableSuccess>
        <doNotChooseTableDelete>Chưa chọn bàn để xoá</doNotChooseTableDelete>
        <deleteMutilTableFail>Xoá các bàn thất bại</deleteMutilTableFail>
        <homeScreenProduct>Sản phẩm</homeScreenProduct>
        <homeScreenRevenue>Doanh thu</homeScreenRevenue>
        <homeScreenTable>Quản lý bàn</homeScreenTable>
        <homeScreenStaff>Nhân viên</homeScreenStaff>
        <homeScreenInfomation>Thông tin thêm</homeScreenInfomation>
        <homeScreenCustomers>Khách hàng</homeScreenCustomers>
        <homeScreenTaxReceipt>Hoá đơn</homeScreenTaxReceipt>
        <homeScreenWareHouse>Quản lý kho</homeScreenWareHouse>
        <homeScreenRevenueToday>Tổng doanh thu hôm nay</homeScreenRevenueToday>
        <homeScreenOrdersNumbersToday>Số đơn hàng đã thanh toán trong ngày</homeScreenOrdersNumbersToday>
        <homeScreenOrders>đơn</homeScreenOrders>
        <homeScreenBestSalesToday>Sản phẩm bán chạy nhất hôm nay</homeScreenBestSalesToday>
        <homeScreenAllOrders>Tổng số lượng</homeScreenAllOrders>
        <homeScreenHighestSalesToday>Sản phẩm có doanh thu cao nhất hôm nay</homeScreenHighestSalesToday>
        <homeScreenLogout>Đăng xuất khỏi tài khoản</homeScreenLogout>
        <homeScreenDeleteAccountText1>Bạn có chắc chắn muốn xóa tài khoản này không?</homeScreenDeleteAccountText1>
        <homeScreenDeleteAccountText2>Cảnh báo: Sau khi xóa, dữ liệu của bạn không thể khôi phục lại</homeScreenDeleteAccountText2>

        <productsScreenAddProduct> Thêm sản phẩm</productsScreenAddProduct>
        <productsScreenListCategories>Danh mục sản phẩm</productsScreenListCategories>
        <productsScreenCategoriesTitle>Danh mục sản phẩm</productsScreenCategoriesTitle>

        <productsScreenListProducts>Danh sách các sản phẩm</productsScreenListProducts>
        <productsScreenFindProduct>Tìm tên sản phẩm</productsScreenFindProduct>
        <productsScreenCategoryFind>Chưa chọn danh mục</productsScreenCategoryFind>
        <productsScreenDishType>Loại món</productsScreenDishType>
        <productsScreenMainCourse>Món chính</productsScreenMainCourse>
        <productsScreenSideDish>Món phụ</productsScreenSideDish>
        <handleProductScreenListSideDish>Danh mục món phụ</handleProductScreenListSideDish>


        <SuccessfullyAddedNewSideDish>Cập nhật thêm món phụ thành công</SuccessfullyAddedNewSideDish>
        <FailedToDddNewSideDish>Cập nhật thêm món phụ thất bại</FailedToDddNewSideDish>


        <tableScreenAddTable>Thêm bàn</tableScreenAddTable>
        <tableScreenNameTable>Tên bàn</tableScreenNameTable>
        <tableScreenDeleteTable>Bạn có chắn chắc muốn xóa bàn này không?</tableScreenDeleteTable>
        <tableScreenDeleteTables>Xoá nhiều bàn</tableScreenDeleteTables>
        <tableScreenMessDeleteTables>Bạn có chắn chắc muốn xóa các bàn này không ?</tableScreenMessDeleteTables>
        <tableScreenListTables>Danh sách bàn tại quán</tableScreenListTables>
        <tableScreenTableNumber>Bàn số</tableScreenTableNumber>
        <tableScreenNameFood>Tên món ăn</tableScreenNameFood>
        <tableScreenQuantityFood>Số lượng</tableScreenQuantityFood>
        <tableScreenPriceFood>Giá tiền</tableScreenPriceFood>
        <tableScreenCancelOrders>Huỷ đơn</tableScreenCancelOrders>
        <tableScreenExitTable>Thoát</tableScreenExitTable>
        <tableScreenWarning>Lưu ý :Những sản phẩm đã đặt sẽ bị huỷ hết</tableScreenWarning>
        <staffScreenCreateStaffs>Thêm nhân viên</staffScreenCreateStaffs>
        <staffScreenNameStaff>Tên nhân viên</staffScreenNameStaff>
        <staffScreenAddressStaff>Địa chỉ</staffScreenAddressStaff>
        <staffScreenPhoneStaff>Số điện thoại</staffScreenPhoneStaff>
        <staffScreenPasswordStaff>Mật khẩu</staffScreenPasswordStaff>
        <staffScreenPasswordConfirmStaff>Xác nhận mật khẩu</staffScreenPasswordConfirmStaff>
        <staffScreenStatusStaff>Trạng thái</staffScreenStatusStaff>
        <staffScreenStatusStaffWork>Trạng thái hoạt động</staffScreenStatusStaffWork>
        <staffScreenAddStaff>Thêm</staffScreenAddStaff>
        <staffScreenCancelStaff>Huỷ</staffScreenCancelStaff>
        <handleProductScreenTitle>Danh mục sản phẩm</handleProductScreenTitle>
        <handleProductScreenName>Tên sản phẩm</handleProductScreenName>
        <handleProductScreenVat>Thuế</handleProductScreenVat>
        <handleProductScreenPrice>Giá tiền</handleProductScreenPrice>
        <handleProductScreenAddImage>Thêm hình ảnh</handleProductScreenAddImage>
        <handleProductScreenUpdateImage>Thay đổi hình ảnh</handleProductScreenUpdateImage>
        <handleProductScreenUpdate>Cập nhật</handleProductScreenUpdate>
        <handleProductScreenDelete>Xoá</handleProductScreenDelete>
        <handleProductScreenDeleteConfirm>Xác nhận xoá sản phẩm</handleProductScreenDeleteConfirm>
        <detailStafScreenChangePass>Thay đổi mật khẩu</detailStafScreenChangePass>
        <detailStafScreenHidestatistics>Ẩn thống kê</detailStafScreenHidestatistics>
        <detailStafScreenShowstatistics>Xem thống kê</detailStafScreenShowstatistics>
        <detailStafScreenStaffNoRevenue> Nhân viên chưa có doanh thu</detailStafScreenStaffNoRevenue>
        <detailStafScreenStaffArrangeBill>Sắp xếp hoá đơn ↑↓</detailStafScreenStaffArrangeBill>
        <detailStafScreenStaffHideBillDeleted>Ẩn hoá đơn đã xoá</detailStafScreenStaffHideBillDeleted>
        <detailStafScreenStaffShowBillDeleted>Xem hoá đơn đã xoá</detailStafScreenStaffShowBillDeleted>
        <detailStafScreenStaffFindBill>Tra cứu số hoá đơn</detailStafScreenStaffFindBill>
        <detailStafScreenStaffPaidBill>Tên nhân viên thanh toán</detailStafScreenStaffPaidBill>
        <detailStafScreenStaffDeleteBill>Huỷ hoá đơn này</detailStafScreenStaffDeleteBill>
        <detailStafScreenStaffDeleteBillConfirm>Xác nhận huỷ hoá đơn</detailStafScreenStaffDeleteBillConfirm>
        <detailStafScreenStaffInvoiceStatus>Tình trạng hoá đơn</detailStafScreenStaffInvoiceStatus>
        <detailStafScreenStaffNoChange>Không có thay đổi</detailStafScreenStaffNoChange>
        <detailStafScreenStaffChanged>Đã thay đổi</detailStafScreenStaffChanged>
        <detailStafScreenStaffChangedReason>Lý do thay đổi</detailStafScreenStaffChangedReason>
        <detailStafScreenStaffPriceNoVAT>Tổng tiền chưa bao gồm VAT</detailStafScreenStaffPriceNoVAT>
        <detailStafScreenStaffPaymented>Khách trả</detailStafScreenStaffPaymented>
        <detailStafScreenStaffDetailBill>Chi tiết hoá đơn</detailStafScreenStaffDetailBill>
        <detailStafScreenStaffDayCreated>Ngày tạo</detailStafScreenStaffDayCreated>
        <detailStafScreenStaffDayDeleted>Ngày xoá</detailStafScreenStaffDayDeleted>
        <detailStafScreenStaffTotalRevenue>Tổng doanh thu</detailStafScreenStaffTotalRevenue>
        <detailStafScreenStaffUpdateInfo>Cập nhật</detailStafScreenStaffUpdateInfo>
        <detailStafScreenStaffDeleteStaff>Xoá nhân viên</detailStafScreenStaffDeleteStaff>
        <detailStafScreenStaffDeleteStaffConfirm>Xác nhận xoá nhân viên</detailStafScreenStaffDeleteStaffConfirm>
        <detailStafScreenStaffUpdate>Thay đổi</detailStafScreenStaffUpdate>
        <detailStafScreenStaffDeleteSuccess>Xoá bill thành công</detailStafScreenStaffDeleteSuccess>
        <detailStafScreenStaffDeleteFail>Xoá bill thất bại</detailStafScreenStaffDeleteFail>
        <detailFoodStartToEndFrom>Từ ngày</detailFoodStartToEndFrom>
        <detailFoodStartToEndTo>đến</detailFoodStartToEndTo>
        <detailFoodStartToEndDetailRevenue>Chi tiết doanh thu</detailFoodStartToEndDetailRevenue>
        <customersScreenTitle>Thêm khách hàng</customersScreenTitle>
        <customersScreenListCustommers>Danh sách khách hàng</customersScreenListCustommers>
        <customersScreenNewCustomer>Khách hàng mới</customersScreenNewCustomer>
        <customersScreenOldCustomer>Khách hàng cũ</customersScreenOldCustomer>
        <customersScreenPaymentMax>Điểm tích lũy nhiều</customersScreenPaymentMax>
        <customersScreenPaymentMin>Điểm tích lũy ít</customersScreenPaymentMin>
        <customersScreenFindCustomer>Tìm kiếm khách hàng</customersScreenFindCustomer>
        <customersScreenSortList>Sắp xếp danh sách</customersScreenSortList>
        <chartScreenmonday>Thứ 2</chartScreenmonday>
        <chartScreentuesday>Thứ 3</chartScreentuesday>
        <chartScreenwednesday>Thứ 4</chartScreenwednesday>
        <chartScreenthursday>Thứ 5</chartScreenthursday>
        <chartScreenfriday>Thứ 6</chartScreenfriday>
        <chartScreensaturday>Thứ 7</chartScreensaturday>
        <chartScreensunday>CN</chartScreensunday>
        <chartScreenlegendWeek>Doanh thu trong tuần</chartScreenlegendWeek>
        <chartScreenFirstWeek>Tuần 1</chartScreenFirstWeek>
        <chartScreenSecondWeek>Tuần 2</chartScreenSecondWeek>
        <chartScreenThirdWeek>Tuần 3</chartScreenThirdWeek>
        <chartScreenFourtWeek>Tuần 4</chartScreenFourtWeek>
        <chartScreenFifthWeek>Tuần 5</chartScreenFifthWeek>
        <chartScreenlegenMonth>Doanh thu trong tháng</chartScreenlegenMonth>
        <chartScreenLastMonth>Doanh thu tháng trước</chartScreenLastMonth>
        <chartScreenMessageReason>Ngày bắt đầu phải bé hơn ngày kết thúc</chartScreenMessageReason>
        <chartScreenRevenueDay>Ngày</chartScreenRevenueDay>
        <chartScreenRevenueWeek>Tuần</chartScreenRevenueWeek>
        <chartScreenRevenueMonth>Tháng</chartScreenRevenueMonth>
        <chartScreenRevenuemanagement>Quản lý doanh thu</chartScreenRevenuemanagement>
        <chartScreenDetail>Xem chi tiết</chartScreenDetail>
        <chartScreenSeeDetailCustomizableDay>Xem doanh thu theo ngày tuỳ chọn</chartScreenSeeDetailCustomizableDay>
        <chartScreenStartDay>Ngày bắt đầu</chartScreenStartDay>
        <chartScreenEndDay>Ngày kết thúc</chartScreenEndDay>
        <chartScreenSeeRevenue>Xem doanh thu</chartScreenSeeRevenue>
        <chartScreenDayRevenue>Doanh thu ngày</chartScreenDayRevenue>
        <chartScreenOther>Khác</chartScreenOther>

        <componentCustomersInfoCustomer>Thông tin khách hàng</componentCustomersInfoCustomer>
        <componentCustomersFullName>Họ và tên</componentCustomersFullName>
        <componentCustomersCreateDay>Ngày đăng ký</componentCustomersCreateDay>
        <componentCustomersTotalPrice>Tổng số tiền đã tích luỹ</componentCustomersTotalPrice>
        <componentCustomersRemainingPoints>Số điểm còn lại</componentCustomersRemainingPoints>
        <componentCustomersPoints>điểm</componentCustomersPoints>
        <componentCustomersPointsUsed>Lịch sử dùng điểm</componentCustomersPointsUsed>
        <componentCustomersPointsListPointUsed>Danh sách điểm đã dùng</componentCustomersPointsListPointUsed>
        <componentDetailPaymentId>Số hoá đơn</componentDetailPaymentId>
        <componentDetailPaymentDate>Ngày thanh toán</componentDetailPaymentDate>
        <componentDetailPaymentHour>Giờ thanh toán</componentDetailPaymentHour>
        <componentDetailPaymentNoted>Ghi chú</componentDetailPaymentNoted>
        <componentDetailPaymentNoReason>Không có lý do</componentDetailPaymentNoReason>
        <componentDetailPaymentDiscountAmount>Số tiền giảm</componentDetailPaymentDiscountAmount>
        <componentDetailPaymentUsedInStone>Khách ăn tại quán</componentDetailPaymentUsedInStone>
        <componentDetailPaymentTaskAway>Mang về</componentDetailPaymentTaskAway>
        <componentDetailPaymentListFoodUsed>Danh sách sản phẩm đã thanh toán</componentDetailPaymentListFoodUsed>
        <componentDetailPaymentName>Tên</componentDetailPaymentName>
        <componentDetailPaymentQuantity>SL</componentDetailPaymentQuantity>
        <componentDetailPaymentPrice>Giá tiền</componentDetailPaymentPrice>
        <componentDetailPaymentVAT>Thuế</componentDetailPaymentVAT>
        <componentDetailPaymentTotalPrice>Tổng tiền</componentDetailPaymentTotalPrice>
        <componentCategoriesTitle>Quản lý danh mục</componentCategoriesTitle>
        <componentCategoriesWarning>Không được bỏ trống tên danh mục</componentCategoriesWarning>
        <componentCategoriesCreateCategory>Thêm danh mục</componentCategoriesCreateCategory>
        <componentCategoriesCreate>Thêm</componentCategoriesCreate>
        <componentCategoriesWarningDelete>Lưu ý :Những sản phẩm trong danh mục này sẽ bị xoá hết</componentCategoriesWarningDelete>
        <componentCustomersWarningPhone>Số điện thoại này đã được đăng ký</componentCustomersWarningPhone>
        <componentCustomersName>Tên khách hàng</componentCustomersName>
        <componentCustomersPhone>Số điện thoại</componentCustomersPhone>
        <componentCustomersCreate>Thêm khách hàng</componentCustomersCreate>
        <componentAddImagesSuccess>Cập nhật ảnh footer thành công</componentAddImagesSuccess>
        <componentAddImagesTitle>Ảnh footer</componentAddImagesTitle>
        <componentAddImagesUpload>Tải ảnh footer</componentAddImagesUpload>
        <componentAddImagesUpdate>Cập nhật</componentAddImagesUpdate>
        <componentAddProductChooseCategoy>Chọn danh mục</componentAddProductChooseCategoy>
        <componentAddProductCategories>Danh mục trống</componentAddProductCategories>
        <componentAddProductName>Tên</componentAddProductName>
        <componentProductPrice>Giá tiền</componentProductPrice>
        <componentAddProductPriceNotVAT>(Chưa thuế)</componentAddProductPriceNotVAT>
        <componentAddProductPrice>(Gồm thuế)</componentAddProductPrice>


        <componentAddProductVat>Thuế</componentAddProductVat>
        <componentAddProductImage>Thêm hình ảnh</componentAddProductImage>
        <componentChangeProductImage>Đổi hình ảnh</componentChangeProductImage>

        <componentAddProductCreate>Thêm sản phẩm</componentAddProductCreate>
        <componentCreateFooterSuccess>Tạo footer thành công</componentCreateFooterSuccess>
        <componentExistFooter>Cửa hàng đã tồn tại footer</componentExistFooter>
        <componentFailFooter>Tạo footer thất bại</componentFailFooter>
        <componentFooterTitle>Footer máy in</componentFooterTitle>
        <componentCreateTitle>Tạo</componentCreateTitle>
        <componentCloseTitle>Đóng</componentCloseTitle>
        <componentSendInfo>Gửi thông tin liên hệ</componentSendInfo>
        <componentSendInfoContent>Nội dung cần liên lạc</componentSendInfoContent>
        <componentUpdateLogoSuccess>Cập nhật logo thành công</componentUpdateLogoSuccess>
        <componentLogoTitle>Logo</componentLogoTitle>
        <componentLogoUploadLogo>Tải ảnh logo</componentLogoUploadLogo>
        <InfoScreenDetailStore>Chi tiết cửa hàng</InfoScreenDetailStore>
        <InfoScreenNameStore>Tên cửa hàng</InfoScreenNameStore>
        <InfoScreenNameBossStore>Tên chủ cửa hàng</InfoScreenNameBossStore>
        <InfoScreenNoName>Chưa đặt tên</InfoScreenNoName>
        <InfoScreenExpired>Ngày hết hạn</InfoScreenExpired>
        <InfoScreenQtiStaff>Số lượng nhân viên</InfoScreenQtiStaff>
        <InfoScreenTraniner>Bản thử nghiệm</InfoScreenTraniner>
        <InfoScreenAddress>Địa chỉ cửa hàng</InfoScreenAddress>
        <InfoScreenContent>Nội dung footer máy in</InfoScreenContent>
        <InfoScreenUpdateFooter>Cập nhật footer máy in</InfoScreenUpdateFooter>
        <InfoScreenCreateFooter>Thêm footer máy in</InfoScreenCreateFooter>
        <InfoScreenUpdateImageFooter> Cập nhật ảnh footer máy in</InfoScreenUpdateImageFooter>
        <RestoreCategories>Khôi phục danh mục thành công</RestoreCategories>
        <CategoryExist>Tên danh mục đã tồn tại</CategoryExist>
        <RestoreTableSuccess>Khôi phục bàn thành công</RestoreTableSuccess>
        <RestoreStaffSuccess>Khôi phục nhân viên thành công</RestoreStaffSuccess>
        <RestoreProductSuccess>Khôi phục sản phẩm thành công</RestoreProductSuccess>
        <PriceOnlyInt>Giá tiền chỉ nhập số nguyên</PriceOnlyInt>
        <highestRevenue>Doanh thu cao nhất ↓</highestRevenue>
        <lowestRevenue>Doanh thu thấp nhất ↑</lowestRevenue>
        <latestInvoice>Hoá đơn mới nhất ↓</latestInvoice>
        <oldestInvoice>Hoá đơn cũ nhất ↑</oldestInvoice>
        <onlineStatus>Online</onlineStatus>
        <offlineStatus>Offline</offlineStatus>
        <stopStatus>Stop</stopStatus>
        <updateFooterSuccess>Thay đổi footer thành công</updateFooterSuccess>
        <updateFooterFail>Thay đổi footer thất bại</updateFooterFail>
        <deliveryNote>Phiếu xuất kho</deliveryNote>
        <ExportBill>Phiếu nhập kho</ExportBill>
        <ExportWarehouseQuantityFood>Số lượng</ExportWarehouseQuantityFood>
        <ProductNotCreated>Chưa tạo sản phẩm</ProductNotCreated>
        <Cash>Tiền mặt</Cash>
        <Transfer>Chuyển khoản</Transfer>
        <Bank>Thẻ ngân hàng</Bank>
        <Export>Xuất hàng</Export>
        <Return>Hoàn trả đại lý</Return>
        <CancelOutput>Huỷ hàng</CancelOutput>
        <WarehouseSuccess>Xuất kho thành công</WarehouseSuccess>
        <WarehouseReturnSuccess>Trả hàng thành công</WarehouseReturnSuccess>
        <WarehouseCancelSuccess>Huỷ hàng thành công</WarehouseCancelSuccess>
        <WarehouseExport>Số lượng sản phẩm không đủ để xuất kho</WarehouseExport>
        <WarehouseExportError>Có lỗi xẩy ra vui lòng thử lại</WarehouseExportError>
        <RepresentativeName>Tên người đại diện</RepresentativeName>
        <AgencyPhoneNumber>SĐT người đại diện</AgencyPhoneNumber>
        <AgencyName>Tên đại lý</AgencyName>
        <AgencyHaveNotSelected>Chưa chọn đại lý</AgencyHaveNotSelected>
        <AgenciesHaveNotSelected>Chưa tạo danh sách đại lý</AgenciesHaveNotSelected>
        <CodeFormInput>Mã phiếu nhập</CodeFormInput>
        <CodeFormExport>Mã phiếu xuất</CodeFormExport>
        <PriceExport>Giá xuất kho</PriceExport>
        <PriceExportCancel>Giá xuất huỷ</PriceExportCancel>
        <DiscountPrice>Giảm giá</DiscountPrice>
        <ProductNotSelected>Chưa chọn sản phẩm</ProductNotSelected>
        <TotalPrice>Tổng tiền thanh toán</TotalPrice>
        <PaymentMethod>Phương thức thanh toán</PaymentMethod>
        <NoteExport>Ghi chú</NoteExport>
        <TotalPriceProduct>Tổng tiền sản phẩm</TotalPriceProduct>
        <CreateDeliveryExport>Tạo phiếu xuất</CreateDeliveryExport>
        <CreateDeliveryBack>Tạo phiếu trả</CreateDeliveryBack>
        <CreateDeliveryCancel>Tạo phiếu huỷ</CreateDeliveryCancel>
        <CreateDeliveryImport>Tạo phiếu nhập</CreateDeliveryImport>
        <AddDeliveryExport>Thêm số tồn kho thành công</AddDeliveryExport>
        <AddDeliveryError>Có lỗi xảy ra, vui lòng thử lại sau</AddDeliveryError>
        <CancelDelivery>Huỷ</CancelDelivery>
        <DetailProduct>Chi tiết sản phẩm</DetailProduct>
        <ProductCode>Mã số sản phẩm</ProductCode>
        <ProductNameInventory>Tên sản phẩm</ProductNameInventory>
        <PriceInventory>Gía tiền</PriceInventory>
        <QuantityInventory>Số lượng tồn kho</QuantityInventory>
        <AddDelivery>Thêm tồn kho</AddDelivery>
        <QuantityDelivery>Số lượng</QuantityDelivery>
        <CreateDelivery>Tạo</CreateDelivery>
        <WarningDiscount>Số tiền giảm giá cao hơn tổng tiền</WarningDiscount>
        <AddDeliveryBillExport>Thêm phiếu tồn kho thành công</AddDeliveryBillExport>
        <PriceImport>Giá nhập</PriceImport>
        <WarehouseExportTitle>Xuất Kho</WarehouseExportTitle>
        <WarehouseFindProduct>Tìm kiếm sản phẩm</WarehouseFindProduct>
        <WarehouseTaxProduct>Thuế</WarehouseTaxProduct>
        <BillExportExist>Mã phiếu nhập đã tồn tại</BillExportExist>

        <TitleInfoAgency>Thông tin đại lý</TitleInfoAgency>
        <CompanyName>Tên công ty</CompanyName>
        <AgencyNameInfo>Tên đại lý </AgencyNameInfo>
        <AgencyTaxCode>Mã số thuế</AgencyTaxCode>
        <AgencyAddress>Địa chỉ</AgencyAddress>
        <AgencyRepresentative>Tên người đại diện</AgencyRepresentative>
        <AgencyEmail>Email</AgencyEmail>
        <AgencyPhoneNumberInfo>SĐT đại lý </AgencyPhoneNumberInfo>
        <AgencyNote>Ghi chú</AgencyNote>
        <CreateAgencySuccess>Tạo đại lý thành công</CreateAgencySuccess>
        <CreateAgencyFail>Tạo đại lý thất bại</CreateAgencyFail>
        <Surcharge>Phụ thu</Surcharge>
        <messLoginFail401>Tài khoản không có quyền đăng nhập hệ thống này</messLoginFail401>
        <messLoginFail204>Tài khoản của bạn chưa kích hoạt. Vui lòng liên hệ quản trị viên để kích hoạt!</messLoginFail204>
        <listInvoice>Danh sách hoá đơn</listInvoice>
        <enterInvoice>Nhập số hoá đơn</enterInvoice>
        <dateCreateInvoice>Ngày tạo</dateCreateInvoice>
        <totalPriceInvoice>Tổng tiền</totalPriceInvoice>
        <paymentCodeInvoice>Payment Code</paymentCodeInvoice>
        <electronicInvoiceIndividual>Cá nhân</electronicInvoiceIndividual>
        <electronicInvoiceCompany>Doanh nghiệp</electronicInvoiceCompany>
        <electronicInvoiceTaxCodeEmpty>Mã số thuế không được để trống</electronicInvoiceTaxCodeEmpty>
        <electronicInvoiceSuccess>Thành công</electronicInvoiceSuccess>
        <electronicInvoiceFail>Thất bại</electronicInvoiceFail>
        <electronicInvoiceTaxCodeValid>Mã số thuế hợp lệ</electronicInvoiceTaxCodeValid>
        <electronicInvoiceTaxCodeInvalid>Mã số thuế không tồn tại</electronicInvoiceTaxCodeInvalid>
        <electronicInvoiceSuccessRegister>Đăng ký hoá đơn điện tử thành công</electronicInvoiceSuccessRegister>
        <electronicInvoiceEmailInvalid>Email không đúng định dạng</electronicInvoiceEmailInvalid>
        <electronicInvoiceError>Đã có lỗi xảy ra</electronicInvoiceError>
        <electronicInvoiceDetail>Chi tiết hoá đơn</electronicInvoiceDetail>
        <electronicInvoiceNumber>Số hoá đơn</electronicInvoiceNumber>
        <electronicInvoiceCode>Mã hoá đơn</electronicInvoiceCode>
        <electronicInvoiceDate>Ngày thanh toán</electronicInvoiceDate>
        <electronicInvoiceTotal>Tổng tiền</electronicInvoiceTotal>
        <electronicInvoiceTaxCode>Số tiền giảm </electronicInvoiceTaxCode>
        <electronicInvoiceEnterTaxCode>Nhập mã số thuế</electronicInvoiceEnterTaxCode>
        <electronicInvoiceCustomerName>Tên khách hàng</electronicInvoiceCustomerName>
        <electronicInvoiceCompanyName>Tên công ty</electronicInvoiceCompanyName>
        <electronicInvoiceEmail>Email</electronicInvoiceEmail>
        <electronicInvoiceAddress>Địa chỉ</electronicInvoiceAddress>
        <electronicInvoiceProductName>Tên sản phẩm</electronicInvoiceProductName>
        <electronicInvoiceProductQuantity>Sl</electronicInvoiceProductQuantity>
        <electronicInvoiceProductPrice>Giá tiền</electronicInvoiceProductPrice>
        <electronicInvoiceProductVat>Vat</electronicInvoiceProductVat>
        <electronicInvoiceRegister>Đăng ký hoá đơn điện tử</electronicInvoiceRegister>
        <electronicInvoiceListProduct>Danh sách sản phẩm trong hoá đơn</electronicInvoiceListProduct>
    </Viet>

    <Eng>
        <titleApp>Sales</titleApp>
        <qlbh>Management Software</qlbh>
        <nameApp>Delta POS</nameApp>
        <phone>Phone Number</phone>
        <pass>Password</pass>
        <login>Login</login>
        <agree>Yes</agree>
        <cancel>Cancel</cancel>
        <registry>Registering a business partnership</registry>
        <contact>Contact us</contact>
        <phoneContact>Phone :***********</phoneContact>
        <email>Email :<EMAIL></email>
        <minValidMessageNumberPhone>The phone number provided is too short</minValidMessageNumberPhone>
        <maxValidMessageNumberPhone>The phone number provided is too long</maxValidMessageNumberPhone>
        <fieldIsBlank>You must not leave this field blank</fieldIsBlank>
        <matchesNumberPhone>Please enter a valid phone number format</matchesNumberPhone>
        <minValidMessagePassword>The password is too short</minValidMessagePassword>
        <confirmPasswordMessage>The authentication password must match the password</confirmPasswordMessage>
        <minValidMessageName>Full name must be more than 2 characters</minValidMessageName>
        <maxValidMessageName>Full name must be less than 40 characters</maxValidMessageName>
        <minValidMessageAddress>Address must be more than 4 characters</minValidMessageAddress>
        <maxValidMessageAddress>Address must be less than 40 characters</maxValidMessageAddress>
        <minValidMessagePSendInfo>The contact content is too short</minValidMessagePSendInfo>
        <maxValidMessagePSendInfo>The contact content is too less</maxValidMessagePSendInfo>
        <send>Send</send>
        <currencySymbol>$</currencySymbol>
        <backToPage>You want to go back to the previous page</backToPage>
        <close>Close</close>
        <tokenExpiresed>Your login session has expired. Please log in again</tokenExpiresed>
        <messageApp>Message</messageApp>
        <createCustomersFail>Failed to send contact information</createCustomersFail>
        <createCustomersSuccess>Successfully added the customer</createCustomersSuccess>
        <createCustomersNumberPhoneExist>The customer's phone number already exists</createCustomersNumberPhoneExist>
        <createCustomerNameFillInBlank>Please enter the customer's name</createCustomerNameFillInBlank>
        <vietVangPhoneNumber>***********</vietVangPhoneNumber>
        <networkIsError>An error has occurred</networkIsError>
        <upLoadImgFail>An error occurred. Please try again later</upLoadImgFail>
        <loginFail>Wrong password or account</loginFail>
        <fillInBlank>Cannot be left blank</fillInBlank>
        <networkError>No network connection. Please log in again later</networkError>
        <messageDisable>Successfully sent the request to delete the admin account. You have been logged out</messageDisable>
        <addCategorySuccess>Successfully added the category</addCategorySuccess>
        <fillInBlankCategory>Category name not provided</fillInBlankCategory>
        <categoryNameExist>Category already exists"</categoryNameExist>
        <addCategoryFail>Error adding the category</addCategoryFail>
        <editCategoryFail>Failed to update the category</editCategoryFail>
        <addCategoryExistButDelete>This category already exists and is currently being deleted. Do you want to restore it</addCategoryExistButDelete>
        <deleteCategorySuccess>Successfully deleted the category</deleteCategorySuccess>
        <categoryNameNotValid>Invalid category</categoryNameNotValid>
        <deleteCategoryFail>Failed to delete the category</deleteCategoryFail>
        <editCategorySuccess>Successfully updated the category</editCategorySuccess>
        <editCategoryBlank>Category name cannot be left blank</editCategoryBlank>
        <addProductSuccess>Successfully added the product</addProductSuccess>
        <productNameExist>The product name already exists</productNameExist>
        <notFullValid>Not all information has been filled in</notFullValid>
        <findNotCategory>Category not found</findNotCategory>
        <notSelectedCategory>Please add a category</notSelectedCategory>
        <addProductFail>Failed to create the product</addProductFail>
        <alertVat>Enter VAT from 0 to 100</alertVat>
        <addProductExistButDeleted>This product already exists and is currently being deleted. Do you want to restore it?</addProductExistButDeleted>
        <deleteProductSuccess>Successfully deleted the product</deleteProductSuccess>
        <deleteProductFail>An error occurred. Please try again later</deleteProductFail>
        <editProductSuccess>Successfully updated the product information</editProductSuccess>
        <editNotFullValid>Not fully filled in or incorrect information</editNotFullValid>
        <editProductFail>Failed to update the product information</editProductFail>
        <sendInfoSuccess>Thank you for submitting the information. We will contact you later</sendInfoSuccess>
        <sendInfoFail>Failed to send information, please do not leave it blank</sendInfoFail>
        <addStaffSuccess>Successfully added the employee</addStaffSuccess>
        <addStaffFaill>Failed to add the employee</addStaffFaill>
        <infoStaffExist>This phone number has already been registered. Please register with a different phone number</infoStaffExist>
        <staffExistButDeleted>The employee information already exists but is currently deleted. Do you want to restore it?</staffExistButDeleted>
        <notFullValidStaff>"Information must not be left blank</notFullValidStaff>
        <notFullValidNameStaff>Employee name must not be left blank</notFullValidNameStaff>
        <changeInfoStaffSuccess>Successfully updated employee information</changeInfoStaffSuccess>
        <phoneNumberExist>The phone number already exists</phoneNumberExist>
        <phoneNumberNotValid>The phone number must be 10 digits</phoneNumberNotValid>
        <notFullValidAddress>The address must not be left blank</notFullValidAddress>
        <changeInfoStaffFail>An error occurred. Please try again later</changeInfoStaffFail>
        <deleteStaffSuccess>Successfully deleted the employee</deleteStaffSuccess>
        <deleteStaffFail>The employee is currently online and cannot be deleted. Please change their status to offline before deleting</deleteStaffFail>
        <changePasswordSuccess>Successfully changed the employee's password</changePasswordSuccess>
        <changePasswordFail>Failed to change the employee's password</changePasswordFail>
        <addTableSuccess>Successfully added the table</addTableSuccess>
        <addTableExist>The table already exists</addTableExist>
        <addTableExistButDeleted>This table already exists and is currently being deleted. Do you want to restore it?</addTableExistButDeleted>
        <addTableFail>Failed to add the table</addTableFail>
        <cancelOrderSuccess>Successfully canceled the table</cancelOrderSuccess>
        <cancelOrderFail>Failed to cancel the table</cancelOrderFail>
        <deleteTableSuccess>Successfully deleted the table</deleteTableSuccess>
        <deleteTableFail>Failed to delete the table</deleteTableFail>
        <deleteMutilTableSuccess>Successfully deleted the tables</deleteMutilTableSuccess>
        <doNotChooseTableDelete>No tables selected for deletion</doNotChooseTableDelete>
        <deleteMutilTableFail>Failed to delete the tables</deleteMutilTableFail>

        <maxValidMessagePassword>The password is too long</maxValidMessagePassword>
        <homeScreenProduct>Product</homeScreenProduct>
        <homeScreenRevenue>Revenue</homeScreenRevenue>
        <homeScreenTable>Table</homeScreenTable>
        <homeScreenStaff>Staff</homeScreenStaff>
        <homeScreenInfomation>Infomation</homeScreenInfomation>
        <homeScreenCustomers>Customers</homeScreenCustomers>
        <homeScreenTaxReceipt>Receipt</homeScreenTaxReceipt>

        <homeScreenWareHouse>Warehouse</homeScreenWareHouse>

        <homeScreenLogout>Logout from the account</homeScreenLogout>
        <homeScreenRevenueToday>The total revenue today</homeScreenRevenueToday>
        <homeScreenOrdersNumbersToday>The orders that have been paid today</homeScreenOrdersNumbersToday>
        <homeScreenOrders>Orders</homeScreenOrders>
        <homeScreenBestSalesToday>The best-selling product today</homeScreenBestSalesToday>
        <homeScreenAllOrders>The total quantity</homeScreenAllOrders>
        <homeScreenHighestSalesToday>The product with the highest revenue today</homeScreenHighestSalesToday>
        <homeScreenDeleteAccountText1>Confirmation to delete the account from the Delta Pos system.</homeScreenDeleteAccountText1>
        <homeScreenDeleteAccountText2>Note :After deletion, your data cannot be recovered</homeScreenDeleteAccountText2>

        <productsScreenAddProduct>Add Products</productsScreenAddProduct>
        <productsScreenListCategories>List Categories</productsScreenListCategories>
        <productsScreenCategoriesTitle>List Categories</productsScreenCategoriesTitle>

        <productsScreenListProducts>List Products</productsScreenListProducts>
        <productsScreenFindProduct>Find Products</productsScreenFindProduct>
        <productsScreenCategoryFind>Please select a category</productsScreenCategoryFind>
        <productsScreenDishType>DishType</productsScreenDishType>
        <productsScreenMainCourse>MainCourse</productsScreenMainCourse>
        <productsScreenSideDish>SideDish</productsScreenSideDish>
        <handleProductScreenListSideDish>Side Dish Categories</handleProductScreenListSideDish>

        <SuccessfullyAddedNewSideDish>Successfully added a new side dish.</SuccessfullyAddedNewSideDish>
        <FailedToDddNewSideDish>Failed to add a new side dish</FailedToDddNewSideDish>

        <tableScreenAddTable>Create Table</tableScreenAddTable>
        <tableScreenNameTable>Table name</tableScreenNameTable>
        
        <tableScreenDeleteTable>Delete Table</tableScreenDeleteTable>
        <tableScreenDeleteTables>Delete Tables</tableScreenDeleteTables>
        <tableScreenMessDeleteTables>Are you sure you want to delete the selected tables?</tableScreenMessDeleteTables>
        <tableScreenListTables>List Tables</tableScreenListTables>
        <tableScreenTableNumber>Table</tableScreenTableNumber>
        <tableScreenNameFood>Food Name</tableScreenNameFood>
        <tableScreenQuantityFood>Quantity</tableScreenQuantityFood>
        <tableScreenPriceFood>Price</tableScreenPriceFood>
        <tableScreenCancelOrders>Cancel Orders</tableScreenCancelOrders>
        <tableScreenExitTable>Exit</tableScreenExitTable>
        <tableScreenWarning>Note :All reserved products will be canceled</tableScreenWarning>

        <staffScreenCreateStaffs>Create Staffs</staffScreenCreateStaffs>
        <staffScreenNameStaff>Staff name</staffScreenNameStaff>
        <staffScreenAddressStaff>Address</staffScreenAddressStaff>
        <staffScreenPhoneStaff>Phone</staffScreenPhoneStaff>
        <staffScreenPasswordStaff>Password</staffScreenPasswordStaff>
        <staffScreenPasswordConfirmStaff>Password Confirm</staffScreenPasswordConfirmStaff>
        <staffScreenStatusStaff>Status</staffScreenStatusStaff>
        <staffScreenStatusStaffWork>Status</staffScreenStatusStaffWork>
        <staffScreenAddStaff>Create</staffScreenAddStaff>
        <staffScreenCancelStaff>Cancel</staffScreenCancelStaff>

        <handleProductScreenTitle>List Categories</handleProductScreenTitle>
        <handleProductScreenName>Product name</handleProductScreenName>
        <handleProductScreenVat>Vat</handleProductScreenVat>
        <handleProductScreenPrice>Price</handleProductScreenPrice>
        <handleProductScreenAddImage>Upload Image</handleProductScreenAddImage>
        <handleProductScreenUpdateImage>Change Image</handleProductScreenUpdateImage>
        <handleProductScreenUpdate>Update</handleProductScreenUpdate>
        <handleProductScreenDelete>Delete</handleProductScreenDelete>
        <handleProductScreenDeleteConfirm>Confirm product deletion</handleProductScreenDeleteConfirm>

        <detailFoodStartToEndFrom>From</detailFoodStartToEndFrom>
        <detailFoodStartToEndTo>to</detailFoodStartToEndTo>
        <detailFoodStartToEndDetailRevenue>Detailed Revenue</detailFoodStartToEndDetailRevenue>

        <detailStafScreenChangePass>Change Password</detailStafScreenChangePass>
        <detailStafScreenHidestatistics>Hide Statistics</detailStafScreenHidestatistics>
        <detailStafScreenShowstatistics>Show Statistics</detailStafScreenShowstatistics>
        <detailStafScreenStaffNoRevenue>Employee with no sales</detailStafScreenStaffNoRevenue>
        <detailStafScreenStaffArrangeBill>Arrange Bill ↑↓</detailStafScreenStaffArrangeBill>
        <detailStafScreenStaffHideBillDeleted>Hide Bill Deleted</detailStafScreenStaffHideBillDeleted>
        <detailStafScreenStaffShowBillDeleted>Show Bill Deleted</detailStafScreenStaffShowBillDeleted>
        <detailStafScreenStaffFindBill>Find Bill</detailStafScreenStaffFindBill>
        <detailStafScreenStaffPaidBill>Cashier's name</detailStafScreenStaffPaidBill>
        <detailStafScreenStaffDeleteBill>Delete Bill</detailStafScreenStaffDeleteBill>
        <detailStafScreenStaffDeleteBillConfirm>Confirm Delete Bill</detailStafScreenStaffDeleteBillConfirm>
        <detailStafScreenStaffInvoiceStatus>Invoice Status</detailStafScreenStaffInvoiceStatus>
        <detailStafScreenStaffNoChange>Don't change</detailStafScreenStaffNoChange>
        <detailStafScreenStaffChanged>Changed</detailStafScreenStaffChanged>
        <detailStafScreenStaffChangedReason>Change Reason</detailStafScreenStaffChangedReason>
        <detailStafScreenStaffPriceNoVAT>Total amount before VAT</detailStafScreenStaffPriceNoVAT>
        <detailStafScreenStaffPaymented>Customer payment</detailStafScreenStaffPaymented>
        <detailStafScreenStaffDetailBill>Bill Details</detailStafScreenStaffDetailBill>
        <detailStafScreenStaffDayCreated>Create day</detailStafScreenStaffDayCreated>
        <detailStafScreenStaffDayDeleted>Delete day</detailStafScreenStaffDayDeleted>
        <detailStafScreenStaffTotalRevenue>Total revenue</detailStafScreenStaffTotalRevenue>
        <detailStafScreenStaffUpdateInfo>Update Infomation</detailStafScreenStaffUpdateInfo>
        <detailStafScreenStaffDeleteStaff>Delete</detailStafScreenStaffDeleteStaff>
        <detailStafScreenStaffDeleteStaffConfirm>Confirm Delete</detailStafScreenStaffDeleteStaffConfirm>
        <detailStafScreenStaffUpdate>Change</detailStafScreenStaffUpdate>
        <detailStafScreenStaffDeleteSuccess>Delete Success</detailStafScreenStaffDeleteSuccess>
        <detailStafScreenStaffDeleteFail>Delete Fail</detailStafScreenStaffDeleteFail>

        <customersScreenTitle>Create Customer</customersScreenTitle>
        <customersScreenListCustommers>List Customers</customersScreenListCustommers>
        <customersScreenNewCustomer>New Customer</customersScreenNewCustomer>
        <customersScreenOldCustomer>Old Customer</customersScreenOldCustomer>
        <customersScreenPaymentMax>Highest points</customersScreenPaymentMax>
        <customersScreenPaymentMin>Lowest points</customersScreenPaymentMin>
        <customersScreenFindCustomer>Find Customer</customersScreenFindCustomer>
        <customersScreenSortList>Sort the list</customersScreenSortList>

        <chartScreenmonday>Mon</chartScreenmonday>
        <chartScreentuesday>Tues</chartScreentuesday>
        <chartScreenwednesday>Wed</chartScreenwednesday>
        <chartScreenthursday>Thurs</chartScreenthursday>
        <chartScreenfriday>Fri</chartScreenfriday>
        <chartScreensaturday>Sat</chartScreensaturday>
        <chartScreensunday>Sun</chartScreensunday>
        <chartScreenlegendWeek>Revenue Week</chartScreenlegendWeek>

        <chartScreenFirstWeek>Week 1</chartScreenFirstWeek>
        <chartScreenSecondWeek>Week 2</chartScreenSecondWeek>
        <chartScreenThirdWeek>Week 3</chartScreenThirdWeek>
        <chartScreenFourtWeek>Week 4</chartScreenFourtWeek>
        <chartScreenFifthWeek>Week 5</chartScreenFifthWeek>
        <chartScreenlegenMonth>Revenue in month</chartScreenlegenMonth>
        <chartScreenLastMonth>Revenue last month</chartScreenLastMonth>


        <chartScreenMessageReason>The start date must be earlier than the end date</chartScreenMessageReason>
        <chartScreenRevenueDay>Day</chartScreenRevenueDay>
        <chartScreenRevenueWeek>Week</chartScreenRevenueWeek>
        <chartScreenRevenueMonth>Month</chartScreenRevenueMonth>

        <chartScreenRevenuemanagement>Revenue management</chartScreenRevenuemanagement>
        <chartScreenDetail>Detail</chartScreenDetail>
        <chartScreenSeeDetailCustomizableDay>View revenue by selected date</chartScreenSeeDetailCustomizableDay>
        <chartScreenStartDay>Start day</chartScreenStartDay>
        <chartScreenEndDay>End day</chartScreenEndDay>
        <chartScreenSeeRevenue>See Revenue</chartScreenSeeRevenue>
        <chartScreenDayRevenue>Revenue Day</chartScreenDayRevenue>
        <chartScreenOther>Other</chartScreenOther>

        <componentCustomersInfoCustomer>Customer Informations</componentCustomersInfoCustomer>
        <componentCustomersFullName>Full Name</componentCustomersFullName>
        <componentCustomersPhone>Phone Number</componentCustomersPhone>
        <componentCustomersCreateDay>Registration date</componentCustomersCreateDay>
        <componentCustomersTotalPrice>Total accumulated amount</componentCustomersTotalPrice>
        <componentCustomersRemainingPoints>Remaining points</componentCustomersRemainingPoints>
        <componentCustomersPoints>Point</componentCustomersPoints>
        <componentCustomersPointsUsed>Points usage history</componentCustomersPointsUsed>
        <componentCustomersPointsListPointUsed>List of points used</componentCustomersPointsListPointUsed>

        <componentDetailPaymentId>Id Payment</componentDetailPaymentId>
        <componentDetailPaymentDate>Payment date</componentDetailPaymentDate>
        <componentDetailPaymentHour>Payment hour</componentDetailPaymentHour>
        <componentDetailPaymentNoted>Note</componentDetailPaymentNoted>
        <componentDetailPaymentNoReason>No reason</componentDetailPaymentNoReason>
        <componentDetailPaymentDiscountAmount>Discount amount</componentDetailPaymentDiscountAmount>
        <componentDetailPaymentUsedInStone>Dine-in customers</componentDetailPaymentUsedInStone>
        <componentDetailPaymentTaskAway>Take away</componentDetailPaymentTaskAway>
        <componentDetailPaymentListFoodUsed>List of paid products</componentDetailPaymentListFoodUsed>
        <componentDetailPaymentName>Name</componentDetailPaymentName>
        <componentDetailPaymentQuantity>Qti</componentDetailPaymentQuantity>
        <componentDetailPaymentPrice>Price</componentDetailPaymentPrice>
        <componentDetailPaymentVAT>Vat</componentDetailPaymentVAT>
        <componentDetailPaymentTotalPrice>Total Price</componentDetailPaymentTotalPrice>

        <componentCategoriesTitle>List Categories</componentCategoriesTitle>
        <componentCategoriesWarning>Category name cannot be left blank</componentCategoriesWarning>
        <componentCategoriesCreateCategory>Create Categories</componentCategoriesCreateCategory>
        <componentCategoriesCreate>Create</componentCategoriesCreate>
        <componentCategoriesWarningDelete>Warning :All products in this category will be deleted.</componentCategoriesWarningDelete>

        <componentCustomersWarningPhone>This phone number has already been registered</componentCustomersWarningPhone>
        <componentCustomersName>Customer name</componentCustomersName>
        <componentCustomersCreate>Create</componentCustomersCreate>

        <componentAddImagesSuccess>Update Image Success</componentAddImagesSuccess>
        <componentAddImagesTitle>Image Footer</componentAddImagesTitle>
        <componentAddImagesUpload>Upload Image</componentAddImagesUpload>
        <componentAddImagesUpdate>Update</componentAddImagesUpdate>

        <componentAddProductChooseCategoy>Choose Category</componentAddProductChooseCategoy>
        <componentAddProductCategories>Blank Category</componentAddProductCategories>
        <componentAddProductName>Name</componentAddProductName>
        <componentProductPrice>Price</componentProductPrice>
        <componentAddProductPrice>(VAT)</componentAddProductPrice>
        <componentAddProductPriceNotVAT>(Not VAT)</componentAddProductPriceNotVAT>

        <componentAddProductVat>Tax</componentAddProductVat>
        <componentAddProductImage>Upload Image</componentAddProductImage>
        <componentChangeProductImage>Change Image</componentChangeProductImage>
        <componentAddProductCreate>Create Product</componentAddProductCreate>

        <componentCreateFooterSuccess>Create Footer Success</componentCreateFooterSuccess>
        <componentExistFooter>The store has a footer</componentExistFooter>
        <componentFailFooter>Create Footer Fail</componentFailFooter>
        <componentFooterTitle>Print Footer</componentFooterTitle>
        <componentCreateTitle>Create</componentCreateTitle>
        <componentCloseTitle>Close</componentCloseTitle>
        <componentSendInfo>Send info</componentSendInfo>
        <componentSendInfoContent>Content Info</componentSendInfoContent>
        <componentUpdateLogoSuccess>Upload Logo Success</componentUpdateLogoSuccess>
        <componentLogoTitle>Logo</componentLogoTitle>
        <componentLogoUploadLogo>Upload Logo</componentLogoUploadLogo>

        <InfoScreenDetailStore>Store Details</InfoScreenDetailStore>
        <InfoScreenNameStore>Store name</InfoScreenNameStore>
        <InfoScreenNameBossStore>Store owner's name</InfoScreenNameBossStore>
        <InfoScreenNoName>Not yet named</InfoScreenNoName>
        <InfoScreenExpired>Expiration date</InfoScreenExpired>  
        <InfoScreenQtiStaff>Number of employees</InfoScreenQtiStaff>
        <InfoScreenTraniner>Trial version</InfoScreenTraniner>
        <InfoScreenAddress>Store address</InfoScreenAddress>
        <InfoScreenContent>Content for footer printer</InfoScreenContent>

        <InfoScreenUpdateFooter>Update printer footer</InfoScreenUpdateFooter>
        <InfoScreenCreateFooter>Add printer footer</InfoScreenCreateFooter>
        <InfoScreenUpdateImageFooter>Update printer footer image</InfoScreenUpdateImageFooter>

        <RestoreCategories>Restore category success</RestoreCategories>
        <CategoryExist>Category exist</CategoryExist>
        <RestoreTableSuccess>Restore table success</RestoreTableSuccess>
        <RestoreStaffSuccess>Restore staff success</RestoreStaffSuccess>
        <RestoreProductSuccess>Restore product success</RestoreProductSuccess>
        <PriceOnlyInt>Product price only Int</PriceOnlyInt>

        <highestRevenue>Highest revenue ↓</highestRevenue>
        <lowestRevenue>Lowest revenue ↑</lowestRevenue>
        <latestInvoice>Lates invoice ↓</latestInvoice>
        <oldestInvoice>Oldest invoice ↑</oldestInvoice>

        <onlineStatus>Online</onlineStatus>
        <offlineStatus>Offline</offlineStatus>
        <stopStatus>Stop</stopStatus>

        <updateFooterSuccess>Update footer success</updateFooterSuccess>
        <updateFooterFail>Update footer fail</updateFooterFail>

        <deliveryNote>Delivery bill</deliveryNote>
        <ExportBill>Receipt</ExportBill>

        <ExportWarehouseQuantityFood>Quantity</ExportWarehouseQuantityFood>
        <ProductNotCreated>Haven't created a product yet</ProductNotCreated>
        <Cash>Cash</Cash>
        <Transfer>Transfer</Transfer>
        <Bank>Bank card</Bank>
        <Export>Product delivery</Export>
        <Return>Refund to dealer</Return>
        <CancelOutput>Product delivery</CancelOutput>
        <WarehouseSuccess>Exported successfully</WarehouseSuccess>
        <WarehouseReturnSuccess>Returned goods successfully</WarehouseReturnSuccess>
        <WarehouseCancelSuccess>Cancellation successful</WarehouseCancelSuccess>
        <WarehouseExport>The quantity of products is not enough to export</WarehouseExport>
        <WarehouseExportError>An error occurred, please try again</WarehouseExportError>

        <RepresentativeName>Representative name</RepresentativeName>
        <AgencyPhoneNumber>Phone number of representative</AgencyPhoneNumber>
        <AgencyName>Agent name</AgencyName>
        <AgencyHaveNotSelected>Haven't selected an agent yet</AgencyHaveNotSelected>
        <AgenciesHaveNotSelected>No agent list created yet</AgenciesHaveNotSelected>

        <CodeFormInput>Bill code entered</CodeFormInput>
        <CodeFormExport>Bill code export</CodeFormExport>

        <PriceExport>Ex-warehouse price</PriceExport>
        <PriceExportCancel>Cancellation price</PriceExportCancel>
        <DiscountPrice>Discount</DiscountPrice>
        <ProductNotSelected>Haven't selected a product yet</ProductNotSelected>
        <TotalPrice>Total payment</TotalPrice>
        <PaymentMethod>Payment methods</PaymentMethod>
        <NoteExport>Note</NoteExport>
        <TotalPriceProduct>Total product cost</TotalPriceProduct>
        <CreateDeliveryExport>Create export ticket</CreateDeliveryExport>
        <CreateDeliveryBack>Create payment slip</CreateDeliveryBack>
        <CreateDeliveryCancel>Generate Slip</CreateDeliveryCancel>

        <CreateDeliveryImport>Create Goods Receipt</CreateDeliveryImport>
        <AddDeliveryExport>Add inventory successfully</AddDeliveryExport>
        <AddDeliveryError>An error occurred, please try again later</AddDeliveryError>
        <CancelDelivery>Cancel</CancelDelivery>

        <DetailProduct>Product details</DetailProduct>
        <ProductCode>Product code</ProductCode>
        <ProductNameInventory>Product's name</ProductNameInventory>
        <PriceInventory>Price</PriceInventory>
        <QuantityInventory>Inventory number</QuantityInventory>
        <AddDelivery>Add inventory</AddDelivery>
        <QuantityDelivery>Quantity</QuantityDelivery>
        <CreateDelivery>Create</CreateDelivery>

        <WarningDiscount>The discount amount is higher than the total amount</WarningDiscount>
        <AddDeliveryBillExport>Add inventory successfully</AddDeliveryBillExport>
        <PriceImport>Import price</PriceImport>
        <WarehouseExportTitle>Create goods Issue</WarehouseExportTitle>
        <WarehouseFindProduct>Search product</WarehouseFindProduct>
        <WarehouseTaxProduct>Tax</WarehouseTaxProduct>
        <BillExportExist>The bill code already exists</BillExportExist>

        <TitleInfoAgency>Agency Information</TitleInfoAgency>
        <CompanyName>Company Name</CompanyName>
        <AgencyNameInfo>Agency Name</AgencyName>
        <AgencyTaxCode>Tax Code</AgencyTaxCode>
        <AgencyAddress>Address</AgencyAddress>s
        <AgencyRepresentative>Representative Name</AgencyRepresentative>
        <AgencyEmail>Email</AgencyEmail>
        <AgencyPhoneNumberInfo>Phone Number</AgencyPhoneNumberInfo>
        <AgencyNote>Note</AgencyNote>
        <CreateAgencySuccess>Agency Creation Successful</CreateAgencySuccess>
        <CreateAgencyFail>Agency Creation Failed</CreateAgencyFail>
        <Surcharge>Surcharge</Surcharge>
        <messLoginFail401>This account does not have permission to access this this.</messLoginFail401>
        <messLoginFail204>Your account is not activated. Please contact administrator to activate!</messLoginFail204>
        <listInvoice>Bill Lists</listInvoice>
        <enterInvoice>Enter bill number</enterInvoice>
        <dateCreateInvoice>Creation Date</dateCreateInvoice>
        <totalPriceInvoice>Total amount</totalPriceInvoice>
        <paymentCodeInvoice>Payment Code</paymentCodeInvoice>
        <electronicInvoiceIndividual>Individual</electronicInvoiceIndividual>
        <electronicInvoiceCompany>Business</electronicInvoiceCompany>
        <electronicInvoiceTaxCodeEmpty>Tax code cannot be empty</electronicInvoiceTaxCodeEmpty>
        <electronicInvoiceSuccess>Success</electronicInvoiceSuccess>
        <electronicInvoiceFail>Failed</electronicInvoiceFail>
        <electronicInvoiceTaxCodeValid>Tax code is valid</electronicInvoiceTaxCodeValid>
        <electronicInvoiceTaxCodeInvalid>Tax code does not exist</electronicInvoiceTaxCodeInvalid>
        <electronicInvoiceSuccessRegister>Register electronic invoice successfully</electronicInvoiceSuccessRegister>
        <electronicInvoiceEmailInvalid>Email is not valid</electronicInvoiceEmailInvalid>
        <electronicInvoiceError>An error occurred</electronicInvoiceError>
        <electronicInvoiceDetail>Bill Details</electronicInvoiceDetail>
        <electronicInvoiceNumber>Bill number</electronicInvoiceNumber>
        <electronicInvoiceCode>Payment code</electronicInvoiceCode>
        <electronicInvoiceDate>Payment date</electronicInvoiceDate>
        <electronicInvoiceTotal>Total</electronicInvoiceTotal>
        <electronicInvoiceTaxCode>Discount</electronicInvoiceTaxCode>
        <electronicInvoiceEnterTaxCode>Enter tax code</electronicInvoiceEnterTaxCode>
        <electronicInvoiceCustomerName>Customer name</electronicInvoiceCustomerName>
        <electronicInvoiceCompanyName>Company name</electronicInvoiceCompanyName>
        <electronicInvoiceEmail>Email</electronicInvoiceEmail>
        <electronicInvoiceAddress>Address</electronicInvoiceAddress>
        <electronicInvoiceProductName>Product name</electronicInvoiceProductName>
        <electronicInvoiceProductQuantity>Qti</electronicInvoiceProductQuantity>
        <electronicInvoiceProductPrice>Price</electronicInvoiceProductPrice>
        <electronicInvoiceProductVat>Vat</electronicInvoiceProductVat>
        <electronicInvoiceRegister>Register for Electronic Invoice</electronicInvoiceRegister>
        <electronicInvoiceListProduct>List product in invoice</electronicInvoiceListProduct>


    </Eng>

    <Jap>
        <titleApp>販売管理</titleApp>
        <qlbh>システム</qlbh>
        <nameApp>Delta POS</nameApp>
        <phone>電話番号</phone>
        <pass>パスワード</pass>
        <login>ログインする</login>
        <registry>新規登録する</registry>
        <agree>OK</agree>
        <cancel>キャンセル</cancel>
        <contact>連絡先情報</contact>
        <phoneContact>電話番号 :***********</phoneContact>
        <email>メール :<EMAIL></email>
        <minValidMessageNumberPhone>必須</minValidMessageNumberPhone>
        <maxValidMessageNumberPhone>電話番号が長すぎま</maxValidMessageNumberPhone>
        <fieldIsBlank>必須</fieldIsBlank>
        <matchesNumberPhone>正しい電話番号を入力してください</matchesNumberPhone>
        <minValidMessagePassword>※半角英数字6文字以上</minValidMessagePassword>
        <maxValidMessagePassword>必須項目を正しく入力して下さい</maxValidMessagePassword>
        <confirmPasswordMessage>パスワードが一致しません</confirmPasswordMessage>
        <minValidMessageName>2文字以上の氏名</minValidMessageName>
        <maxValidMessageName>姓名が 40 文字未満</maxValidMessageName>
        <minValidMessageAddress>住所は4文字以上である必要があります</minValidMessageAddress>
        <maxValidMessageAddress>住所は40文字以下である必要があります</maxValidMessageAddress>
        <minValidMessagePSendInfo>通信内容が短すぎる</minValidMessagePSendInfo>
        <maxValidMessagePSendInfo>通信内容が長すぎる</maxValidMessagePSendInfo>
        <send>送信</send>
        <currencySymbol>¥</currencySymbol>
        <backToPage>前のページに戻りたいですか？</backToPage>
        <close>閉</close>
        <tokenExpiresed>ログインセッションが期限切れです。再度ログインしてください</tokenExpiresed>
        <messageApp>メッセージ</messageApp>
        <createCustomersFail>連絡先情報の送信に失敗しました</createCustomersFail>
        <createCustomersSuccess>新しいお客様を追加しました</createCustomersSuccess>
        <createCustomersNumberPhoneExist>顧客の電話番号は既に存在しています</createCustomersNumberPhoneExist>
        <createCustomerNameFillInBlank>顧客の名前を入力してください</createCustomerNameFillInBlank>
        <vietVangPhoneNumber>***********</vietVangPhoneNumber>
        <networkIsError>エラーが発生しました</networkIsError>
        <upLoadImgFail>エラーが発生しました。後でもう一度試してください</upLoadImgFail>
        <loginFail>電話番号またはパスワードが誤っています</loginFail>
        <fillInBlank>必須項目を正しく入力してください</fillInBlank>
        <networkError>接続が制限されています。もう一度ログインして下さい</networkError>
        <messageDisable>アカウントはシステムから削除されました</messageDisable>
        <addCategorySuccess>商品カテゴリーが作成されました</addCategorySuccess>
        <fillInBlankCategory>カテゴリ名が入力されていません</fillInBlankCategory>
        <categoryNameExist>この商品カテゴリー名は既に存在しています。他の商品カテゴリー名を入力してください</categoryNameExist>
        <addCategoryFail>商品カテゴリーを作成する際にエラーが発生しました</addCategoryFail>
        <editCategoryFail>カタログの更新に失敗しました</editCategoryFail>
        <addCategoryExistButDelete>このカテゴリーは既に存在していましたが、削除されたため、復元しますか。</addCategoryExistButDelete>
        <deleteCategorySuccess>削除しました</deleteCategorySuccess>
        <categoryNameNotValid>無効なカテゴリ</categoryNameNotValid>
        <deleteCategoryFail>ディレクトリの削除に失敗しました</deleteCategoryFail>
        <editCategorySuccess>商品カテゴリーが編集されました</editCategorySuccess>
        <editCategoryBlank>商品カテゴリー名を入力してください</editCategoryBlank>
        <addProductSuccess>商品が作成されました</addProductSuccess>
        <productNameExist>商品名は既に存在しています。他の商品名を入力してください</productNameExist>
        <notFullValid>不完全な情報</notFullValid>
        <findNotCategory>カテゴリが見つかりません</findNotCategory>
        <notSelectedCategory>カテゴリが見つかりません</notSelectedCategory>
        <addProductFail>製品の作成に失敗しました</addProductFail>
        <alertVat>付加価値税を 0 から 100 まで入力して下さい</alertVat>
        <addProductExistButDeleted>この商品は以前に削除されましたが、情報を回復しませんか？</addProductExistButDeleted>
        <deleteProductSuccess>商品が削除されました</deleteProductSuccess>
        <deleteProductFail>エラーが発生しました。しばらくしてからもう一度お試し下さい</deleteProductFail>
        <editProductSuccess>商品カテゴリーが更新されました</editProductSuccess>
        <editNotFullValid>産品情報が不完全または間違っています</editNotFullValid>
        <editProductFail>産品情報の更新に失敗しました</editProductFail>
        <sendInfoSuccess>情報を送信いただきありがとうございます。後日ご連絡いたします</sendInfoSuccess>
        <sendInfoFail>情報の送信に失敗しました。未入力の項目があります</sendInfoFail>
        <addStaffSuccess>スタッフを登録しました</addStaffSuccess>
        <addStaffFaill>登録出来ませんでした。再度お試しください</addStaffFaill>
        <infoStaffExist>電話番号が既に存在しています</infoStaffExist>
        <staffExistButDeleted>このスタッフは以前に削除されましたが、情報を回復しませんか？</staffExistButDeleted>
        <notFullValidStaff>情報を入力して下さい</notFullValidStaff>
        <notFullValidNameStaff>従業員名は空白にしてはいけません</notFullValidNameStaff>
        <changeInfoStaffSuccess>従業員情報が更新されました</changeInfoStaffSuccess>
        <phoneNumberExist>その電話番号はすでに存在しています</phoneNumberExist>
        <phoneNumberNotValid>電話番号は10桁でなければなりません</phoneNumberNotValid>
        <notFullValidAddress>住所は空白にしてはいけません</notFullValidAddress>
        <changeInfoStaffFail>エラーが発生しました。後でもう一度お試し下さい</changeInfoStaffFail>
        <deleteStaffSuccess>スタッフを削除しました</deleteStaffSuccess>
        <deleteStaffFail>オンライン中の従業員は削除できません。削除するために、オフライン状態に変更してください。</deleteStaffFail>
        <changePasswordSuccess>パスワードを変更しました</changePasswordSuccess>
        <changePasswordFail>更新が出来ませんでした。再度お試してください</changePasswordFail>
        <addTableSuccess>テーブルを作成しました</addTableSuccess>
        <addTableExist>テーブルは既に存在しています</addTableExist>
        <addTableExistButDeleted>このテーブルは既に存在していましたが、削除されたため、復元しますか。</addTableExistButDeleted>
        <addTableFail>テーブルの追加に失敗しました</addTableFail>
        <cancelOrderSuccess>テーブルが削除されました</cancelOrderSuccess>
        <cancelOrderFail>テーブルのキャンセルに失敗しました</cancelOrderFail>
        <deleteTableSuccess>テーブルを削除しました</deleteTableSuccess>
        <deleteTableFail>テーブルの削除に失敗しました</deleteTableFail>
        <deleteMutilTableSuccess>テーブルを削除しました</deleteMutilTableSuccess>
        <doNotChooseTableDelete>テーブルが選択されていません</doNotChooseTableDelete>
        <deleteMutilTableFail>非常に削除できません</deleteMutilTableFail>

        <homeScreenProduct>商品</homeScreenProduct>
        <homeScreenRevenue>売上</homeScreenRevenue>
        <homeScreenTable>テーブル</homeScreenTable>
        <homeScreenStaff>スタッフ</homeScreenStaff>
        <homeScreenInfomation>設定</homeScreenInfomation>
        <homeScreenCustomers>顧客</homeScreenCustomers>
        <homeScreenTaxReceipt>請求書</homeScreenTaxReceipt>
        <homeScreenWareHouse>倉庫管理</homeScreenWareHouse>
        <homeScreenLogout>このアカウントからログアウトして、よろしいでしょうか？</homeScreenLogout>
        <homeScreenRevenueToday> 今日の総売上</homeScreenRevenueToday>
        <homeScreenOrdersNumbersToday>今日の会計済み注文数</homeScreenOrdersNumbersToday>
        <homeScreenOrders>注文</homeScreenOrders>
        <homeScreenBestSalesToday>今日のベストセラー商品</homeScreenBestSalesToday>
        <homeScreenAllOrders>総数量</homeScreenAllOrders>
        <homeScreenHighestSalesToday>今日の最高売上商品</homeScreenHighestSalesToday>
        <homeScreenDeleteAccountText1>このアカウントを削除して、よろしいでしょうか？</homeScreenDeleteAccountText1>
        <homeScreenDeleteAccountText2>＊警告：削除した後、アカウント情報を回復することができません</homeScreenDeleteAccountText2>

        <productsScreenAddProduct>商品作成</productsScreenAddProduct>
        <productsScreenListCategories>カテゴリー管理</productsScreenListCategories>
        <productsScreenCategoriesTitle>カテゴリー</productsScreenCategoriesTitle>

        <productsScreenListProducts>商品一覧</productsScreenListProducts>
        <productsScreenFindProduct>商品名を検索する</productsScreenFindProduct>
        <productsScreenCategoryFind>カテゴリを選択してください</productsScreenCategoryFind>
        <productsScreenDishType>料理種類</productsScreenDishType>
        <productsScreenMainCourse>主菜</productsScreenMainCourse>
        <productsScreenSideDish>副菜</productsScreenSideDish>
        <handleProductScreenListSideDish>副菜のカテゴリ</handleProductScreenListSideDish>

        <SuccessfullyAddedNewSideDish>新しい副菜の追加に成功</SuccessfullyAddedNewSideDish>
        <FailedToDddNewSideDish>新しい副菜の追加に失敗</FailedToDddNewSideDish>

        <tableScreenAddTable>テーブル作成</tableScreenAddTable>
        <tableScreenNameTable>テーブル名</tableScreenNameTable>
        <tableScreenDeleteTable>このテーブルを削除して、よろしいでしょうか？</tableScreenDeleteTable>
        <tableScreenDeleteTables>複数削除</tableScreenDeleteTables>
        <tableScreenMessDeleteTables>複数のテーブルを削除して、よろしいでしょうか？</tableScreenMessDeleteTables>

        <tableScreenListTables>テーブル一覧</tableScreenListTables>
        <tableScreenTableNumber>テーブル詳細</tableScreenTableNumber>
        <tableScreenNameFood>料理名</tableScreenNameFood>
        <tableScreenQuantityFood>数量</tableScreenQuantityFood>
        <tableScreenPriceFood>価格</tableScreenPriceFood>
        <tableScreenCancelOrders>注文を削除する</tableScreenCancelOrders>
        <tableScreenExitTable>キャンセル</tableScreenExitTable>
        <tableScreenWarning> 注意：すべての注文された商品は削除されます。</tableScreenWarning>

        <staffScreenCreateStaffs>新規スタッフ登録</staffScreenCreateStaffs>
        <staffScreenNameStaff>スタッフ名</staffScreenNameStaff>
        <staffScreenAddressStaff>住所</staffScreenAddressStaff>
        <staffScreenPhoneStaff>電話番号</staffScreenPhoneStaff>
        <staffScreenPasswordStaff>新しいパスワード</staffScreenPasswordStaff>
        <staffScreenPasswordConfirmStaff>新しいパスワード(確認)</staffScreenPasswordConfirmStaff>
        <staffScreenStatusStaff>ステータス</staffScreenStatusStaff>
        <staffScreenStatusStaffWork>ステータス</staffScreenStatusStaffWork>
        <staffScreenAddStaff>登録する</staffScreenAddStaff>
        <staffScreenCancelStaff>キャンセル</staffScreenCancelStaff>

        <handleProductScreenTitle>カテゴリー</handleProductScreenTitle>
        <handleProductScreenName>商品名</handleProductScreenName>
        <handleProductScreenVat>税率</handleProductScreenVat>
        <handleProductScreenPrice>単価（税抜）</handleProductScreenPrice>
        <handleProductScreenAddImage>画像アップロード</handleProductScreenAddImage>
        <handleProductScreenUpdateImage>画像を変更する</handleProductScreenUpdateImage>

        <handleProductScreenUpdate>更新する</handleProductScreenUpdate>
        <handleProductScreenDelete>商品を削除する</handleProductScreenDelete>
        <handleProductScreenDeleteConfirm>本当に商品を削除しますか。</handleProductScreenDeleteConfirm>

        <detailStafScreenChangePass>パスワード変更</detailStafScreenChangePass>
        <detailStafScreenHidestatistics>統計を非表示にする</detailStafScreenHidestatistics>
        <detailStafScreenShowstatistics>統計を表示する</detailStafScreenShowstatistics>
        <detailStafScreenStaffNoRevenue>売上なし</detailStafScreenStaffNoRevenue>
        <detailStafScreenStaffArrangeBill>領収書の整理 ↑↓</detailStafScreenStaffArrangeBill>
        <detailStafScreenStaffHideBillDeleted>削除された領収書を非表示にする</detailStafScreenStaffHideBillDeleted>
        <detailStafScreenStaffShowBillDeleted>削除された領収書を表示する</detailStafScreenStaffShowBillDeleted>
        <detailStafScreenStaffFindBill>領収書番号を検索する</detailStafScreenStaffFindBill>
        <detailStafScreenStaffPaidBill>支払いを行ったスタッフ名</detailStafScreenStaffPaidBill>
        <detailStafScreenStaffDeleteBill>領収書を削除する</detailStafScreenStaffDeleteBill>
        <detailStafScreenStaffDeleteBillConfirm>この領収書を削除してもよろしいでしょうか？</detailStafScreenStaffDeleteBillConfirm>
        <detailStafScreenStaffInvoiceStatus>領収書の状態</detailStafScreenStaffInvoiceStatus>
        <detailStafScreenStaffNoChange>変更なし</detailStafScreenStaffNoChange>
        <detailStafScreenStaffChanged>変更されました</detailStafScreenStaffChanged>
        <detailStafScreenStaffChangedReason>変更の理由</detailStafScreenStaffChangedReason>
        <detailStafScreenStaffPriceNoVAT>税抜き総額</detailStafScreenStaffPriceNoVAT>
        <detailStafScreenStaffPaymented>顧客の支払い</detailStafScreenStaffPaymented>
        <detailStafScreenStaffDetailBill>詳細な領収書</detailStafScreenStaffDetailBill>
        <detailStafScreenStaffDayCreated>作成日</detailStafScreenStaffDayCreated>
        <detailStafScreenStaffDayDeleted>削除日</detailStafScreenStaffDayDeleted>
        <detailStafScreenStaffTotalRevenue>総売上高</detailStafScreenStaffTotalRevenue>
        <detailStafScreenStaffUpdateInfo>更新する</detailStafScreenStaffUpdateInfo>
        <detailStafScreenStaffDeleteStaff>スタッフを削除する</detailStafScreenStaffDeleteStaff>
        <detailStafScreenStaffDeleteStaffConfirm>このスタッフを削除して、よろしいでしょうか？</detailStafScreenStaffDeleteStaffConfirm>
        <detailStafScreenStaffUpdate>更新する</detailStafScreenStaffUpdate>
        <detailStafScreenStaffDeleteSuccess>領収書を削除しました</detailStafScreenStaffDeleteSuccess>
        <detailStafScreenStaffDeleteFail>削除失敗</detailStafScreenStaffDeleteFail>

        <customersScreenTitle>顧客登録</customersScreenTitle>
        <customersScreenListCustommers>顧客一覧</customersScreenListCustommers>
        <customersScreenNewCustomer>新規顧客</customersScreenNewCustomer>
        <customersScreenOldCustomer>既存顧客</customersScreenOldCustomer>
        <customersScreenPaymentMax>最大ポイント</customersScreenPaymentMax>
        <customersScreenPaymentMin>最少ポイント</customersScreenPaymentMin>
        <customersScreenFindCustomer>顧客を検索する</customersScreenFindCustomer>
        <customersScreenSortList>顧客を絞り込む</customersScreenSortList>

        <chartScreenmonday>月曜日</chartScreenmonday>
        <chartScreentuesday>火曜日</chartScreentuesday>
        <chartScreenwednesday>水曜日</chartScreenwednesday>
        <chartScreenthursday>木曜日</chartScreenthursday>
        <chartScreenfriday>金曜日</chartScreenfriday>
        <chartScreensaturday>土曜日</chartScreensaturday>
        <chartScreensunday>日曜日</chartScreensunday>
        <chartScreenlegendWeek>週間売上</chartScreenlegendWeek>

        <chartScreenFirstWeek>第1週</chartScreenFirstWeek>
        <chartScreenSecondWeek>第2週</chartScreenSecondWeek>
        <chartScreenThirdWeek>第3週</chartScreenThirdWeek>
        <chartScreenFourtWeek>第4週</chartScreenFourtWeek>
        <chartScreenFifthWeek>第5週</chartScreenFifthWeek>
        <chartScreenlegenMonth>月間売上</chartScreenlegenMonth>
        <chartScreenLastMonth>先月売上</chartScreenLastMonth>

        <chartScreenMessageReason>開始日は終了日よりも前でなければなりません</chartScreenMessageReason>
        <chartScreenRevenueDay>日次売上</chartScreenRevenueDay>
        <chartScreenRevenueWeek>週次売上</chartScreenRevenueWeek>
        <chartScreenRevenueMonth>月次売上</chartScreenRevenueMonth>

        <chartScreenRevenuemanagement>売上管理 </chartScreenRevenuemanagement>
        <chartScreenDetail>売上詳細</chartScreenDetail>
        <chartScreenSeeDetailCustomizableDay>日付を選んでください</chartScreenSeeDetailCustomizableDay>
        <chartScreenStartDay>開始日</chartScreenStartDay>
        <chartScreenEndDay>終了日</chartScreenEndDay>
        <chartScreenSeeRevenue>売上表示</chartScreenSeeRevenue>
        <chartScreenDayRevenue>日利益</chartScreenDayRevenue>
        <chartScreenOther>その他</chartScreenOther>

        <detailFoodStartToEndFrom>開始日</detailFoodStartToEndFrom>
        <detailFoodStartToEndTo>終了日</detailFoodStartToEndTo>
        <detailFoodStartToEndDetailRevenue>利益詳細</detailFoodStartToEndDetailRevenue>

        <componentCustomersInfoCustomer>顧客の詳細情報</componentCustomersInfoCustomer>
        <componentCustomersFullName>氏名</componentCustomersFullName>
        <componentCustomersPhone>電話番号</componentCustomersPhone>
        <componentCustomersCreateDay>登録日</componentCustomersCreateDay>
        <componentCustomersTotalPrice>支払総額</componentCustomersTotalPrice>
        <componentCustomersRemainingPoints>残りポイント</componentCustomersRemainingPoints>
        <componentCustomersPoints>ポイント</componentCustomersPoints>
        <componentCustomersPointsUsed> ポイントの利用履歴 </componentCustomersPointsUsed>
        <componentCustomersPointsListPointUsed>利用済みポイントの一覧</componentCustomersPointsListPointUsed>

        <componentDetailPaymentId>領収書番号</componentDetailPaymentId>
        <componentDetailPaymentDate>支払日</componentDetailPaymentDate>
        <componentDetailPaymentHour>支払い時間</componentDetailPaymentHour>
        <componentDetailPaymentNoted>メモ</componentDetailPaymentNoted>
        <componentDetailPaymentNoReason>理由なし</componentDetailPaymentNoReason>
        <componentDetailPaymentDiscountAmount>割引額</componentDetailPaymentDiscountAmount>
        <componentDetailPaymentUsedInStone>店内飲食客</componentDetailPaymentUsedInStone>
        <componentDetailPaymentTaskAway>持ち帰り</componentDetailPaymentTaskAway>
        <componentDetailPaymentListFoodUsed>支払い済み商品一覧</componentDetailPaymentListFoodUsed>
        <componentDetailPaymentName>料理名</componentDetailPaymentName>
        <componentDetailPaymentQuantity>数量</componentDetailPaymentQuantity>
        <componentDetailPaymentPrice>単価（税抜）</componentDetailPaymentPrice>
        <componentDetailPaymentVAT>税</componentDetailPaymentVAT>
        <componentDetailPaymentTotalPrice>総額</componentDetailPaymentTotalPrice>

        <componentCategoriesTitle>カテゴリー管理</componentCategoriesTitle>
        <componentCategoriesWarning>カテゴリ名を空白にすることはできません</componentCategoriesWarning>
        <componentCategoriesCreateCategory>商品カテゴリー名</componentCategoriesCreateCategory>
        <componentCategoriesCreate>作成する</componentCategoriesCreate>
        <componentCategoriesWarningDelete>注意: このカテゴリー内のすべての商品が削除されます</componentCategoriesWarningDelete>

        <componentCustomersWarningPhone>この電話番号はすでに登録されています</componentCustomersWarningPhone>
        <componentCustomersName>お客様名を入力してください</componentCustomersName>
        <componentCustomersCreate>登録する</componentCustomersCreate>

        <componentAddImagesSuccess>フッター画像がアップロードされました</componentAddImagesSuccess>
        <componentAddImagesTitle>フッター画像</componentAddImagesTitle>
        <componentAddImagesUpload>フッター画像をアップロードする</componentAddImagesUpload>
        <componentAddImagesUpdate>更新する</componentAddImagesUpdate>

        <componentAddProductChooseCategoy>商品カテゴリーを選ぶ</componentAddProductChooseCategoy>
        <componentAddProductCategories>空のカテゴリー</componentAddProductCategories>
        <componentAddProductName>商品名</componentAddProductName>
        <componentProductPrice>単価</componentProductPrice>

        <componentAddProductPrice>（税込）</componentAddProductPrice>
        <componentAddProductPriceNotVAT>(税抜)</componentAddProductPriceNotVAT>

        <componentAddProductVat>税</componentAddProductVat>
        <componentAddProductImage>画像をアップロードする</componentAddProductImage>
        <componentChangeProductImage>画像を変更する</componentChangeProductImage>
        
        <componentAddProductCreate>商品を作成する</componentAddProductCreate>

        <componentCreateFooterSuccess>フッターの作成に成功しました</componentCreateFooterSuccess>
        <componentExistFooter>店舗にフッターがあります</componentExistFooter>
        <componentFailFooter>フッターの作成に失敗しました</componentFailFooter>
        <componentFooterTitle>フッターを印刷する</componentFooterTitle>
        <componentCreateTitle>作成する</componentCreateTitle>
        <componentCloseTitle>閉</componentCloseTitle>

        <componentSendInfo>お問い合わせ</componentSendInfo>
        <componentSendInfoContent>お問い合わせの詳細</componentSendInfoContent>

        <componentUpdateLogoSuccess>ロゴを更新しました</componentUpdateLogoSuccess>
        <componentLogoTitle>店舗ロゴの変更</componentLogoTitle>
        <componentLogoUploadLogo>ロゴをアップロードする</componentLogoUploadLogo>

        <InfoScreenDetailStore>店舗の詳細情報</InfoScreenDetailStore>
        <InfoScreenNameStore>店舗名</InfoScreenNameStore>
        <InfoScreenNameBossStore>店主名</InfoScreenNameBossStore>
        <InfoScreenNoName>まだ名前がありません</InfoScreenNoName>
        <InfoScreenExpired>有効期限</InfoScreenExpired>
        <InfoScreenQtiStaff>従業員数</InfoScreenQtiStaff>
        <InfoScreenTraniner>体験版</InfoScreenTraniner>
        <InfoScreenAddress>店舗の住所</InfoScreenAddress>
        <InfoScreenContent>プリンターのフッター内容</InfoScreenContent>

        <InfoScreenUpdateFooter>プリンターのフッターを更新する</InfoScreenUpdateFooter>
        <InfoScreenCreateFooter>プリンターのフッターを追加する</InfoScreenCreateFooter>
        <InfoScreenUpdateImageFooter>プリンターのフッター画像を更新する</InfoScreenUpdateImageFooter>

        <RestoreCategories>商品カテゴリーの復元に成功しました</RestoreCategories>
        <CategoryExist>カテゴリが存在します</CategoryExist>
        <RestoreTableSuccess>テーブルの復元に成功しました</RestoreTableSuccess>
        <RestoreStaffSuccess>スタッフの復元に成功しました</RestoreStaffSuccess>
        <RestoreProductSuccess>商品の復元に成功しました</RestoreProductSuccess>
        <PriceOnlyInt>製品価格は整数のみです</PriceOnlyInt>

        <highestRevenue>最高売上高 ↑</highestRevenue>
        <lowestRevenue>最低売上高 ↓</lowestRevenue>
        <latestInvoice>最新の領収書 ↑</latestInvoice>
        <oldestInvoice>最古の領収書 ↓</oldestInvoice>

        <onlineStatus>オンライン</onlineStatus>
        <offlineStatus>オフライン</offlineStatus>
        <stopStatus>ストップ</stopStatus>

        <updateFooterSuccess>フッターを更新しました</updateFooterSuccess>
        <updateFooterFail>フッターの更新に失敗しました</updateFooterFail>

        <deliveryNote>出庫伝票</deliveryNote>
        <ExportBill>入庫伝票</ExportBill>

        <ExportWarehouseQuantityFood>数量</ExportWarehouseQuantityFood>
        <ProductNotCreated>まだ製品を作成していません</ProductNotCreated>
        <Cash>現金</Cash>
        <Transfer>振込</Transfer>
        <Bank>銀行カード</Bank>
        <Export>出荷</Export>
        <Return>代理店への返品</Return>
        <CancelOutput>キャンセル</CancelOutput>
        <WarehouseSuccess>伝票を作成しました</WarehouseSuccess>
        <WarehouseReturnSuccess>無事に商品が返品されました</WarehouseReturnSuccess>
        <WarehouseCancelSuccess>キャンセルが成功しました</WarehouseCancelSuccess>
        <WarehouseExport>製品の量が輸出するには十分ではない</WarehouseExport>
        <WarehouseExportError>エラーが発生しました。もう一度やり直してください</WarehouseExportError>

        <RepresentativeName>代表者名</RepresentativeName>
        <AgencyPhoneNumber>代表者の電話番号</AgencyPhoneNumber>
        <AgencyName>代理店名</AgencyName>
        <AgencyHaveNotSelected>代理店が選択されていません</AgencyHaveNotSelected>
        <AgenciesHaveNotSelected>エージェントリストはまだ作成されていません</AgenciesHaveNotSelected>

        <CodeFormInput>入庫伝票番号</CodeFormInput>
        <CodeFormExport>出庫伝票番号</CodeFormExport>
        <PriceExport>出庫価格</PriceExport>
        <PriceExportCancel>キャンセル料金</PriceExportCancel>
        <DiscountPrice>値引き</DiscountPrice>
        <ProductNotSelected>商品が選択されていません</ProductNotSelected>
        <TotalPrice>合計金額</TotalPrice>
        <PaymentMethod>支払方法</PaymentMethod>
        <NoteExport>備考</NoteExport>
        <TotalPriceProduct>製品総コスト</TotalPriceProduct>
        <CreateDeliveryExport>伝票を作成する</CreateDeliveryExport>
        <CreateDeliveryBack>払込票を作成する</CreateDeliveryBack>
        <CreateDeliveryCancel>キャンセル伝票を作成する</CreateDeliveryCancel>

        <CreateDeliveryImport>入庫伝票の作成</CreateDeliveryImport>
        <AddDeliveryExport>在庫を追加しました</AddDeliveryExport>
        <AddDeliveryError>エラーが発生しました。後でもう一度お試しください</AddDeliveryError>
        <CancelDelivery>キャンセル</CancelDelivery>

        <DetailProduct>製品詳細</DetailProduct>
        <ProductCode>製品コード</ProductCode>
        <ProductNameInventory>商品名</ProductNameInventory>
        <PriceInventory>価格</PriceInventory>
        <QuantityInventory>在庫数</QuantityInventory>
        <AddDelivery>在庫を追加する</AddDelivery>
        <QuantityDelivery>数量</QuantityDelivery>
        <CreateDelivery>作成</CreateDelivery>

        <WarningDiscount>割引額が合計金額より大きい</WarningDiscount>
        <AddDeliveryBillExport>在庫を正常に追加しました</AddDeliveryBillExport>
        <PriceImport>仕入れ価格</PriceImport>
        <WarehouseExportTitle>出庫</WarehouseExportTitle>
        <WarehouseFindProduct>商品の検索</WarehouseFindProduct>
        <WarehouseTaxProduct>税</WarehouseTaxProduct>
        <BillExportExist>バウチャーコードはすでに存在</BillExportExist>

        <TitleInfoAgency>代理店情報</TitleInfoAgency>
        <CompanyName>会社名</CompanyName>
        <AgencyNameInfo>代理店名</AgencyName>
        <AgencyTaxCode>税番号</AgencyTaxCode>
        <AgencyAddress>住所</AgencyAddress>
        <AgencyRepresentative>代表者名</AgencyRepresentative>
        <AgencyEmail>メールアドレス</AgencyEmail>
        <AgencyPhoneNumberInfo>代表者の電話番号</AgencyPhoneNumberInfo>
        <AgencyNote>備考</AgencyNote>
        <CreateAgencySuccess>代理店を作成しました</CreateAgencySuccess>
        <CreateAgencyFail>代理店の作成に失敗しました</CreateAgencyFail>
        <Surcharge>割増</Surcharge>
        <messLoginFail401>このアカウントはシステムにログイン権限がありません。</messLoginFail401>
        <messLoginFail204>このアカウントは有効化されていません。管理者に連絡して有効化を依頼してください。
        <listInvoice>Bill Lists</listInvoice>
        <enterInvoice>Enter bill number</enterInvoice>
        <dateCreateInvoice>Creation Date</dateCreateInvoice>
        <totalPriceInvoice>Total amount</totalPriceInvoice>
        <paymentCodeInvoice>Payment Code</paymentCodeInvoice>
        <electronicInvoiceIndividual>Individual</electronicInvoiceIndividual>
        <electronicInvoiceCompany>Business</electronicInvoiceCompany>
        <electronicInvoiceTaxCodeEmpty>Tax code cannot be empty</electronicInvoiceTaxCodeEmpty>
        <electronicInvoiceSuccess>Success</electronicInvoiceSuccess>
        <electronicInvoiceFail>Failed</electronicInvoiceFail>
        <electronicInvoiceTaxCodeValid>Tax code is valid</electronicInvoiceTaxCodeValid>
        <electronicInvoiceTaxCodeInvalid>Tax code does not exist</electronicInvoiceTaxCodeInvalid>
        <electronicInvoiceSuccessRegister>Register electronic invoice successfully</electronicInvoiceSuccessRegister>
        <electronicInvoiceEmailInvalid>Email is not valid</electronicInvoiceEmailInvalid>
        <electronicInvoiceError>An error occurred</electronicInvoiceError>
        <electronicInvoiceDetail>Bill Details</electronicInvoiceDetail>
        <electronicInvoiceNumber>Bill number</electronicInvoiceNumber>
        <electronicInvoiceCode>Payment code</electronicInvoiceCode>
        <electronicInvoiceDate>Payment date</electronicInvoiceDate>
        <electronicInvoiceTotal>Total</electronicInvoiceTotal>
        <electronicInvoiceTaxCode>Discount</electronicInvoiceTaxCode>
        <electronicInvoiceEnterTaxCode>Enter tax code</electronicInvoiceEnterTaxCode>
        <electronicInvoiceCustomerName>Customer name</electronicInvoiceCustomerName>
        <electronicInvoiceCompanyName>Company name</electronicInvoiceCompanyName>
        <electronicInvoiceEmail>Email</electronicInvoiceEmail>
        <electronicInvoiceAddress>Address</electronicInvoiceAddress>
        <electronicInvoiceProductName>Product name</electronicInvoiceProductName>
        <electronicInvoiceProductQuantity>Qti</electronicInvoiceProductQuantity>
        <electronicInvoiceProductPrice>Price</electronicInvoiceProductPrice>
        <electronicInvoiceProductVat>Vat</electronicInvoiceProductVat>
        <electronicInvoiceRegister>Register for Electronic Invoice</electronicInvoiceRegister>
        <electronicInvoiceListProduct>Product lists</electronicInvoiceListProduct>

    </Jap>

</lang>
