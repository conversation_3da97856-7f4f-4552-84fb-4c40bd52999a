PODS:
  - BEMCheckBox (1.4.1)
  - boost (1.76.0)
  - CocoaAsyncSocket (7.6.5)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.72.4)
  - FBReactNativeSpec (0.72.4):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.72.4)
    - RCTTypeSafety (= 0.72.4)
    - React-Core (= 0.72.4)
    - React-jsi (= 0.72.4)
    - ReactCommon/turbomodule/core (= 0.72.4)
  - fmt (6.2.1)
  - glog (0.3.5)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCTRequired (0.72.4)
  - RCTTypeSafety (0.72.4):
    - FBLazyVector (= 0.72.4)
    - RCTRequired (= 0.72.4)
    - React-Core (= 0.72.4)
  - React (0.72.4):
    - React-Core (= 0.72.4)
    - React-Core/DevSupport (= 0.72.4)
    - React-Core/RCTWebSocket (= 0.72.4)
    - React-RCTActionSheet (= 0.72.4)
    - React-RCTAnimation (= 0.72.4)
    - React-RCTBlob (= 0.72.4)
    - React-RCTImage (= 0.72.4)
    - React-RCTLinking (= 0.72.4)
    - React-RCTNetwork (= 0.72.4)
    - React-RCTSettings (= 0.72.4)
    - React-RCTText (= 0.72.4)
    - React-RCTVibration (= 0.72.4)
  - React-callinvoker (0.72.4)
  - React-Codegen (0.72.4):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.72.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.4)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.72.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.72.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.72.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.4)
    - React-Core/RCTWebSocket (= 0.72.4)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.72.4)
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.72.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.72.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.72.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.72.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.72.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.72.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.72.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.72.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.72.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.72.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.4)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.72.4):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.4)
    - React-Codegen (= 0.72.4)
    - React-Core/CoreModulesHeaders (= 0.72.4)
    - React-jsi (= 0.72.4)
    - React-RCTBlob
    - React-RCTImage (= 0.72.4)
    - ReactCommon/turbomodule/core (= 0.72.4)
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.72.4):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.4)
    - React-debug (= 0.72.4)
    - React-jsi (= 0.72.4)
    - React-jsinspector (= 0.72.4)
    - React-logger (= 0.72.4)
    - React-perflogger (= 0.72.4)
    - React-runtimeexecutor (= 0.72.4)
  - React-debug (0.72.4)
  - React-jsc (0.72.4):
    - React-jsc/Fabric (= 0.72.4)
    - React-jsi (= 0.72.4)
  - React-jsc/Fabric (0.72.4):
    - React-jsi (= 0.72.4)
  - React-jsi (0.72.4):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.72.4):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.72.4)
    - React-jsi (= 0.72.4)
    - React-perflogger (= 0.72.4)
  - React-jsinspector (0.72.4)
  - React-logger (0.72.4):
    - glog
  - react-native-blob-util (0.17.3):
    - React-Core
  - react-native-date-picker (4.4.2):
    - React-Core
  - react-native-document-picker (8.2.2):
    - React-Core
  - react-native-image-picker (5.7.0):
    - React-Core
  - react-native-mail (6.1.1):
    - React-Core
  - react-native-netinfo (9.5.0):
    - React-Core
  - react-native-network-info (5.2.1):
    - React
  - react-native-ping (1.2.8):
    - React
  - react-native-safe-area-context (4.10.8):
    - React-Core
  - react-native-tcp-socket (6.3.0):
    - CocoaAsyncSocket
    - React-Core
  - React-NativeModulesApple (0.72.4):
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.72.4)
  - React-RCTActionSheet (0.72.4):
    - React-Core/RCTActionSheetHeaders (= 0.72.4)
  - React-RCTAnimation (0.72.4):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.4)
    - React-Codegen (= 0.72.4)
    - React-Core/RCTAnimationHeaders (= 0.72.4)
    - React-jsi (= 0.72.4)
    - ReactCommon/turbomodule/core (= 0.72.4)
  - React-RCTAppDelegate (0.72.4):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-jsc
    - React-NativeModulesApple
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.72.4):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.4)
    - React-Core/RCTBlobHeaders (= 0.72.4)
    - React-Core/RCTWebSocket (= 0.72.4)
    - React-jsi (= 0.72.4)
    - React-RCTNetwork (= 0.72.4)
    - ReactCommon/turbomodule/core (= 0.72.4)
  - React-RCTImage (0.72.4):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.4)
    - React-Codegen (= 0.72.4)
    - React-Core/RCTImageHeaders (= 0.72.4)
    - React-jsi (= 0.72.4)
    - React-RCTNetwork (= 0.72.4)
    - ReactCommon/turbomodule/core (= 0.72.4)
  - React-RCTLinking (0.72.4):
    - React-Codegen (= 0.72.4)
    - React-Core/RCTLinkingHeaders (= 0.72.4)
    - React-jsi (= 0.72.4)
    - ReactCommon/turbomodule/core (= 0.72.4)
  - React-RCTNetwork (0.72.4):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.4)
    - React-Codegen (= 0.72.4)
    - React-Core/RCTNetworkHeaders (= 0.72.4)
    - React-jsi (= 0.72.4)
    - ReactCommon/turbomodule/core (= 0.72.4)
  - React-RCTSettings (0.72.4):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.4)
    - React-Codegen (= 0.72.4)
    - React-Core/RCTSettingsHeaders (= 0.72.4)
    - React-jsi (= 0.72.4)
    - ReactCommon/turbomodule/core (= 0.72.4)
  - React-RCTText (0.72.4):
    - React-Core/RCTTextHeaders (= 0.72.4)
  - React-RCTVibration (0.72.4):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.4)
    - React-Core/RCTVibrationHeaders (= 0.72.4)
    - React-jsi (= 0.72.4)
    - ReactCommon/turbomodule/core (= 0.72.4)
  - React-rncore (0.72.4)
  - React-runtimeexecutor (0.72.4):
    - React-jsi (= 0.72.4)
  - React-runtimescheduler (0.72.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker
    - React-debug
    - React-jsi
    - React-runtimeexecutor
  - React-utils (0.72.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-debug
  - ReactCommon/turbomodule/bridging (0.72.4):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.4)
    - React-cxxreact (= 0.72.4)
    - React-jsi (= 0.72.4)
    - React-logger (= 0.72.4)
    - React-perflogger (= 0.72.4)
  - ReactCommon/turbomodule/core (0.72.4):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.4)
    - React-cxxreact (= 0.72.4)
    - React-jsi (= 0.72.4)
    - React-logger (= 0.72.4)
    - React-perflogger (= 0.72.4)
  - RNCAsyncStorage (1.23.1):
    - React-Core
  - RNCCheckbox (0.5.17):
    - BEMCheckBox (~> 1.4)
    - React-Core
  - RNDeviceInfo (11.1.0):
    - React-Core
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.25.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNNotifee (7.8.2):
    - React-Core
    - RNNotifee/NotifeeCore (= 7.8.2)
  - RNNotifee/NotifeeCore (7.8.2):
    - React-Core
  - RNReanimated (3.5.4):
    - DoubleConversion
    - FBLazyVector
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-callinvoker
    - React-Core
    - React-Core/DevSupport
    - React-Core/RCTWebSocket
    - React-CoreModules
    - React-cxxreact
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-RCTActionSheet
    - React-RCTAnimation
    - React-RCTAppDelegate
    - React-RCTBlob
    - React-RCTImage
    - React-RCTLinking
    - React-RCTNetwork
    - React-RCTSettings
    - React-RCTText
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.32.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - React-RCTImage
  - RNSVG (12.4.3):
    - React-Core
  - SocketRocket (0.6.1)
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - GTMSessionFetcher
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-jsc (from `../node_modules/react-native/ReactCommon/jsc`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-blob-util (from `../node_modules/react-native-blob-util`)
  - react-native-date-picker (from `../node_modules/react-native-date-picker`)
  - react-native-document-picker (from `../node_modules/react-native-document-picker`)
  - react-native-image-picker (from `../node_modules/react-native-image-picker`)
  - react-native-mail (from `../node_modules/react-native-mail`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-network-info (from `../node_modules/react-native-network-info`)
  - react-native-ping (from `../node_modules/react-native-ping`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-tcp-socket (from `../node_modules/react-native-tcp-socket`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCCheckbox (from `../node_modules/@react-native-community/checkbox`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - "RNNotifee (from `../node_modules/@notifee/react-native`)"
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - BEMCheckBox
    - CocoaAsyncSocket
    - fmt
    - GTMSessionFetcher
    - SocketRocket

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-jsc:
    :path: "../node_modules/react-native/ReactCommon/jsc"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-blob-util:
    :path: "../node_modules/react-native-blob-util"
  react-native-date-picker:
    :path: "../node_modules/react-native-date-picker"
  react-native-document-picker:
    :path: "../node_modules/react-native-document-picker"
  react-native-image-picker:
    :path: "../node_modules/react-native-image-picker"
  react-native-mail:
    :path: "../node_modules/react-native-mail"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-network-info:
    :path: "../node_modules/react-native-network-info"
  react-native-ping:
    :path: "../node_modules/react-native-ping"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-tcp-socket:
    :path: "../node_modules/react-native-tcp-socket"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCCheckbox:
    :path: "../node_modules/@react-native-community/checkbox"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNNotifee:
    :path: "../node_modules/@notifee/react-native"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  BEMCheckBox: 5ba6e37ade3d3657b36caecc35c8b75c6c2b1a4e
  boost: 7dcd2de282d72e344012f7d6564d024930a6a440
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBLazyVector: 5d4a3b7f411219a45a6d952f77d2c0a6c9989da5
  FBReactNativeSpec: 3fc2d478e1c4b08276f9dd9128f80ec6d5d85c1f
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  RCT-Folly: 8dc08ca5a393b48b1c523ab6220dfdcc0fe000ad
  RCTRequired: c0569ecc035894e4a68baecb30fe6a7ea6e399f9
  RCTTypeSafety: e90354072c21236e0bcf1699011e39acd25fea2f
  React: a1be3c6dc0a6e949ccd3e659781aa47bbae1868f
  React-callinvoker: 1020b33f6cb1a1824f9ca2a86609fbce2a73c6ed
  React-Codegen: 737d64c442d145fedf67c8ecd34d758f8a5ae52d
  React-Core: 7e85bffed8cbb93a4355fb37d5f986777de8f610
  React-CoreModules: b934bf2d99a27be649d5682b5057268b5df91928
  React-cxxreact: 550099f12a39afd3503ac489e4cd008e2cf7a07d
  React-debug: 17366a3d5c5d2f5fc04f09101a4af38cb42b54ae
  React-jsc: 844ccd35b8dbd105aa0430b658611a43f10ba8a5
  React-jsi: 95dad58d773cb439c3fc71046d5041b6fea5e550
  React-jsiexecutor: a5d2f70d7a791ea562ca23c443c974e584630d14
  React-jsinspector: aaed4cf551c4a1c98092436518c2d267b13a673f
  React-logger: 005cf0fc175b43fd5a1abca9ad02fc50c5db45d3
  react-native-blob-util: 8bb91e2b0c6c74b82ee73e515396e05e5a13e087
  react-native-date-picker: 5ddb34db03afc527bc07777952c2ac72fa384411
  react-native-document-picker: a338165804b1a14c8e408448115dc0edfd7b73ca
  react-native-image-picker: b981b88b807605540b2de3245c76e9729f418b37
  react-native-mail: 6e83813066984b26403d3fdfe79ac7bb31857e3c
  react-native-netinfo: 26560022f28c06d8ef00a9ff1e03beefbbb60c2d
  react-native-network-info: 23b4f34419d7998727c9e50bf8dee3c1bd3f975a
  react-native-ping: 589027e929c300b0b68cc5c34105f33610177055
  react-native-safe-area-context: b72c4611af2e86d80a59ac76279043d8f75f454c
  react-native-tcp-socket: 120072c8020262032773f80f0daaf3964aaa08a1
  React-NativeModulesApple: 2034acfb29f1cbd716ac5f1808951c0c5b3957e8
  React-perflogger: 496a1a3dc6737f964107cb3ddae7f9e265ddda58
  React-RCTActionSheet: 02904b932b50e680f4e26e7a686b33ebf7ef3c00
  React-RCTAnimation: 88feaf0a85648fb8fd497ce749829774910276d6
  React-RCTAppDelegate: 4946740e9cec5bd6a9c19c35eb4c3c7f63f6be7e
  React-RCTBlob: 455b680e457c36f8d98f4338b51cd3cbf48744bd
  React-RCTImage: b111645ab901f8e59fc68fbe31f5731bdbeef087
  React-RCTLinking: 3d719727b4c098aad3588aa3559361ee0579f5de
  React-RCTNetwork: b44d3580be05d74556ba4efbf53570f17e38f734
  React-RCTSettings: c0c54b330442c29874cd4dae6e94190dc11a6f6f
  React-RCTText: 9b9f5589d9b649d7246c3f336e116496df28cfe6
  React-RCTVibration: 691c67f3beaf1d084ceed5eb5c1dddd9afa8591e
  React-rncore: 142268f6c92e296dc079aadda3fade778562f9e4
  React-runtimeexecutor: d465ba0c47ef3ed8281143f59605cacc2244d5c7
  React-runtimescheduler: 255945245bcf8d402d106f5991a8486a7897e529
  React-utils: 5af1d6c958748398eae8030a2fa08af220f4a2e6
  ReactCommon: e1d24200e93da4f6f93d54b2da9a7dd8c2b81c28
  RNCAsyncStorage: aa75595c1aefa18f868452091fa0c411a516ce11
  RNCCheckbox: 450ce156f3e29e25efa0315c96cfbabe5a39ded1
  RNDeviceInfo: 900bd20e1fd3bfd894e7384cc4a83880c0341bd3
  RNFS: 89de7d7f4c0f6bafa05343c578f61118c8282ed8
  RNGestureHandler: c2358951aa4fe5a9ee2085b3c2880c1af98af269
  RNNotifee: 8768d065bf1e2f9f8f347b4bd79147431c7eacd6
  RNReanimated: a954cbe1f0967ad8dc07cb7a6399821b87de48a5
  RNScreens: 67dd26e0c1e6df63f4ccaaf281c1ecdd88c995c3
  RNSVG: d5b9770b400490ea6a08b04c9401d9434b1b58b9
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  Yoga: 3efc43e0d48686ce2e8c60f99d4e6bd349aff981

PODFILE CHECKSUM: 07b49e2b1580808296589b57a78d467667c6cedb

COCOAPODS: 1.16.2
