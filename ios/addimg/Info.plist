<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CFBundleDevelopmentRegion</key>
		<string>en</string>
		<key>CFBundleDisplayName</key>
		<string>Delta POS Admin</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>$(PRODUCT_NAME)</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(MARKETING_VERSION)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(CURRENT_PROJECT_VERSION)</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSExceptionDomains</key>
			<dict>
				<key>localhost</key>
				<dict>
					<key>NSExceptionAllowsInsecureHTTPLoads</key>
					<true/>
				</dict>
			</dict>
		</dict>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>The application requires permission to use images in the device for the purpose of adding product description images</string>
		<key>UIAppFonts</key>
		<array>
			<string>AntDesign.ttf</string>
			<string>Fontisto.ttf</string>
			<string>MaterialCommunityIcons.ttf</string>
			<string>FontAwesome.ttf</string>
			<string>SimpleLineIcons.ttf</string>
			<string>Feather.ttf</string>
			<string>FontAwesome5.ttf</string>
			<string>Ionicons.ttf</string>
			<string>Zocial.ttf</string>
			<string>MaterialIcons.ttf</string>
			<string>AntDesign.ttf</string>
			<string>EvilIcons.ttf</string>
			<string>Entypo.ttf</string>
		</array>
		<key>UIBackgroundModes</key>
		<array>
			<string>processing</string>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIRequiredDeviceCapabilities</key>
		<array>
			<string>armv7</string>
		</array>
		<key>UIStatusBarStyle</key>
		<string></string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false/>
		<key>BGTaskSchedulerPermittedIdentifiers</key>
		<array>
			<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		</array>
	</dict>
</plist>
