import Foundation
import UIKit

@objc(DatePickerModule)
class DatePickerModule: NSObject {

  @objc
  func showDatePicker() {
    DispatchQueue.main.async {
      let alertController = UIAlertController(title: "Select Date", message: nil, preferredStyle: .actionSheet)
      let datePicker = UIDatePicker()
      datePicker.datePickerMode = .date
      datePicker.preferredDatePickerStyle = .wheels
      alertController.view.addSubview(datePicker)
      datePicker.translatesAutoresizingMaskIntoConstraints = false
      NSLayoutConstraint.activate([
        datePicker.leadingAnchor.constraint(equalTo: alertController.view.leadingAnchor),
        datePicker.trailingAnchor.constraint(equalTo: alertController.view.trailingAnchor),
        datePicker.topAnchor.constraint(equalTo: alertController.view.topAnchor, constant: 20),
        datePicker.heightAnchor.constraint(equalToConstant: 200)
      ])
      let doneAction = UIAlertAction(title: "Done", style: .default) { _ in
        let selectedDate = datePicker.date
        print("Selected date: \(selectedDate)")
      }
      alertController.addAction(doneAction)
      let cancelAction = UIAlertAction(title: "Cancel", style: .cancel, handler: nil)
      alertController.addAction(cancelAction)
      if let rootViewController = UIApplication.shared.keyWindow?.rootViewController {
        rootViewController.present(alertController, animated: true, completion: nil)
      }
    }
  }
}