require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'
platform :ios, min_ios_version_supported
prepare_react_native_project!

# pod 'Firebase', :modular_headers => true
# pod 'FirebaseCore', :modular_headers => true
# pod 'GoogleUtilities', :modular_headers => true
# pod 'FirebaseAppCheckInterop', :modular_headers => true
# pod 'FirebaseAuthInterop', :modular_headers => true
# pod 'FirebaseCoreExtension', :modular_headers => true
pod 'GTMSessionFetcher', :modular_headers => true
pod 'RNGestureHandler', :path => '../node_modules/react-native-gesture-handler'
# Force pods to match minimum iOS version for React Native
# Fixes build issue on Xcode Cloud where some pods
# Use iOS 12 calls despite being set as iOS 11

# fix bugs version xCode 14.3 don't work
def __apply_Xcode_14_3_RC_post_install_workaround(installer)
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      current_target = config.build_settings['IPHONEOS_DEPLOYMENT_TARGET']
      minimum_target = min_ios_version_supported
      if current_target.to_f < minimum_target.to_f
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = minimum_target
      end
    end
  end
end
# fix bugs version xCode 14.3 don't work


# If you are using a `react-native-flipper` your iOS build will fail when `NO_FLIPPER=1` is set.
# because `react-native-flipper` depends on (FlipperKit,...) that will be excluded
#
# To fix this you can also exclude `react-native-flipper` using a `react-native.config.js`
# ```js
# module.exports = {
#   dependencies: {
#     ...(process.env.NO_FLIPPER ? { 'react-native-flipper': { platforms: { ios: null } } } : {}),
# ```
# flipper_config = ENV['NO_FLIPPER'] == "1" ? FlipperConfiguration.disabled : FlipperConfiguration.enabled

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'addimg' do
  config = use_native_modules!
 
  # Flags change depending on the env values.
  flags = get_default_flags()

  use_react_native!(
    :path => config[:reactNativePath],
    # Hermes is now enabled by default. Disable by setting this flag to false.
    # Upcoming versions of React Native may rely on get_default_flags(), but
    # we make it explicit here to aid in the React Native upgrade process.
    :hermes_enabled => false,
    :fabric_enabled => flags[:fabric_enabled],
    # Enables Flipper.
    #
    # Note that if you have use_frameworks! enabled, Flipper will not work and
    # you should disable the next line.
    # :flipper_configuration => flipper_config,
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  target 'addimgTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|
    react_native_post_install(
      installer,
      # Set `mac_catalyst_enabled` to `true` in order to apply patches
      # necessary for Mac Catalyst builds
      :mac_catalyst_enabled => false
    )
    # __apply_Xcode_12_5_M1_post_install_workaround(installer)
    # fix bugs version xCode 14.3 don't work
    # __apply_Xcode_14_3_RC_post_install_workaround(installer)
    # fix bugs version xCode 14.3 don't work
  end
end
