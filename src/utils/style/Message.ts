//  const messageApp = 'Thông báo';
//  const cancel = 'Huỷ bỏ';
//  const agree = 'Đồng ý';
//  const networkIsError = '<PERSON><PERSON> lỗi xảy ra ';
// // Message on Login Screen
//  const loginFail = 'Sai mật khẩu hoặc tài khoản';
//  const fillInBlank = 'Không đc bỏ trống';
//  const networkError = 'Không có mạng vui lòng đăng nhập lại sau';

// //Message disable account admin

//  const messageDisable =
//     'Gửi yêu cầu xoá tài khoản admin thành công . Bạn đã bị đăng xuất';

// //Message on Category feature
//  const addCategorySuccess = 'Thêm danh mục thành công';
//  const fillInBlankCategory = 'Chưa điền tên danh mục';
//  const categoryNameExist = 'Tên danh mục đã tồn tại';
//  const addCategoryFail = 'Lỗi thêm danh mục';
//  const editCategoryFail = 'Cập nhật danh mục không thành công';
//  const addCategoryExistButDelete =
//     'Danh mục này đã tồn tại và hiện tại đang bị xóa bạn có muốn khôi phục';
//  const deleteCategorySuccess = 'Xoá danh mục thành công';
//  const categoryNameNotValid = 'Danh mục không hợp lệ';
//  const deleteCategoryFail = 'Xoá danh mục không thành công';
//  const editCategorySuccess = 'Cập nhật danh mục thành công';
//  const editCategoryBlank = 'Không được bỏ trống tên danh mục';

// //Message on create Product
//  const addProductSuccess = 'Thêm sản phẩm thành công';
//  const productNameExist = 'Tên sản phẩm đã tồn tại';
//  const notFullValid = 'Chưa điền đầy đủ thông tin';
//  const findNotCategory = 'Không tìm thấy danh mục';
//  const notSelectedCategory = 'Vui lòng thêm danh mục';
//  const addProductFail = 'Tạo sản phẩm thất bại';
//  const alertVat = 'Nhập VAT từ 0 đến 100';
//  const addProductExistButDeleted =
//     'Sản phẩm này đã tồn tại và hiện tại đang bị xóa bạn có muốn khôi phục';
//  const deleteProductSuccess = 'Xoá sản phẩm thành công';
//  const deleteProductFail = 'Có lỗi xảy ra vui lòng thử lại sau';

// //Message on edit Product
//  const editProductSuccess = 'Cập nhật thông tin sản phẩm thành công';
//  const editNotFullValid = 'Chưa điền đầy đủ hoặc sai thông tin';
//  const editProductFail = 'Cập nhật thông tin sản phẩm thất bại';

// //Message on send info guest
//  const sendInfoSuccess =
//     'Cảm ơn bạn đã gửi thông tin. Chúng tôi sẽ liên lạc lại sau';
//  const sendInfoFail = 'Gửi thông tin thất bại, không được bỏ trống';

// //Message on staff screen
//  const addStaffSuccess = 'Thêm nhân viên thành công';
//  const addStaffFaill = 'Thêm nhân viên thất bại';
//  const infoStaffExist =
//     'Số điện thoại này đã được đăng ký vui lòng đăng ký số điện thoại khác';
//  const staffExistButDeleted =
//     'Thông tin nhân viên đã tồn tại nhưng hiện tại đã bị xoá bạn có muốn khôi phục';

//  const notFullValidStaff = 'Không được bỏ trống thông tin';

// //Message on detail staff screen
//  const changeInfoStaffSuccess = 'Cập nhật thông tin nhân viên thành công';
//  const phoneNumberExist = 'Số điện thoại đã tồn tại';
//  const phoneNumberNotValid = 'Số điện thoại phải là 10 số';
//  const changeInfoStaffFail = 'Có lỗi xảy ra vui lòng thử lại sau';

//  const deleteStaffSuccess = 'Xoá nhân viên thành công';
//  const deleteStaffFail =
//     'Nhân viên đang trực tuyến không thể xoá. Vui lòng đổi thành offline để xoá';

//  const changePasswordSuccess = 'Thay đổi mật khẩu nhân viên thành công';
//  const changePasswordFail = 'Thay đổi mật khẩu nhân viên thất bại';

// //Message on  Table
//  const addTableSuccess = 'Thêm bàn thành công';
//  const addTableExist = 'Bàn đã tồn tại';
//  const addTableExistButDeleted =
//     'Bàn này đã tồn tại và hiện tại đang bị xóa bạn có muốn khôi phục lại';

//  const addTableFail = 'Thêm bàn thất bại';

//  const cancelOrderSuccess = 'Huỷ bàn thành công';
//  const cancelOrderFail = 'Huỷ bàn thất bại';

//  const deleteTableSuccess = 'Xoá bàn thành công';
//  const deleteTableFail = 'Xoá bàn thất bại';

//  const deleteMutilTableSuccess = 'Xoá các bàn thành công';
//  const doNotChooseTableDelete = 'Chưa chọn bàn để xoá';
//  const deleteMutilTableFail = 'Xoá các bàn thất bại';

// //Message formik
//  const fieldIsBlank = 'Không được bỏ trống';
//  const minValidNumber = 4;
//  const maxValidNumberPhone = 11;
//  const maxValidNumber = 40;
//  const minValidPassword = 4;
//  const maxValidPassword = 20;
//  const maxValidSendInfo = 200;

//  const minValidMessageName = 'Họ và tên lớn hơn 4 kí tự';
//  const maxValidMessageName = 'Họ và tên lớn hơn 40 kí tự';

//  const minValidMessageAddress = 'Địa chỉ lớn hơn 4 kí tự';
//  const maxValidMessageAddress = 'Địa chỉ bé hơn 40 kí tự';

//  const minValidMessageNumberPhone = 'Số điện thoại quá ngắn';
//  const maxValidMessageNumberPhone = 'Số điện thoại quá dài';
//  const matchesNumberPhone =
//     'Nhập đúng định dạng số điện thoại';
//  const matchNumberPhone = /^(84|0[1|2|3|4|5|6|7|8|9])+([0-9]{8})\b/g;

//  const minValidMessagePassword = 'Mật khẩu quá ngắn';
//  const maxValidMessagePassword = 'Mật khẩu quá dài';
//  const confirmPasswordMessage =
//     'Mật khẩu xác thực phải giống với mật khẩu';

//  const minValidMessagePSendInfo = 'Nội dung liên lạc quá ngắn';
//  const maxValidMessagePSendInfo = 'Nội dung liên lạc quá dài';

// //Message customers

//  const createCustomersSuccess = 'Thêm khách hàng thành công';
//  const createCustomersNumberPhoneExist =
//     'Số điện thoại khách hàng đã tồn tại';

//  const createCustomerNameFillInBlank = 'Vui lòng điền tên khách hàng';
// // VietVang number phone
//  const vietVangPhoneNumber = `02862651411`;

//handle  default
/* export default {
    messageApp,
    cancel,
    agree,
    networkIsError,
    loginFail,
    fillInBlank,
    networkError,
    messageDisable,
    addCategorySuccess,
    fillInBlankCategory,
    categoryNameExist,
    addCategoryFail,
    editCategoryFail,
    addCategoryExistButDelete,
    deleteCategorySuccess,
    categoryNameNotValid,
    deleteCategoryFail,
    editCategorySuccess,
    editCategoryBlank,
    addProductSuccess,
    productNameExist,
    notFullValid,
    findNotCategory,
    notSelectedCategory,
    addProductFail,
    alertVat,
    addProductExistButDeleted,
    deleteProductSuccess,
    deleteProductFail,
    editProductSuccess,
    editNotFullValid,
    editProductFail,
    sendInfoSuccess,
    sendInfoFail,
    addStaffSuccess,
    addStaffFaill,
    infoStaffExist,
    staffExistButDeleted,
    notFullValidStaff,
    changeInfoStaffSuccess,
    phoneNumberExist,
    phoneNumberNotValid,
    changeInfoStaffFail,
    deleteStaffSuccess,
    deleteStaffFail,
    changePasswordSuccess,
    changePasswordFail,
    addTableSuccess,
    addTableExist,
    addTableExistButDeleted,
    addTableFail,
    cancelOrderSuccess,
    cancelOrderFail,
    deleteTableSuccess,
    deleteTableFail,
    deleteMutilTableSuccess,
    doNotChooseTableDelete,
    deleteMutilTableFail,
    fieldIsBlank,
    minValidNumber,
    maxValidNumberPhone,
    maxValidNumber,
    minValidPassword,
    maxValidPassword,
    maxValidSendInfo,
    minValidMessageName,
    maxValidMessageName,
    minValidMessageAddress,
    maxValidMessageAddress,
    minValidMessageNumberPhone,
    maxValidMessageNumberPhone,
    matchesNumberPhone,
    matchNumberPhone,
    minValidMessagePassword,
    maxValidMessagePassword,
    confirmPasswordMessage,
    minValidMessagePSendInfo,
    maxValidMessagePSendInfo,
    createCustomersSuccess,
    createCustomersNumberPhoneExist,
    createCustomerNameFillInBlank,
    vietVangPhoneNumber,
};
*/