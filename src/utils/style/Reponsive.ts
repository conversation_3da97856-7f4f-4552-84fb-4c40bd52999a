import { Dimensions, PixelRatio } from 'react-native';

export const widthWindow = Dimensions.get('window').width;
export const heightWindow = Dimensions.get('window').height;
const { width: W, height: H } = Dimensions.get('screen');
const pixelDensity = PixelRatio.get();
interface Item {
  name: string;
  price: string;
  quantity: number;
  total_price: string;
  vat: number;
  weight: string;
}
interface OutputItem {
  stt: string;
  hanghoa_loai: string;
  ten: string;
  dongia: string;
  tongtien_chuathue: string;
  mathue: string;
  tongtien_thue: string;
  tongtien_cothue: string;
}
const metricsNumber = () => {
  const density = pixelDensity * 160;
  const x = Math.pow((W * pixelDensity) / density, 2);
  const y = Math.pow((H * pixelDensity) / density, 2);
  const screenInches = Math.sqrt(x + y) + 1.6;

  return screenInches;
};

export const scale = (number: number) => {
  const ratio = (metricsNumber() + pixelDensity) / 10;
  const value = number * Number(ratio.toFixed(1));

  return Number(value.toFixed(1));
};

export const toNonAccentVietnamese = (str: string) => {
  str = str.replace(/A|Á|À|Ã|Ạ|Â|Ấ|Ầ|Ẫ|Ậ|Ă|Ắ|Ằ|Ẵ|Ặ/g, 'A');
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
  str = str.replace(/E|É|È|Ẽ|Ẹ|Ê|Ế|Ề|Ễ|Ệ/, 'E');
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
  str = str.replace(/I|Í|Ì|Ĩ|Ị/g, 'I');
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
  str = str.replace(/O|Ó|Ò|Õ|Ọ|Ô|Ố|Ồ|Ỗ|Ộ|Ơ|Ớ|Ờ|Ỡ|Ợ/g, 'O');
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
  str = str.replace(/U|Ú|Ù|Ũ|Ụ|Ư|Ứ|Ừ|Ữ|Ự/g, 'U');
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
  str = str.replace(/Y|Ý|Ỳ|Ỹ|Ỵ/g, 'Y');
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
  str = str.replace(/Đ/g, 'D');
  str = str.replace(/đ/g, 'd');
  // Some system encode vietnamese combining accent as individual utf-8 characters
  str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ''); // Huyền sắc hỏi ngã nặng
  str = str.replace(/\u02C6|\u0306|\u031B/g, ''); // Â, Ê, Ă, Ơ, Ư
  return str;
};

export const setHeight = (h: number) => (heightWindow / 100) * h;
export const setWidth = (w: number) => (widthWindow / 100) * w;

export function extractDateTime(updated_at: string) {
  const dateTime = new Date(updated_at);

  const day = dateTime.getDate().toString().padStart(2, '0');
  const month = (dateTime.getMonth() + 1).toString().padStart(2, '0');
  const year = dateTime.getFullYear();
  const hours = dateTime.getHours().toString().padStart(2, '0');
  const minutes = dateTime.getMinutes().toString().padStart(2, '0');
  const seconds = dateTime.getSeconds().toString().padStart(2, '0');

  const datePart = `${day}-${month}-${year}`;
  const timePart = `${hours}:${minutes}:${seconds}`;

  return {
    date: datePart,
    time: timePart,
  };
}

export function getToday(): string {
  const today = new Date();
  // Assuming you format the date as a string
  const formattedDate = `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}-${today.getDate().toString().padStart(2, '0')}`;
  return formattedDate;
}

export function numberToWords(n: number): string {
  if (n > 10**10 - 1) {
      return "Giá trị vượt quá giới hạn";
  }

  const units = ["", "một", "hai", "ba", "bốn", "năm", "sáu", "bảy", "tám", "chín"];
  const teens = ["mười", "mười một", "mười hai", "mười ba", "mười bốn", "mười lăm", "mười sáu", "mười bảy", "mười tám", "mười chín"];
  const tens = ["", "", "hai mươi", "ba mươi", "bốn mươi", "năm mươi", "sáu mươi", "bảy mươi", "tám mươi", "chín mươi"];
  const thousands = ["", "nghìn", "triệu", "tỷ"];

  function threeDigitToWords(num: number): string {
      if (num === 0) {
          return "";
      } else if (num < 10) {
          return units[num];
      } else if (num < 20) {
          return teens[num - 10];
      } else if (num < 100) {
          return tens[Math.floor(num / 10)] + (num % 10 !== 0 ? " " + units[num % 10] : "");
      } else {
          return units[Math.floor(num / 100)] + " trăm" + (num % 100 >= 20 ? " " + tens[Math.floor((num % 100) / 10)] : "") + (num % 10 !== 0 ? " " + units[num % 10] : "");
      }
  }

  function splitNumberByThousands(num: number): number[] {
      const parts: number[] = [];
      while (num > 0) {
          parts.push(num % 1000);
          num = Math.floor(num / 1000);
      }
      return parts;
  }

  const parts = splitNumberByThousands(n);
  const partsInWords: string[] = [];

  for (let i = 0; i < parts.length; i++) {
      if (parts[i] !== 0) {
          let partInWords = threeDigitToWords(parts[i]);
          if (thousands[i]) {
              partInWords += " " + thousands[i];
          }
          partsInWords.push(partInWords);
      }
  }

  const result = partsInWords.reverse().join(" ");
  return result.trim() + " đồng";
}


export function calculateTotalPrice(items: any): number {
  return items.reduce((total: number, item: { quantity: number; price: number; }) => total + item.quantity * item.price, 0);
}






export function formatOutput(items: any): any {
  return items.map((item: { price: any; total_price: any; quantity: number; name: any; products: {
    [x: string]: any; vat: { toString: () => any; }; 
}; }, index: number) => {
      const price = (item.price);
      const total_price = (item.total_price);
      const tongtien_chuathue = price * item.quantity;
      const tongtien_thue = total_price - tongtien_chuathue;

      return {
          stt: (index + 1).toString(),  // Start from 1
          hanghoa_loai: '1',
          ten: item.products?.title,
          dongia: item.price,  
          tongtien_chuathue: (tongtien_chuathue),
          mathue: item.products?.vat.toString() ?? 0,
          tongtien_thue: (tongtien_thue),
          tongtien_cothue: (total_price)
      };
  });
}
