import { NetworkInfo } from 'react-native-network-info';
import <PERSON> from 'react-native-ping';
import TcpSocket from 'react-native-tcp-socket';

const PRINTER_PORTS = [9100]; // Raw printing, IPP, LPD

// Hàm kiểm tra port TCP
const checkPortOpen = (ip: any, port: any) => {

    
    return new Promise((resolve) => {
        const client = TcpSocket.createConnection(
            { host: ip, port, timeout: 800 },
            () => {
                client.destroy();
                resolve(true);
            }
        );

        client.on('error', () => {
            client.destroy();
            resolve(false);
        });

        client.on('timeout', () => {
            client.destroy();
            resolve(false);
        });
    });
};

// Hàm kiểm tra 1 IP có phải máy in không
const isPrinter = async (ip: any) => {
    for (let port of PRINTER_PORTS) {
        if (await checkPortOpen(ip, port)) {
            return true;
        }
    }
    return false;
};

// <PERSON><PERSON>m quét toàn bộ LAN
export const scanLANForPrinters = async () => {
    const printers: any = [];
    try {
        // const gatewayIP = await NetworkInfo.getGatewayIPAddress();
        const gatewayIP = '***********'; // test tạm trên simulator

        NetworkInfo.getIPV4Address().then(ip => console.log('IP máy:', ip));
        NetworkInfo.getGatewayIPAddress().then(ip => console.log('Gateway:', ip));
        if (!gatewayIP) {
            console.warn('Không lấy được Gateway IP');
            return [];
        }

        const subnetBase = gatewayIP.split('.').slice(0, 3).join('.');

        const scanTasks = [];
        for (let i = 1; i <= 254; i++) {
            const ip = `${subnetBase}.${i}`;
            scanTasks.push(
                (async () => {
                    try {
                        await Ping.start(ip, { timeout: 300 });
                        if (await isPrinter(ip)) {
                            console.log(`✅ Máy in tìm thấy: ${ip}`);
                            printers.push(ip);
                        }
                    } catch {
                        // không phản hồi → bỏ qua
                    }
                })()
            );
        }

        await Promise.all(scanTasks);
        return printers;
    } catch (error) {
        console.error('Lỗi khi quét LAN:', error);
        return [];
    }
};
