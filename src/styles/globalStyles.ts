import { Platform, StyleSheet } from 'react-native';
import { colors } from '../constants/color';
import { scale } from '../utils/style/Reponsive';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from 'react-native-responsive-screen';

export const globalStyles = StyleSheet.create({
  container: {
    flex: 1,
    marginHorizontal:scale(3),
  },

  row: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  col: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },

  text: {
    fontSize: scale(14),
    color: colors.text,
  },

  inputContainer: {
    borderRadius: scale(12),
    paddingHorizontal: Platform.OS === 'ios' ? scale(12) : scale(8),
    paddingVertical: 2,
    borderWidth: 1,
    height: scale(40),
  },

  section: {
    marginBottom: scale(16),
    paddingHorizontal: scale(2),
  },

  tag: {
    paddingHorizontal: 20,
    paddingVertical: Platform.OS === 'ios' ? 6 : 4,
    borderRadius: 100,
    backgroundColor: colors.blue,
  },

  card: {
    borderRadius: 12,
  },

  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 100,
    backgroundColor: 'rgba(0,0,0,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  button: {
    marginVertical: scale(2),
    height: scale(48),
    width: '42%',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scale(10),
    backgroundColor: colors.white,
  },
  loading: {
    position: 'absolute',
    justifyContent: 'center',
    alignContent: 'center',
    top: -scale(20),
    bottom: 0,
    right: -scale(20),
    left: -scale(20),
  },
  iconSearch: {
    // paddingHorizontal: scale(16),
  },
});
