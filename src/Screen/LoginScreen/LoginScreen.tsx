import React, {useContext, useEffect, useState} from 'react';
import {
  Keyboard,
  KeyboardAvoidingView,
  Linking,
  PermissionsAndroid,
  Platform,
  SafeAreaView,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import {useDispatch} from 'react-redux';
import {Formik} from 'formik';
import Spinner from 'react-native-loading-spinner-overlay';
import * as Yup from 'yup';
import {Text, View, ModalSendInfo} from '../../Component/index';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Feather from 'react-native-vector-icons/Feather';
import notifee from '@notifee/react-native';

import {LoginScreenCss} from './LoginScreenCss';
import {AuthContext} from '../../context/AuthContext';
import {setStateShowOption} from '../../Redux/Slide';
import {setIsValue} from '../../Redux/GetData';
import {languages} from '../../constants/index';

import {scale} from '../../utils/style/Reponsive';
import {useAppSelector} from '../../hooks';
import {ThunkDispatch} from '@reduxjs/toolkit';
import SelectDropdown from 'react-native-select-dropdown';
// import notifee, {EventType} from '@notifee/react-native';

interface LanguageData {
  [key: string]: string | undefined;
  minValidMessageNumberPhone?: string;
  maxValidMessageNumberPhone?: string;
  fieldIsBlank?: string;
  matchesNumberPhone?: string;
  minValidMessagePassword?: string;
  maxValidMessagePassword?: string;
  titleApp?: string;
  qlbh?: string;
  nameApp?: string;
  phone?: string;
  pass?: string;
  login?: string;
  registry?: string;
  contact?: string;
  phoneContact?: string;
  email?: string;
}

const LoginScreen = () => {
  const nameOpition = useAppSelector(state => state.counter.nameSelectOpition);
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const {isLoading, login} = useContext(AuthContext);
  const [eye, setEye] = useState<boolean>(true);
  const {
    minValidNumber,
    maxValidNumberPhone,
    matchNumberPhone,
    minValidPassword,
    maxValidPassword,
    maincolor,
    blackcolor,
    graycolor,
    backgroundcolor,
  } = xmlData;
  // async function getToken() {
  //   const deviceToken = await messaging().getToken();
  // }

  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): LanguageData | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: LanguageData = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    minValidMessageNumberPhone,
    maxValidMessageNumberPhone,
    fieldIsBlank,
    matchesNumberPhone,
    minValidMessagePassword,
    maxValidMessagePassword,
    titleApp,
    qlbh,
    nameApp,
    phone,
    pass,
    registry,
    contact,
    phoneContact,
    email,
    vietVangPhoneNumber,
  }: any = langData;

  const regexPattern = matchNumberPhone;
  const regexPhone = new RegExp(regexPattern);
  const countries: string[] = ['Vie', 'Eng', 'Jap'];

  const SignupSchema = Yup.object().shape({
    phone: Yup.string()
      .min(minValidNumber, minValidMessageNumberPhone)
      .max(maxValidNumberPhone, maxValidMessageNumberPhone)
      .required(fieldIsBlank)
      .matches(regexPhone, matchesNumberPhone),
    password: Yup.string()
      .min(minValidPassword, minValidMessagePassword)
      .max(maxValidPassword, maxValidMessagePassword)
      .required(fieldIsBlank),
  });

  const handleLogin = (values: {phone: string; password: string}) => {
    const {phone, password} = values;
    login(phone, password);
  };

  const handleSeteye: () => void = () => {
    setEye(!eye);
  };
  const showModal: () => void = () => {
    dispatch(setStateShowOption({valid: false, nameOpiton: 'SendContact'}));
  };
  const dialCall: () => void = () => {
    const prefix: string = Platform.OS === 'android' ? 'tel:' : 'telprompt:';
    const phoneNumber: string = `${prefix}0${vietVangPhoneNumber}`;
    Linking.openURL(phoneNumber);
  };

  const requestStoragePermission = async () => {
    try {
      await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
      );
      await notifee.requestPermission();
    } catch (err: unknown) {
      console.warn(err);
    }
  };

  useEffect(() => {
    Platform.OS === 'android' ? requestStoragePermission() : '';
    // notifee.requestPermission();
    // return notifee.onForegroundEvent(({type, detail}) => {
    //   switch (type) {
    //     case EventType.DISMISSED:
    //       console.log('User dismissed notification', detail.notification);
    //       break;
    //     case EventType.PRESS:
    //       console.log('User pressed notification', detail.notification);
    //       break;
    //   }
    // });
  }, []);
  // useEffect(() => {
  //   async function onAppBootstrap() {
  //     // Register the device with FCM
  //     await messaging().registerDeviceForRemoteMessages();
  //     console.log('registerDeviceForRemoteMessages');
  //   }
  //   onAppBootstrap();
  // }, []);
  // async function getTokenDivice() {
  //   let deviceToken = await messaging().getToken();
  // }
  // useEffect(() => {
  //   const setupNotifications = async () => {
  //     const channelId = await notifee.createChannel({
  //       id: 'default',
  //       name: 'Default Channel',
  //     });

  //     await getTokenDivice();

  //     messaging().onNotificationOpenedApp(remoteMessage => {
  //       Alert.alert('Open');
  //       console.log('Remote', remoteMessage);
  //     });

  //     const unsubscrit = messaging().onMessage(async remoteMess => {
  //       await notifee.displayNotification({
  //         id: '123',
  //         title: `số lượng đơn hàng`,
  //         body: 'Updated ',
  //         android: {
  //           channelId,
  //           smallIcon: 'ic_launcher', // optional, defaults to 'ic_launcher'.
  //           largeIcon: 'ic_launcher',
  //         },
  //       });
  //     });

  //     return unsubscrit;
  //   };

  //   setupNotifications(); // Call the async function here

  //   // Return a cleanup function (if needed)
  //   return () => {
  //     // Cleanup code here
  //   };
  // }, []);
  return (
    <SafeAreaView style={LoginScreenCss.container}>
      <Spinner visible={isLoading} color={maincolor} />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'height' : 'height'}
        style={[
          LoginScreenCss.container,
          {
            marginHorizontal: scale(8),
          },
        ]}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View flex1>
            <View aItemsFlexEnd style={{marginTop: 10}}>
              <View w_70 />
              <View w_30>
                <SelectDropdown
                  buttonStyle={[
                    {
                      borderColor: maincolor,
                      backgroundColor: backgroundcolor,
                      width: '100%',
                      borderWidth: 1,
                      borderRadius: 8,
                      height: scale(40),
                      marginLeft: 20,
                    },
                  ]}
                  defaultButtonText={
                    isCheckLang == 0 ? `Viet` : isCheckLang == 1 ? `Eng` : `Jap`
                  }
                  data={countries}
                  onSelect={selectedItem => {
                    if (selectedItem === 'Jap') {
                      dispatch(setIsValue(2));
                    }
                    if (selectedItem === 'Vie') {
                      dispatch(setIsValue(0));
                    }
                    if (selectedItem === 'Eng') {
                      dispatch(setIsValue(1));
                    }
                  }}
                  buttonTextAfterSelection={selectedItem => {
                    return selectedItem;
                  }}
                  rowTextForSelection={item => {
                    return item;
                  }}
                />
              </View>
            </View>
            <View
              flex1
              jContentCenter
              aItemsCenter
              mHorizontal10
              mBottom10
              mTop6>
              <View aItemsCenter>
                <Text fontSize32 fontWeight600 mainColor>
                  {titleApp}
                </Text>
                <Text fontSize32 fontWeight600 mainColor>
                  {qlbh}
                </Text>
                <Text fontSize32 fontWeight600 mainColor>
                  {nameApp}
                </Text>
              </View>
            </View>
            <Formik
              initialValues={{
                phone: '',
                password: '',
              }}
              validationSchema={SignupSchema}
              onSubmit={handleLogin}>
              {({
                handleChange,
                values,
                errors,
                touched,
                isValid,
                dirty,
                setFieldTouched,
              }) => (
                <View>
                  <View>
                    <View
                      borderMainColor
                      aItemsCenter
                      borderWidth1
                      flDirectionRow
                      bRadius5
                      mBottom10>
                      <FontAwesome
                        name="phone"
                        size={30}
                        style={[
                          LoginScreenCss.loginIcon,
                          {
                            color: maincolor,
                          },
                        ]}
                      />
                      <TextInput
                        placeholder={phone}
                        style={[
                          LoginScreenCss.InputCss,
                          {
                            color: blackcolor,
                          },
                        ]}
                        onChangeText={handleChange('phone')}
                        onBlur={() => setFieldTouched('phone')}
                        value={values.phone}
                        keyboardType={'numeric'}
                        placeholderTextColor={blackcolor}
                      />
                    </View>
                    {errors.phone && touched.phone ? (
                      <Text paddingLeft6 paddingBottom8 redText>
                        {errors.phone}
                      </Text>
                    ) : null}
                    <View
                      borderMainColor
                      aItemsCenter
                      borderWidth1
                      flDirectionRow
                      bRadius5
                      mBottom10>
                      <Ionicons
                        name="md-key"
                        size={30}
                        style={[
                          LoginScreenCss.loginIcon,
                          {
                            color: maincolor,
                          },
                        ]}
                      />
                      <TextInput
                        placeholder={pass}
                        style={[
                          LoginScreenCss.InputCss,
                          {
                            color: blackcolor,
                          },
                        ]}
                        secureTextEntry={eye}
                        onChangeText={handleChange('password')}
                        onBlur={() => setFieldTouched('password')}
                        value={values.password}
                        placeholderTextColor={blackcolor}
                      />
                      <TouchableOpacity onPress={handleSeteye}>
                        <Ionicons
                          name={eye ? 'eye-off' : 'eye'}
                          size={30}
                          style={[
                            LoginScreenCss.loginIcon,
                            {
                              color: maincolor,
                            },
                          ]}
                        />
                      </TouchableOpacity>
                    </View>
                    {errors.password && touched.password ? (
                      <Text paddingLeft6 paddingBottom8 redText>
                        {errors.password}
                      </Text>
                    ) : null}
                  </View>
                  <TouchableOpacity
                    onPress={() => handleLogin(values)}
                    disabled={!isValid || !dirty}
                    style={[
                      {
                        backgroundColor:
                          !isValid || !dirty ? graycolor : maincolor,
                      },
                      LoginScreenCss.buttonLogin,
                    ]}>
                    <Text fontSize22 whiteColor>
                      {langData?.login}
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={showModal}
                    style={[
                      {
                        backgroundColor: maincolor,
                      },
                      LoginScreenCss.buttonLogin,
                    ]}>
                    <Text fontSize22 whiteColor>
                      {registry}
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
            </Formik>
            <View flex1 jContentCenter aItemsCenter>
              <View
                style={[
                  LoginScreenCss.iconPhone,
                  {
                    backgroundColor: maincolor,
                    borderColor: maincolor,
                  },
                ]}>
                <Feather
                  name="phone-call"
                  size={scale(30)}
                  style={{color: backgroundcolor}}
                  onPress={() => {
                    dialCall();
                  }}
                />
              </View>
              <View aItemsCenter>
                <Text contactText fontSize22>
                  {contact}
                </Text>
                <Text contactText fontSize22>
                  {phoneContact}
                </Text>
                <Text contactText fontSize22>
                  {email}
                </Text>
              </View>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>

      {nameOpition === 'SendContact' ? (
        <View loadingScreen>
          <ModalSendInfo />
        </View>
      ) : null}
    </SafeAreaView>
  );
};

export default React.memo(LoginScreen);
