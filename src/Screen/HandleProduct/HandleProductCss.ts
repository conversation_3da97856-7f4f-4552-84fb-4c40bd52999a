import { StyleSheet } from 'react-native';
import { heightWindow, scale, widthWindow } from '../../utils/style/Reponsive';
import { heightPercentageToDP as hp , widthPercentageToDP as wp} from 'react-native-responsive-screen'

export const HandleProductCss = StyleSheet.create({
  container: {
    flex: 1,
    // marginHorizontal: scale(8),
    marginHorizontal: hp('0.5%'),
  },
  btnBackScreen: {
    alignItems: 'flex-end',
    marginVertical: wp('1%'),
    marginRight:hp('0.5%'),
  },
  addImage: {
    height: scale(48),
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: scale(8),
  },

  modalView: {
    width: '80%',
    margin: scale(20),
    backgroundColor: 'white',
    borderRadius: scale(20),
    padding: scale(32),
    alignItems: 'center',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  btnDeleteProduct: {
    width: scale(100),
    borderRadius: scale(20),
    padding: scale(10),
    elevation: 2,
    marginHorizontal: 5,
  },
  modalText: {
    marginBottom: scale(15),
    marginVertical: 7,
  },

  inputNameProduct: {
    // width: wp('60%'),
    borderBottomWidth: scale(1),
    height: scale(30),
    fontSize: scale(18),
  },

  inputVat: {
    borderBottomWidth: scale(1),
    height: scale(30),
    width: '100%',
    fontSize: scale(18),

    // textAlign: 'center',
  },

  inputPrice: {
    borderBottomWidth: scale(1),
    height: scale(30),
    fontSize: scale(18),

    // textAlign: 'center',
    // alignItems: 'center',
    // justifyContent: 'center',
  },

  viewImage: {
    alignItems: 'center',
  },
  deleteImage: {
    alignItems: 'flex-end',
    bottom: -scale(20),
    zIndex: 10,
    right: -scale(6),
    width: '100%',
  },
  imageProduct: {
    width: wp('100%'),
    height: hp('30%'),
    borderRadius: scale(12),
    marginVertical:hp('1.5%')
  },

  btnConfirm: {
    marginVertical: scale(10),
    height: scale(48),
    width: widthWindow * 0.45,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scale(10),
  },

  contentLoadding: {
    position: 'absolute',
    backgroundColor: 'rgba(0,0,0,0.1)',
    justifyContent: 'center',
    alignContent: 'center',
    top: 0,
    bottom: 0,
    right: -scale(20),
    left: -scale(20),
  },
});
