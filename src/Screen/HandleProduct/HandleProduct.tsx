import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  Alert,
  Image,
  Keyboard,
  Modal,
  Pressable,
  SafeAreaView,
  ScrollView,
  TextInput,
} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {useDispatch} from 'react-redux';
import {Text, View, DropDownComponents} from '../../Component/index';
import {launchImageLibrary} from 'react-native-image-picker';
import {TouchableOpacity} from 'react-native-gesture-handler';
import Entypo from 'react-native-vector-icons/Entypo';

import {setFlagProduct, setLoading} from '../../Redux/Slide';
import {BASE_URL} from '../../api/ApiManager';
import {HandleProductCss} from './HandleProductCss';

import {useAppSelector} from '../../hooks';
import {ThunkDispatch} from '@reduxjs/toolkit';
import {handleDeleteProduct} from '../../api/handleApi';
import {rootInterface} from '../../interface';
import {languages} from '../../constants';
import {scale} from '../../utils/style/Reponsive';
import axios from 'axios';
import {fetchListProduct} from '../../Redux/GetData';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';

interface IProps {
  route: any;
  navigation: {
    pop: (count?: number) => void;
  };
}
interface IHandleProduct {
  [key: string]: string | undefined;
  handleProductScreenTitle?: string;
  handleProductScreenName?: string;
  handleProductScreenVat?: string;
  handleProductScreenPrice?: string;
  handleProductScreenAddImage?: string;
  handleProductScreenUpdateImage?: string;
  handleProductScreenUpdate?: string;
  handleProductScreenDelete?: string;
  handleProductScreenDeleteConfirm?: string;
  agree?: string;
  cancel?: string;
  componentAddProductPrice?: string;
  componentAddProductPriceNotVAT?: string;
}
const HandleProduct = (props: IProps) => {
  const {navigation} = props;
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {graycolor, maincolor, blackcolor} = xmlData;
  const [selectedCategory, setSelectedCategory] = useState<{
    value: string;
    key: number;
  }>();
  const [title, setTitle] = useState<string>('');
  const [vat, setVAT] = useState<string>('0');
  const [price, setPrice] = useState<string>('0');
  const [idProduct, setId] = useState<string>('');
  const [image, setImage] = useState<string>('');
  const [category_name, setGroup] = useState<string>('');

  const [imageUri, setImageUri] = useState<string>();
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [btnUpdate, setBtnUpdate] = useState<boolean>(true);

  const [categoryID, setCategoryID] = useState<string>();
  const [fromData, setFromData] = useState<FormData>(new FormData());
  const [checkValue, setCheckValue] = useState<boolean>(true);
  const [listSelectSubProduct, setListSelectSubProduct] = useState<any[]>([]);
  const {access_token, loading} = useAppSelector(state => state.counter);
  const listCategory = useAppSelector(state => state.getData.listCategory);
  const listSubProduct = useAppSelector(state => state.getData.listSubProduct);
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const is_tax_included =
    useAppSelector(state => state.counter.infoAdmin?.admin?.store?.is_tax_included) || 0;

  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): IHandleProduct | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: IHandleProduct = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();

  const {
    handleProductScreenTitle,
    handleProductScreenName,
    handleProductScreenVat,
    handleProductScreenPrice,
    handleProductScreenAddImage,
    handleProductScreenUpdateImage,
    handleProductScreenUpdate,
    handleProductScreenDelete,
    handleProductScreenDeleteConfirm,
    agree,
    cancel,
    deleteProductFail,
    deleteProductSuccess,
    editNotFullValid,
    editProductFail,
    editProductSuccess,
    messageApp,
    PriceOnlyInt,
    networkIsError,
    alertVat,
    productsScreenDishType,
    productsScreenSideDish,
    FailedToDddNewSideDish,
    SuccessfullyAddedNewSideDish,
    handleProductScreenListSideDish,
    componentAddProductPrice,
    componentAddProductPriceNotVAT,
    componentProductPrice,
  }: any = langData;
  //value update product
  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const currencySymbol = useAppSelector(state => state.counter?.infoAdmin?.admin?.store?.currency_symbol) || "đ";
  //handle getdata from product screen
  useEffect(() => {
    const {
      title,
      vat,
      price,
      id,
      category_name,
      image,
      category_id,
      product_extras,price_after_tax
    } = props.route.params;
    image === `${BASE_URL}/image/default_product.png`
      ? setImage('')
      : setImage(image);
    setTitle(title);
    setVAT(vat);
    setPrice(is_tax_included == 0 ? price : price_after_tax);
    setId(id);
    setGroup(category_name);
    setCategoryID(category_id);
    setListSelectSubProduct(
      product_extras.map((item: {id: any; title: any}) => ({
        key: item.id,
        value: item.title,
      })),
    );
  }, []);
  //handle update data
  const options: rootInterface['ImageOptions'] = {
    title: 'Select Image',
    type: 'library',
    maxWidth: 200,
    maxHeight: 200,
    quality: 1,
    mediaType: 'photo',
    includeBase64: false,
    selectionLimit: 1,
  };

  //handle update images
  const addImageProduct = async () => {
    const images = await launchImageLibrary(options);
    const formdata: any = new FormData();
    formdata.append('image', {
      uri: images?.assets?.[0].uri ?? '',
      type: images?.assets?.[0].type ?? '',
      name: images?.assets?.[0].fileName ?? '',
    });
    formdata.append('id', idProduct);
    formdata.append('vat', vat);
    formdata.append('check', 0);
    formdata.append('title', title);
    formdata.append('quantity', 0);
    formdata.append('price', price);
    formdata.append('qtyitem', 0);
    formdata.append('category_id', selectedCategory?.key || categoryID);
    formdata.append('delete_image', 0);
    if (images.didCancel) {
      setImageUri(imageUri);
      setCheckValue(true);
    }
    if (images.assets) {
      setCheckValue(false);
      setImageUri(images?.assets?.[0].uri ?? '');
      setImage(images?.assets?.[0].uri ?? '');
    }
    setFromData(formdata);
  };

  const handleUpdateProduct = async () => {
    let i = 0;
    dispatch(setLoading({valid: true}));
    do {
      i++;
      const formdata: any = new FormData();
      formdata.append('id', idProduct);
      formdata.append('delete_image', image == '' ? 1 : 0);
      // formdata.append('ProductCode', null);
      formdata.append('title', title);
      formdata.append('price', price);
      formdata.append('vat', vat);
      formdata.append(
        'category_id',
        selectedCategory?.key.toString() || categoryID,
      );
      // formdata.append('type_commodity', 0);
      // formdata.append('is_extra', 0);
      // formdata.append('type_final_product', 1);
      // formdata.append('inventory_required', 0);
      // formdata.append('type_commodity', 0);
      // formdata.append('min_quantity', 0);
      // formdata.append('check', 0);
      try {
        const res = await fetch(`${BASE_URL}/api/admin/product/edit_product`, {
          method: 'post',
          headers: {
            Accept: 'application/json',
            'Content-Type': 'multipart/form-data',
            Authorization: `Bearer ${access_token}`,
          },
          body: image == null || imageUri == undefined ? formdata : fromData,
        });

        const json = await res.json();
        let obj: {message: string} = {message: ''};
        if (json.status_code === 200) {
          Alert.alert(messageApp, editProductSuccess);
          dispatch(setFlagProduct());
          navigation.pop(1);
          break;
        } else if (json.status_code === 204) {
          Alert.alert(messageApp, obj.message);
          break;
        } else if (json.status_code === 400) {
          Alert.alert(messageApp, editNotFullValid);
          break;
        } else if (json.status_code === 500) {
          Alert.alert(messageApp, networkIsError);
          break;
        } else {
          Alert.alert(messageApp, editProductFail);
        }
      } catch (err: unknown) {
        handleUpdateProduct();
        console.error(err);
      }
    } while (i <= 5);
    dispatch(setLoading({valid: false}));
  };

  const isInteger = (str: string) => {
    return /^\d+$/.test(str);
  };

  useEffect(() => {
    if (
      isInteger(price) == false &&
      parseInt(price) !== 0 &&
      price !== undefined
    ) {
      Alert.alert(messageApp, PriceOnlyInt);
    }
    (isInteger(vat) == false && parseInt(vat) !== 0 && vat !== undefined) ||
    parseInt(vat) >= 100 ||
    parseInt(vat) < 0
      ? Alert.alert(messageApp, alertVat)
      : null;
  }, [vat, price]);

  useEffect(() => {
    selectedCategory?.key ? setCheckValue(false) : setCheckValue(true);
  }, [selectedCategory?.key]);

  const handleDelete = async () => {
    try {
      handleDeleteProduct(idProduct, access_token).then(res => {
        const result = res.data.status_code;
        if (result === 200) {
          Alert.alert(messageApp, deleteProductSuccess);
          navigation.pop(1);
          dispatch(setFlagProduct());
        } else {
          Alert.alert(messageApp, deleteProductFail);
        }
      });
    } catch (err: unknown) {
      console.log(err);
    }
  };
  //handle go back screen
  const handleGoBack = () => {
    navigation.pop(1);
  };

  const ClearImage = () => {
    setImage('');
    setCheckValue(false);
  };
  const selectSubProduct = (value: any) => {
    setListSelectSubProduct(prevList => {
      if (!prevList.includes(value)) {
        setBtnUpdate(false);
        return [...prevList, value];
      }
      return prevList;
    });
  };
  const removeSubProduct = (value: any) => {
    setListSelectSubProduct(prevList => {
      setBtnUpdate(false);
      return prevList.filter(item => item.key !== value.key);
    });
  };
  const updateSubProduct = async () => {
    const request = await axios({
      method: 'post',
      url: `${BASE_URL}/api/admin/product/add_product_extra`,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${access_token}`,
      },
      data: {
        main_product_id: idProduct.toString(),
        extra_product_list:
          '[' + listSelectSubProduct.map(item => item.key).join(',') + ']',
      },
    });
    if (request.data.status_code === 200) {
      dispatch(fetchListProduct({access_token, messageApp}));
      setBtnUpdate(true);
      Alert.alert(messageApp, SuccessfullyAddedNewSideDish);
    } else {
      setBtnUpdate(true);
      Alert.alert(messageApp, FailedToDddNewSideDish);
    }
    return request;
  };
  return (
    <SafeAreaView style={HandleProductCss.container}>
      <KeyboardAwareScrollView
        contentContainerStyle={{flexGrow: 1}}
        keyboardShouldPersistTaps="handled">
        <View w_100 flDirectionRow>
          <View w_80 />
          <View w_20>
            <TouchableOpacity
              onPress={handleGoBack}
              style={HandleProductCss.btnBackScreen}>
              <Entypo name="log-out" size={40} color={maincolor} />
            </TouchableOpacity>
          </View>
        </View>

        <View>
          <View>
            <View>
              <Text fontSize20 fontWeight600 mainColor>
                {handleProductScreenTitle}
              </Text>
            </View>
            <View w_100>
              <DropDownComponents
                value={category_name || handleProductScreenTitle}
                data={listCategory}
                onSelect={setSelectedCategory}
                widthComponent={wp('98%')}
              />
            </View>
          </View>
          <View flDirectionRow jContentBetween>
            <View jContentCenter w_55>
              <Text fontSize20 fontWeight600 mainColor>
                {handleProductScreenName}:
              </Text>
            </View>
            <View w_40>
              <TextInput
                defaultValue={`${title}`}
                onChangeText={e => {
                  setTitle(e);
                  setCheckValue(false);
                }}
                style={[
                  HandleProductCss.inputNameProduct,
                  {
                    borderColor: maincolor,
                    color: blackcolor,
                  },
                ]}
                numberOfLines={1}
              />
            </View>
            <View w_5 />
          </View>
          <View flDirectionRow aItemsCenter jContentBetween w_100>
            <View w_55>
              <Text fontSize20 fontWeight600 mainColor>
                {handleProductScreenVat}:
              </Text>
            </View>
            <View flDirectionRow aItemsCenter jContentBetween w_45>
              <View w_35>
                <TextInput
                  defaultValue={`${vat}`}
                  keyboardType={'numeric'}
                  style={[
                    HandleProductCss.inputVat,
                    {
                      borderColor: maincolor,
                      color: blackcolor,
                      width: '100%',
                    },
                  ]}
                  onChangeText={e => {
                    setVAT(e);
                    setCheckValue(false);
                  }}
                />
              </View>
              <View w_10>
                <Text blackColor fontSize20>
                  %
                </Text>
              </View>
            </View>
          </View>
          <View flDirectionRow aItemsCenter w_100>
            <View w_55>
              <Text fontSize20 fontWeight600 mainColor>
                {componentProductPrice}{` `}
                {is_tax_included == 1
                  ? componentAddProductPrice
                  : componentAddProductPriceNotVAT}
                :
              </Text>
            </View>
            <View w_35>
              <TextInput
                defaultValue={`${price}`}
                keyboardType={'numeric'}
                style={[
                  HandleProductCss.inputPrice,
                  {
                    borderColor: maincolor,
                    color: blackcolor,
                  },
                ]}
                onChangeText={e => {
                  setPrice(e);
                  setCheckValue(false);
                }}
              />
            </View>
            <View w_10>
              <Text blackColor fontSize18>
                {currencySymbol}
              </Text>
            </View>
          </View>
          {props.route?.params?.is_extra == '0' ? (
            <>
              <View flDirectionRow aItemsCenter w_100>
                <View flDirectionRow aItemsCenter>
                  <Text fontSize18 fontWeight600 mainColor>
                    {handleProductScreenListSideDish}
                  </Text>
                </View>
              </View>
              {listSelectSubProduct ? (
                <View w_100 flDirectionRow aItemsCenter jContentBetween>
                  <View w_70>
                    <ScrollView
                      horizontal
                      style={{
                        width: '100%',
                        height: scale(40),
                        borderColor: maincolor,
                        borderWidth: 1,
                        borderRadius: 5,
                        padding: 5,
                        // margin: 5,
                        marginVertical: wp('1%'),
                      }}>
                      {listSelectSubProduct &&
                        listSelectSubProduct.map((item, index) => {
                          return (
                            <TouchableOpacity
                              key={index}
                              style={{
                                width: scale(80),
                                marginRight: scale(10),
                                backgroundColor: maincolor,
                                height: scale(30),
                                alignItems: 'center',
                                justifyContent: 'center',
                              }}
                              onPress={() => {
                                removeSubProduct(item);
                              }}>
                              <Text>{`${item.value.substring(0, 10)}...`}</Text>
                            </TouchableOpacity>
                          );
                        })}
                    </ScrollView>
                  </View>

                  <View
                    w_25
                    style={{
                      marginRight: wp('1%'),
                    }}>
                    <TouchableOpacity
                      style={[
                        {
                          backgroundColor: btnUpdate ? graycolor : maincolor,
                          borderRadius: scale(5),
                          width: '100%',
                          padding: scale(10),
                        },
                      ]}
                      onPress={updateSubProduct}
                      disabled={btnUpdate}>
                      <Text whiteColor textCenter>
                        {handleProductScreenUpdate}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ) : <></>}

              <DropDownComponents
                value={handleProductScreenTitle}
                data={listSubProduct}
                onSelect={selectSubProduct}
                widthComponent={wp('98%')}
              />
            </>
          ) : (
            <>
              <View flDirectionRow aItemsCenter>
                <View>
                  <Text fontSize20 fontWeight600 mainColor>
                    {productsScreenDishType}:
                  </Text>
                </View>
                <View>
                  <Text fontSize20>
                    {` `}
                    {productsScreenSideDish}
                  </Text>
                </View>
              </View>
            </>
          )}
        </View>

        {image === `${BASE_URL}/image/default_product.png` ? (
          ''
        ) : (
          <View aItemsCenter>
            {image == '' ? (
              <View
                style={{
                  height: hp('1%'),
                }}
              />
            ) : (
              <>
                {/* <View style={HandleProductCss.deleteImage}>
                  <TouchableOpacity onPress={ClearImage}>
                    <AntDesign name="closecircle" size={40} color={maincolor} />
                  </TouchableOpacity>
                </View> */}
                <Image
                  source={
                    imageUri !== undefined
                      ? {
                          uri: imageUri,
                        }
                      : {
                          uri: image,
                        }
                  }
                  style={HandleProductCss.imageProduct}
                  resizeMode="contain"
                />
              </>
            )}
          </View>
        )}

        <View>
          <TouchableOpacity
            onPress={addImageProduct}
            style={[
              HandleProductCss.addImage,
              {
                backgroundColor: maincolor,
              },
            ]}>
            <Text whiteColor fontWeight600 fontSize16>
              {image == ''
                ? handleProductScreenAddImage
                : handleProductScreenUpdateImage}
            </Text>
          </TouchableOpacity>
        </View>
        <View flDirectionRow jContentBetween>
          <TouchableOpacity
            onPress={handleUpdateProduct}
            style={[
              HandleProductCss.btnConfirm,
              {
                backgroundColor: checkValue ? graycolor : maincolor,
              },
            ]}
            disabled={checkValue}>
            <Text textCenter whiteColor fontSize16 fontWeight600>
              {handleProductScreenUpdate}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => setModalVisible(true)}
            style={[
              HandleProductCss.btnConfirm,
              {
                backgroundColor: maincolor,
              },
            ]}>
            <Text textCenter whiteColor fontSize16 fontWeight600>
              {handleProductScreenDelete}
            </Text>
          </TouchableOpacity>
        </View>
        <View flex1 jContentCenter aItemsCenter>
          <Modal animationType="fade" transparent={true} visible={modalVisible}>
            <View flex1 jContentCenter aItemsCenter>
              <View modalView style={HandleProductCss.modalView}>
                <Text
                  style={HandleProductCss.modalText}
                  textCenter
                  fontSize25
                  fontWeight600
                  mainColor>
                  {handleProductScreenDeleteConfirm}
                </Text>
                <View flDirectionRow>
                  <Pressable
                    style={[
                      HandleProductCss.btnDeleteProduct,
                      {
                        backgroundColor: maincolor,
                      },
                    ]}
                    onPress={() => handleDelete()}>
                    <Text whiteColor fontWeightBold textCenter>
                      {agree}
                    </Text>
                  </Pressable>
                  <Pressable
                    style={[
                      HandleProductCss.btnDeleteProduct,
                      {
                        backgroundColor: maincolor,
                      },
                    ]}
                    onPress={() => setModalVisible(!modalVisible)}>
                    <Text whiteColor fontWeightBold textCenter>
                      {cancel}
                    </Text>
                  </Pressable>
                </View>
              </View>
            </View>
          </Modal>
        </View>
      </KeyboardAwareScrollView>
      {loading ? (
        <View style={HandleProductCss.contentLoadding}>
          <ActivityIndicator size="large" color={maincolor} />
        </View>
      ) : null}
    </SafeAreaView>
  );
};

export default React.memo(HandleProduct);
