// /* eslint-disable react-native/no-inline-styles */
// import {
//   Text,
//   Image,
//   View,
//   StyleSheet,
//   ScrollView,
//   TouchableOpacity,
// } from 'react-native';
// //   import {Display} from '@utils';
// import React, {memo} from 'react';
// //   import {Colors} from '@constants';
// //   import {Categories} from '@constants';
// import {useDispatch} from 'react-redux';
// //   import {addFood} from '../redux/foodSlice';
// // import {useSelector} from 'react-redux';
// import {setHeight, setWidth} from '../../../utils/style/Reponsive';
// import {useAppSelector} from '../../../hooks';
// const ListCategories = () => {
//   const dispatch = useDispatch();
//   //   const indexCategory = useAppSelector((state: any) => state.food);
//   const handleAction = (key: any) => {
//     // console.log(key);
//     //   dispatch(addFood({id: index}));
//   };
//   const listCategory = useAppSelector(state => state.getData.listCategory);
//   console.log('listCategory');
//   return (
//     <ScrollView
//       horizontal
//       showsHorizontalScrollIndicator={false}
//       contentContainerStyle={styles.categoriesListContainer}>
//       {listCategory &&
//         listCategory.map((item: any) => (
//           <TouchableOpacity
//             key={item.key}
//             activeOpacity={0.8}
//             onPress={() => handleAction(item.key)}>
//             <View
//               style={{
//                 // backgroundColor:
//                 //   listCategory[0].key === item.key ? 'red' : 'blue',
//                 ...styles.categoryBtn,
//               }}>
//               <Text
//                 style={{
//                   fontSize: setWidth(7.8) / 2,
//                   fontWeight: 'bold',
//                   marginLeft: 10,
//                   color: listCategory[0].key === item.key ? 'yellow' : 'blue',
//                 }}>
//                 {item.value}
//               </Text>
//             </View>
//           </TouchableOpacity>
//         ))}
//     </ScrollView>
//   );
// };
// const styles = StyleSheet.create({
//   categoriesListContainer: {
//     alignItems: 'center',
//     // paddingHorizontal: 20,
//     // backgroundColor: 'yellow',
//   },
//   categoryBtn: {
//     height: setWidth(11),
//     width: setWidth(35),
//     marginRight: 7,
//     borderRadius: 30,
//     alignItems: 'center',
//     paddingHorizontal: 5,
//     flexDirection: 'row',
//     backgroundColor: 'gray',
//   },
//   categoryBtnImgCon: {
//     height: setHeight(3.6),
//     width: setHeight(3.6),
//     backgroundColor: 'white',
//     borderRadius: 30,
//     justifyContent: 'center',
//     alignItems: 'center',
//   },
// });
// export default memo(ListCategories);
