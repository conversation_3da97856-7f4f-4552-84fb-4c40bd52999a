import {
  Image,
  LogBox,
  StyleSheet,
  Platform,
  TouchableHighlight,
  Pressable,
} from 'react-native';
import {setWidth} from '../../../utils/style/Reponsive';
import {useAppSelector} from '../../../hooks';
import {Text, View} from '../../../Component/index';
import {languages} from '../../../../src/constants';

LogBox.ignoreLogs(['Sending...']);
import {memo, useCallback} from 'react';
interface ProductsScreen {
  [key: string]: string | undefined;
  productsScreenAddProduct?: string;
  productsScreenListCategories?: string;
  productsScreenListProducts?: string;
  productsScreenFindProduct?: string;
}
const Card = (props: any) => {
  const {navigation, item, i} = props;
  const listProduct = useAppSelector(state => state.getData.listProduct);
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {maincolor} = xmlData;
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const is_tax_included =
    useAppSelector(state => state.counter.infoAdmin?.admin?.store?.is_tax_included) || 0;
  const checkLangue = (): ProductsScreen | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: ProductsScreen = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    componentAddProductPrice,
    componentAddProductVat,
    componentAddProductPriceNotVAT,
    componentProductPrice,
  }: any = langData;
  const currencySymbol = useAppSelector(state => state.counter?.infoAdmin?.admin?.store?.currency_symbol) || "đ";
  return (
    <View
    // underlayColor="transparent"
    // activeOpacity={0.9}
    // onPress={handleGetProduct}
    >
      <View style={styles.card} flDirectionRow>
        <View w_35 aItemsCenter jContentCenter>
          <Image
            source={{
              uri: item.image,
            }}
            style={{
              height: setWidth(28),
              width: setWidth(28),
              borderRadius: 20,
              borderWidth: 3,
              borderColor: maincolor,
            }}
          />
        </View>
        <View w_65 jContentCenter style={{marginLeft: 15}}>
          <View>
            <Text
              style={{
                fontSize: setWidth(4.5),
                fontWeight: Platform.OS === 'ios' ? 'bold' : '800',
                color: maincolor,
                marginVertical: 2,
              }}>
              {item.title?.length < 14
                ? `${item.title}`
                : `${item.title.substring(0, 14)}...`}
            </Text>
            <Text
              style={{
                fontSize: setWidth(4.5),
                fontWeight: Platform.OS === 'ios' ? 'bold' : '800',
                color: maincolor,
                marginVertical: 2,
              }}>
              {item.category_name?.length < 18
                ? `${item.category_name}`
                : `${item.category_name?.substring(0, 18)}...`}
            </Text>
            <Text
              style={{
                fontSize: setWidth(4.5),
                fontWeight: 'bold',
                marginVertical: 2,
                color: 'black',
              }}>
              {componentAddProductVat}: {item.vat}%
            </Text>
            <Text
              style={{
                fontSize: setWidth(4.5),
                fontWeight: 'bold',
                marginVertical: 2,
                color: 'black',
              }}>
              {componentProductPrice} :{' '}
              {is_tax_included == 1 
                ? item.price_after_tax.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') 
                : item.price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              {currencySymbol}
            </Text>
            <Text
              style={{
                fontSize: setWidth(4.5),
                fontWeight: 'bold',
                color: 'black',
              }}>
              {is_tax_included == 1
                ? componentAddProductPrice
                : componentAddProductPriceNotVAT}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  card: {
    height: setWidth(35),
    width: '100%',
    marginTop: Platform.OS === 'ios' ? 15 : 20,
    borderRadius: 15,
    backgroundColor: 'white',
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 5,
  },
});
export default memo(Card);
