import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  ActivityIndicator,
  TextInput,
  TouchableOpacity
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';
import {
  ModalAddCategory,
  ModalAddItem,
  Text,
  View,
} from '../../Component/index';
import { ProductScreenCss } from './ProductScreenCss';
import DraggableFlatList, {
  RenderItemParams,
ScaleDecorator,
} from "react-native-draggable-flatlist";
import { StackNavigationProp } from '@react-navigation/stack';
import { ThunkDispatch } from '@reduxjs/toolkit';
import { widthPercentageToDP as wp } from 'react-native-responsive-screen';
import { useDispatch } from 'react-redux';
import Container from '../../Component/Container/Container';
import { languages } from '../../constants';
import { useAppSelector } from '../../hooks';
import { fetchListCategory, fetchListProduct } from '../../Redux/GetData';
import { setFlagProduct, setStateShowOption } from '../../Redux/Slide';
import { scale } from '../../utils/style/Reponsive';
import Cart from './components/Cart';
import { sortRankProduct } from '../../api/handleApi';
import {debounce, set} from 'lodash';
interface RootStackParamList {
  [key: string]: {};
}
interface IProps {
  navigation: StackNavigationProp<RootStackParamList, 'HandleProduct'>;
}    interface SortRankItem {
  id: string;
  sort_rank: string;
}

interface SortRanks {
  sortedData: SortRankItem[];
}
interface ProductsScreen {
  [key: string]: string | undefined;
  productsScreenAddProduct?: string;
  productsScreenListCategories?: string;
  productsScreenListProducts?: string;
  productsScreenFindProduct?: string;
  componentChangeProductImage?: string;
}
const ProductScreen = (props: IProps) => {
  const {navigation} = props;
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {maincolor, blackcolor, modalbackground} = xmlData;
  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const [searchProduct, setSearchProduct] = useState('');
  const debouncedSearch = useCallback(
    debounce((value) => {
    dispatch(fetchListProduct({access_token, messageApp}));
    setSearchProduct(value);
    }, 500), 
    [] 
  );
  const [loading, setLoading] = useState<boolean>(false);
  const {access_token, flagProduct, nameSelectOpition} = useAppSelector(
    state => state.counter,
  );
  const listProduct = useAppSelector(state => state.getData.listProduct)|| [];
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): ProductsScreen | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: ProductsScreen = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    productsScreenAddProduct,
    productsScreenListCategories,
    productsScreenListProducts,
    productsScreenFindProduct,
    messageApp,
  }: any = langData;
  const handleGetProduct = useCallback(
  
    (index: number) => {
      navigation.navigate('HandleProduct', listProduct[index]);
    },
    [listProduct, navigation],
  );

  const addProduct = useCallback(() => {
    dispatch(setStateShowOption({valid: false, nameOpiton: 'AddProduct'}));
    refreshScreen()
  }, [dispatch]);

  const addCategory = useCallback(() => {
    dispatch(setStateShowOption({valid: false, nameOpiton: 'AddCategory'}));
refreshScreen()
  }, [dispatch]);

  //get Product and list Category
  useEffect(() => {
    dispatch(fetchListCategory({access_token, messageApp}));
    dispatch(fetchListProduct({access_token, messageApp}));
  }, [flagProduct, dispatch, access_token]);

  const refreshScreen = useCallback(() => {
    setLoading(true);
    const timeId = setTimeout(() => {
      dispatch(setFlagProduct());
      setLoading(false);
    }, 1500);

    return () => clearTimeout(timeId);
  }, [dispatch, access_token]);
  const NUM_ITEMS = listProduct.length || 0;
  
  type Item = {
    key: any;
    label: any;
    id: string;
  };
  
  const initialData: Item[] = [...Array(NUM_ITEMS)].map((d, index) => {
    return {
      key: `item-${index}`,
      label: String(++index) + "",
      id: `item-${index}`,
    };
  });
  const RenderCardItem = () => {
    const filteredData = useMemo(() => {
      const mappedData = listProduct.map((item: { id: any; }, index: any) => ({
        key: `item-${index}`,
        ...item,
      }));
      if (!searchProduct) return mappedData;

    return mappedData.filter((item: { name: string; searchTitle: string; title:string }) =>
      item.searchTitle?.toLowerCase().includes(searchProduct.toLowerCase()) ||
      item.title?.toLowerCase().includes(searchProduct.toLowerCase())
    );
  }, [listProduct, searchProduct]);
    const [data, setData] = useState(initialData);
    useEffect(() => {
      const timeoutId = setTimeout(() => {
        setData(filteredData);
      }, 1000);
      return () => clearTimeout(timeoutId);
    }, [filteredData]);
    
    const stableListProduct = useMemo(() => listProduct, [listProduct]);
    const debounceSetSortedData = useCallback((updatedSortRanks: SortRanks) => {
      setTimeout(() => {
        sortRankProduct(access_token, updatedSortRanks);
      }, 1000);
    }, [access_token]);
    
    const renderItem = 
      ({item, drag, isActive}: RenderItemParams<Item>) => {
        // const originalIndex = stableListProduct.findIndex(
        //   (product: { id: string; }) => product.id === item.id
        // )
        
        return (
          <ScaleDecorator>
            <TouchableOpacity
              onLongPress={drag}
              disabled={isActive}
              onPress={() => handleGetProduct(item.key.split('-')[1])}>
              <Cart
                navigation={navigation}
                item={stableListProduct[item.key.split('-')[1]]}
                i={item.key.split('-')[1]}
                drag={drag}
                isActive={isActive}
              />
            </TouchableOpacity>
          </ScaleDecorator>
        );
      }

    return (
      <DraggableFlatList
        data={data}
        onDragEnd={({data: newData}) => {
          setData(newData);
          const updatedSortRanks: SortRanks = {
            sortedData: newData.map((item: Item, index: number) => {
              const itemIndex: string = item.key.split('-')[1];
              const id: string = stableListProduct[itemIndex]?.id || '';
              return {
                id: id.toString(),
                sort_rank: (index + 1).toString(),
              };
            }),
          };
          debounceSetSortedData(updatedSortRanks);
        }}
        keyExtractor={item => item.key}
        renderItem={renderItem}
      />
    );
  }

  
  return (
    <Container>
      <View flex1 style={{
        marginHorizontal:wp('1%')
      }}>
        <View flDirectionRow jContentBetween>
          <TouchableOpacity
            onPress={addProduct}
            style={[
              ProductScreenCss.btnHandleProduct,
              {
                backgroundColor: maincolor,
              },
            ]}>
            <Text textCenter fontSize16 fontWeight600 whiteColor>
              {productsScreenAddProduct}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={addCategory}
            style={[
              ProductScreenCss.btnHandleProduct,
              {
                backgroundColor: maincolor,
              },
            ]}>
            <Text textCenter fontSize16 fontWeight600 whiteColor>
              {productsScreenListCategories}
            </Text>
          </TouchableOpacity>
          {/* <View jContentCenter>
            <SimpleLineIcons
              name="refresh"
              size={scale(44)}
              color={maincolor}
              onPress={refreshScreen}
            />
          </View> */}
        </View>
        <View>
          <Text fontSize28 textCenter fontWeight700 mainColor>
            {productsScreenListProducts}
          </Text>
        </View>
        <View flex1 aItemsCenter>
          <View
            flDirectionRow
            w_100
            borderBlackColor
            borderWidth1
            mTop8
            bRadius5
            aItemsCenter
            style={{
              width:wp('98%')
            }}
            >
            <Ionicons
              name="search"
              size={34}
              style={ProductScreenCss.iconSearch}
              color={blackcolor}
            />
            <TextInput
              placeholder={productsScreenFindProduct}
              onChangeText={(text) => debouncedSearch(text)}
              placeholderTextColor={blackcolor}
              style={{
                color: blackcolor,
                width: wp('90%'),
                paddingVertical: scale(10),
              }}
            />
          </View>
          <RenderCardItem />
        </View>
      </View>
    
      {nameSelectOpition === 'AddProduct' ? (
        <View
          style={[
            ProductScreenCss.modalOption,
            {
              backgroundColor: modalbackground,
            },
          ]}>
          <ModalAddItem />
        </View>
      ) : null}
      {nameSelectOpition === 'AddCategory' ? (
        <View
          style={{
            backgroundColor: modalbackground,
            position: 'absolute',
            top: 0,
            bottom: 0,
            right: 0,
            left: 0,
            justifyContent: 'center',
          }}>
          <ModalAddCategory />
        </View>
      ) : null}

      {loading ? (
        <View
          style={[
            ProductScreenCss.loadingView,
            {
              backgroundColor: modalbackground,
            },
          ]}>
          <ActivityIndicator size="large" color={blackcolor} />
        </View>
      ) : null}
    </Container>

  );
};

export default React.memo(ProductScreen);
