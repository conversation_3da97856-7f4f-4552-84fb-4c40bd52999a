import { StyleSheet } from 'react-native';
import { scale } from '../../utils/style/Reponsive';
import { heightPercentageToDP as hp , widthPercentageToDP as wp} from 'react-native-responsive-screen'

export const ProductScreenCss = StyleSheet.create({
  SafeAreaViewContainer: {
    flex: 1,
  },

  iconSearch: {
    paddingHorizontal: scale(16),
  },
  category: {
    width: '100%',
  },
  modalOption: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    right: 0,
    left: 0,
    justifyContent: 'center',
  },
  viewProduct: {
    width: '50%',
    // backgroundColor: 'red'
  },
  btnHandleProduct: {
    marginVertical: scale(2),
    height: scale(48),
    width: wp('48%'),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scale(10),
  },

  imgProduct: {
    width: scale(110),
    height: scale(110),
    borderRadius: scale(8),
  },
  loadingView: {
    position: 'absolute',
    justifyContent: 'center',
    alignContent: 'center',
    top: 0,
    bottom: 0,
    right: -scale(20),
    left: -scale(20),
  },
});
