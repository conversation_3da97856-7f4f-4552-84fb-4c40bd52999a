import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Icon from 'react-native-vector-icons/MaterialIcons';

import {
  HomeScreen,
  TableScreen,
  ChartScreen,
  ProductScreen,
  StaffScreen,
} from '../../Screen/index';

import {useAppSelector} from '../../hooks';
import {scale, setWidth} from '../../utils/style/Reponsive';
import {Platform} from 'react-native';
import DeviceInfo from 'react-native-device-info';

const Tab = createBottomTabNavigator();

export default function BottomHomeTab() {
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {maincolor} = xmlData;
  const size: number = scale(32);
    // Check if the device is an iPhone 15 or newer
    const isIphone13OrNewer = () => {
      const model = DeviceInfo.getModel();
      if (Platform.OS === 'ios' && model.startsWith('iPhone')) {
        const modelNumber = parseInt(model.replace('iPhone', ''), 10);
        return modelNumber >= 11;
      }
      return false;
    };
  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: maincolor,
        headerShown: false,
        tabBarShowLabel: false,
        tabBarStyle: [
          {
            display: 'flex',
            paddingTop: `${scale(0.1)}%`,
            // height: `${scale(5.5)}%`,
            // minHeight: isIphone13OrNewer() ? scale(70) : 40, // Conditionally set minHeight
            // backgroundColor:'red'
          },
        ],
      }}>
      <Tab.Screen
        name="HomeScreen"
        component={HomeScreen}
        options={{
          tabBarLabel: 'Quản lý',
          headerShown: false,
          tabBarIcon: ({color}) => (
            <Ionicons name="home" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="ProductScreen"
        component={ProductScreen}
        options={{
          tabBarLabel: 'Sản phẩm',
          tabBarIcon: ({color}) => (
            <Ionicons name="restaurant-outline" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="ChartScreen"
        component={ChartScreen}
        options={{
          tabBarLabel: 'Doanh thu' ,
          tabBarIcon: ({color}) => (
            <Ionicons name="bar-chart-outline" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="TableScreen"
        component={TableScreen}
        options={{
          tabBarLabel: 'Bàn',
          tabBarIcon: ({color}) => (
            <MaterialCommunityIcons
              name="table-chair"
              size={size}
              color={color}
            />
          ),
        }}
      />
      <Tab.Screen
        name="StaffScreen"
        component={StaffScreen}
        options={{
          tabBarLabel: 'Nhân viên',
          tabBarIcon: ({color}) => (
            <Icon name="supervised-user-circle" size={size} color={color} />
          ),
        }}
      />
    </Tab.Navigator>
  );
}
