import {TouchableOpacity} from 'react-native';
import {Text, View} from '../../../Component/index';
import {useAppSelector} from '../../../hooks';
import React from 'react';

type Props = {
  item: {
    user_name: string;
    id: string;
    dayPayment: string;
    reason: string;
    valuetotal: number;
    paymentNotChange: number;
    modalCancelBill: boolean;
  };
  setIdBillDelete: any;
  setModalCancelBill: any;
  setModalVisible: any;
  handleDeleteBill: any;
  showModal: any;
  modalCancelBill: any;
};

const ItemDetailPayment = (item: Props) => {
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {
    fieldIsBlank,
    minValidPassword,
    minValidMessagePassword,
    maxValidPassword,
    maxValidMessagePassword,
    confirmPasswordMessage,
    messageApp,
    agree,
    cancel,
    changeInfoStaffFail,
    changeInfoStaffSuccess,
    changePasswordFail,
    changePasswordSuccess,
    deleteStaffFail,
    deleteStaffSuccess,
    phoneNumberExist,
    phoneNumberNotValid,
    notFullValidAddress,
    notFullValidNameStaff,
    maincolor,
    graycolor,
    blackcolor,
    backgroundcolor,
    modalbackground,
  } = xmlData;
  const {user_name, id, dayPayment, reason, paymentNotChange, valuetotal} =
    item.item;
  const {
    setIdBillDelete,
    setModalCancelBill,
    handleDeleteBill,
    setModalVisible,
    showModal,
    modalCancelBill,
  } = item;
  const isLoadingScreen = useAppSelector(
    state => state.counter.isLoadingScreen,
  );
  return (
    <View mVertical6 bRadius8 p_10 borderMainColor borderWidth2>
      <View flDirectionRow jContentBetween>
        <View>
          <Text fontSize16 blackColor>
            {user_name}
          </Text>
        </View>
        <View>
          <Text fontSize16 blackColor>
            {id}
          </Text>
        </View>
      </View>
      <View flDirectionRow w_100>
        <View w_50>
          <Text fontSize16 blackColor>
            {dayPayment}
          </Text>
        </View>
        <View w_50 aItemsFlexEnd>
          <TouchableOpacity
          // onPress={() => {
          //   setIdBillDelete(id);
          //   setModalCancelBill(!modalCancelBill);
          // }}
          >
            <Text redText fontWeight700 fontSize18>
              Huỷ hoá đơn này
            </Text>
          </TouchableOpacity>
        </View>
        {/* <Modal
          animationType="fade"
          transparent={true}
          // visible={modalCancelBill}
        >
          <View flex1 aItemsCenter jContentCenter>
            <View
              style={{
                width: '80%',
                margin: scale(20),
                backgroundColor: 'white',
                borderRadius: scale(20),
                padding: scale(32),
                alignItems: 'center',
                shadowOffset: {
                  width: 0,
                  height: 2,
                },
                shadowOpacity: 0.25,
                shadowRadius: 4,
                elevation: 5,
                shadowColor: blackcolor,
              }}>
              <Text
                style={{
                  marginVertical: 7,
                }}
                fontSize22
                fontWeight600
                mainColor>
                Xác nhận huỷ hoá đơn
              </Text>
              <View flDirectionRow>
                <Pressable
                  style={{
                    borderRadius: scale(20),
                    padding: scale(10),
                    elevation: 2,
                    marginHorizontal: scale(5),
                    paddingHorizontal: 32,
                    backgroundColor: maincolor,
                  }}
                  // onPress={handleDeleteBill}
                >
                  <Text whiteColor fontWeightBold textCenter>
                    {agree}
                  </Text>
                </Pressable>
                <Pressable
                  style={{
                    borderRadius: scale(20),
                    padding: scale(10),
                    elevation: 2,
                    marginHorizontal: scale(5),
                    paddingHorizontal: 32,
                    backgroundColor: maincolor,
                  }}
                  // onPress={() => setModalCancelBill(!modalCancelBill)}
                >
                  <Text whiteColor fontWeightBold textCenter>
                    {cancel}
                  </Text>
                </Pressable>
              </View>
            </View>
            {isLoadingScreen ? (
              <View
                style={{
                  position: 'absolute',
                  justifyContent: 'center',
                  alignContent: 'center',
                  top: -scale(20),
                  bottom: 0,
                  right: -scale(20),
                  left: -scale(20),
                  backgroundColor: modalbackground,
                }}>
                <ActivityIndicator size="large" color={maincolor} />
              </View>
            ) : null}
          </View>
        </Modal> */}
      </View>

      <View>{/* <Pressable onPress={() => setModalVisible(true)} /> */}</View>
      <View flDirectionRow>
        <View>
          <Text fontSize16 blackColor>
            Tình trạng hoá đơn:
          </Text>
        </View>
        <View>
          <Text fontSize16 blackColor>
            {reason == '0' ? (
              ' Không có thay đổi'
            ) : (
              <Text redText fontWeight700>
                {' '}
                Đã thay đổi
              </Text>
            )}
          </Text>
        </View>
      </View>

      <View flDirectionRow>
        <View>
          <Text fontSize16 blackColor>
            Lý do thay đổi:
          </Text>
        </View>
        <View>
          <Text fontSize16 blackColor>
            {reason == '0' ? null : (
              <>
                <Text redText fontWeight700>
                  {' '}
                  {reason}
                </Text>
              </>
            )}
          </Text>
        </View>
      </View>

      <View flDirectionRow>
        <View>
          <Text fontSize16 blackColor>
            Tổng tiền chưa bao gồm VAT:{'  '}
          </Text>
        </View>
        <View>
          <Text redText fontWeight800 fontSize16>
            {reason == '0'
              ? paymentNotChange &&
                paymentNotChange
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              : paymentNotChange &&
                paymentNotChange
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            {reason == '0' ? ' đ' : ' đ'}
          </Text>
        </View>
      </View>

      <View flDirectionRow jContentBetween aItemsCenter>
        <View>
          <Text mainColor fontWeight700 fontSize18>
            <Text redText fontSize16>
              Khách trả:{' '}
            </Text>
            {valuetotal &&
              valuetotal.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}{' '}
            đ
          </Text>
        </View>
        <TouchableOpacity
        // onPress={() => {
        //   showModal(item);
        // }}
        >
          <Text mainColor fontWeight700 fontSize18>
            Chi tiết hoá đơn
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default React.memo(ItemDetailPayment);
