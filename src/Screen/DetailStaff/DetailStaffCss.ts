import { Platform, StyleSheet } from 'react-native';
import { heightWindow, scale, widthWindow } from '../../utils/style/Reponsive';

export const DetailStaffCss = StyleSheet.create({
  container: {
    flex: 1,
    marginHorizontal: scale(6),
    marginTop: scale(15),
  },
  modalView: {
    width: '80%',
    margin: scale(20),
    backgroundColor: 'white',
    borderRadius: scale(20),
    padding: scale(32),
    alignItems: 'center',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  btnConfirm: {
    borderRadius: scale(20),
    padding: scale(10),
    elevation: 2,
    marginHorizontal: scale(5),
    paddingHorizontal: 32,
  },
  modalTitle: {
    marginVertical: 7,
  },
  btnChangePassword: {
    height: scale(48),
    justifyContent: 'center',
    borderRadius: 10,
    width: '50%',
  },
  btnChangeStatus: {
    width: '100%',
    height: scale(40),
    borderRadius: scale(8),
  },

  inputStaffName: {
    width: '100%',
    borderBottomWidth: 1,
    fontSize: scale(20),
    paddingVertical: scale(4),
    paddingLeft: 8
  },
  inputAddressStaff: {
    width: '100%',
    borderBottomWidth: 0.7,
    fontSize: scale(20),
    paddingVertical: scale(4),
    paddingHorizontal: scale(8),
  },
  inputNumberStaff: {
    width: '100%',
    borderBottomWidth: 0.7,
    fontSize: scale(20),
    paddingVertical: scale(4),
    paddingHorizontal: scale(8),
  },

  btnSortPayment: {
    width: '95%',
    justifyContent: 'center',
    height: scale(32),
    borderRadius: scale(8),
  },

  viewSearchBill: {
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: '#000',
    borderWidth: 1,
    width: '100%',
    marginTop: scale(10),
    borderRadius: scale(5),
  },
  iconSearchBill: {
    paddingHorizontal: scale(8),
  },
  btnUpdateInfo: {
    height: scale(48),
    width: widthWindow * 0.45,
    // alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scale(10),
  },
  btnDeleteStaff: {
    height: scale(48),
    width: widthWindow * 0.45,
    // alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scale(10),
  },
  viewLoading: {
    position: 'absolute',
    justifyContent: 'center',
    alignContent: 'center',
    top: -scale(20),
    bottom: 0,
    right: -scale(20),
    left: -scale(20),
  },
  showDetailPayment: {
    position: 'absolute',
    justifyContent: 'center',
    alignContent: 'center',
    top: -scale(20),
    bottom: 0,
    right: -scale(20),
    left: -scale(10),
  },
  modalChangePassword: {
    backgroundColor: 'white',
    borderRadius: scale(20),
    padding: scale(20),
    marginTop: heightWindow * 0.2,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    ...Platform.select({
      ios: {
        marginBottom: scale(10),
      },
    }),
  },
  inputChangePassword: {
    flexDirection: 'row',
    borderWidth: 0.7,

    borderRadius: scale(5),
    height: scale(38),
    marginVertical: scale(4),
    ...Platform.select({
      ios: {
        paddingHorizontal: scale(10),
      },
    }),
  },
  view90Persent: {
    width: '90%',
    padding: scale(8),
  },
  footerConfirmPassword: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  btnConfirmPassWord: {
    width: '40%',
    height: scale(44),
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: scale(6),
    borderRadius: scale(8),
  },
});
