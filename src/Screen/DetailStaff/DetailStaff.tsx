import React, {useCallback, useEffect, useState} from 'react';
import {
  ActivityIndicator,
  Alert,
  FlatList,
  Keyboard,
  Modal,
  Platform,
  Pressable,
  SafeAreaView,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import {useDispatch} from 'react-redux';
import {Formik} from 'formik';
import * as Yup from 'yup';

import SelectDropdown from 'react-native-select-dropdown';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Entypo from 'react-native-vector-icons/Entypo';
import {Text, View, DetailPaymentStaff} from '../../Component/index';

import {DetailStaffCss} from './DetailStaffCss';
import {
  getDetailPaymentStaff,
  handleDeleleStaff,
  handleUpdatePassword,
  handleUpdateStaff,
  deleteBillPayment,
} from '../../api/handleApi';
import {
  setFlagStaff,
  setLoadingScreen,
  setStateShowOption,
} from '../../Redux/Slide';
import {useAppSelector} from '../../hooks';
import {ThunkDispatch} from '@reduxjs/toolkit';
import {scale} from '../../utils/style/Reponsive';
import {languages} from '../../constants';
import { heightPercentageToDP as hp , widthPercentageToDP as wp} from 'react-native-responsive-screen'

interface IProps {
  navigation: {
    pop: (count?: number) => void;
  };
  route: any;
}
interface IHandleProduct {
  [key: string]: string | undefined;
  handleProductScreenTitle?: string;
  handleProductScreenName?: string;
  handleProductScreenVat?: string;
  handleProductScreenPrice?: string;
  handleProductScreenAddImage?: string;
  handleProductScreenUpdateImage?: string;
  handleProductScreenUpdate?: string;
  handleProductScreenDelete?: string;
  handleProductScreenDeleteConfirm?: string;
  agree?: string;
  cancel?: string;
}

const DetailStaff = (props: IProps) => {
  const [id, setId] = useState<number>(0);
  const [name, setName] = useState<string>('');
  const [status, setStatus] = useState<number>();
  const [phone, setPhone] = useState<string>('');
  const [address, setAddress] = useState<string>('');
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [modalCancelBill, setModalCancelBill] = useState<boolean>(false);
  const [modalVisiblePassword, setModalVisiblePassword] =
    useState<boolean>(false);
  const [eye, setEye] = useState<boolean>(true);
  const [eyeConfim, setEyeConfim] = useState<boolean>(true);
  const [confirmButton, setConfirmButton] = useState<boolean>(true);
  const [detailPayment, setDetailPayment] = useState([]);
  const [showDetailPayment, setShowDetailPayment] = useState<boolean>(false);
  const [showPaymentDeleted, setShowPaymentDeleted] = useState<boolean>(false);

  const [detailPaymentStaff, setDetailPaymentStaff] = useState();
  const [detailPaymentDeleted, setDetailPaymentDeleted] = useState<any>();
  const [idBillDelete, setIdBillDelete] = useState<string>('');
  const [searchProduct, setSearchProduct] = useState<string>('');
  const [check, setCheck] = useState<boolean>(true);

  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const valueProps = props.route.params;
  const {navigation} = props;
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): IHandleProduct | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: IHandleProduct = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };

  const langData = checkLangue();
  const {
    agree,
    cancel,
    detailStafScreenChangePass,
    staffScreenNameStaff,
    staffScreenAddressStaff,
    staffScreenPhoneStaff,
    staffScreenPasswordStaff,
    staffScreenPasswordConfirmStaff,
    staffScreenStatusStaffWork,
    confirmPasswordMessage,
    detailStafScreenHidestatistics,
    fieldIsBlank,
    minValidMessagePassword,
    maxValidMessagePassword,
    detailStafScreenShowstatistics,
    detailStafScreenStaffNoRevenue,
    detailStafScreenStaffArrangeBill,
    detailStafScreenStaffHideBillDeleted,
    detailStafScreenStaffShowBillDeleted,
    detailStafScreenStaffFindBill,
    detailStafScreenStaffPaidBill,
    detailStafScreenStaffDeleteBill,
    detailStafScreenStaffDeleteBillConfirm,
    detailStafScreenStaffInvoiceStatus,
    detailStafScreenStaffNoChange,
    detailStafScreenStaffChanged,
    detailStafScreenStaffChangedReason,
    detailStafScreenStaffPriceNoVAT,
    detailStafScreenStaffPaymented,
    detailStafScreenStaffDetailBill,
    detailStafScreenStaffDayCreated,
    detailStafScreenStaffDayDeleted,
    detailStafScreenStaffTotalRevenue,
    detailStafScreenStaffUpdateInfo,
    detailStafScreenStaffDeleteStaff,
    detailStafScreenStaffDeleteStaffConfirm,
    detailStafScreenStaffUpdate,
    detailStafScreenStaffDeleteSuccess,
    detailStafScreenStaffDeleteFail,
    messageApp,
    changeInfoStaffFail,
    changeInfoStaffSuccess,
    changePasswordFail,
    changePasswordSuccess,
    deleteStaffFail,
    deleteStaffSuccess,
    phoneNumberExist,
    phoneNumberNotValid,
    notFullValidAddress,
    notFullValidNameStaff,
    highestRevenue,
    lowestRevenue,
    latestInvoice,
    oldestInvoice,
    onlineStatus,
    offlineStatus,
    stopStatus,
  }: any = langData;
  const {
    maincolor,
    graycolor,
    blackcolor,
    backgroundcolor,
    modalbackground,
    minValidPassword,
    maxValidPassword,
  } = xmlData;
const g = useAppSelector(state => state.counter?.infoAdmin?.admin?.store?.currency_symbol) || "đ";
  const SignupSchema = Yup.object().shape({
    password: Yup.string()
      .min(minValidPassword, minValidMessagePassword)
      .max(maxValidPassword, maxValidMessagePassword)
      .required(fieldIsBlank),
    confirmPassWord: Yup.string()
      .min(minValidPassword, minValidMessagePassword)
      .max(maxValidPassword, maxValidMessagePassword)
      .required(fieldIsBlank)
      .oneOf([Yup.ref('password')], confirmPasswordMessage),
  });
  const {access_token, nameSelectOpition} = useAppSelector(
    state => state.counter,
  );
  const isLoadingScreen = useAppSelector(
    state => state.counter.isLoadingScreen,
  );
  const countries = [onlineStatus, offlineStatus, stopStatus];
  const searchPayment = [
    highestRevenue,
    lowestRevenue,
    latestInvoice,
    oldestInvoice,
  ];
  useEffect(() => {
    const {name, status, phone, address, id} = valueProps;
    setName(name);
    setStatus(status);
    setPhone(phone);
    setAddress(address);
    setId(id);
    getPaymentStaff(id);
  }, [valueProps]);
  const getPaymentStaff = async (id: number) => {
    dispatch(setLoadingScreen({valid: true}));
    try {
      getDetailPaymentStaff(id, access_token).then(res => {
        function extractDateTime(updated_at: string) {
          const dateTime = new Date(updated_at);

          const day = dateTime.getDate().toString().padStart(2, '0');
          const month = (dateTime.getMonth() + 1).toString().padStart(2, '0');
          const year = dateTime.getFullYear();
          const hours = dateTime.getHours().toString().padStart(2, '0');
          const minutes = dateTime.getMinutes().toString().padStart(2, '0');
          const seconds = dateTime.getSeconds().toString().padStart(2, '0');

          const datePart = `${day}-${month}-${year}`;
          const timePart = `${hours}:${minutes}:${seconds}`;

          return {
            date: datePart,
            time: timePart,
          };
        }
        setDetailPayment(
          res.data.payment.filter(
            (item: {status: number}) => item.status !== -1,
          ),
        );
        setDetailPaymentDeleted(
          res.data.payment.filter(
            (item: {status: number}) => item.status === -1,
          ),
        );

        res.data.payment.forEach(
          (item: {
            items: string;
            paymentNotChange: number;
            dayPayment: string;
            hourPayment: string;
            created_at: string;
            timeDeletePayment: string;
            updated_at: string;
          }) => {
            // let paymentNotChange = JSON.parse(item.items);
            const jsonObject = JSON.parse(item.items);
            const itemArray: any = Object.values(jsonObject.item);
            let paymentNotChangeCopy = itemArray;
            let result = paymentNotChangeCopy.reduce(
              (
                total: number,
                currentItem: {price: number; quantity: number},
              ) => {
                return total + currentItem.price * currentItem.quantity;
              },
              0,
            );
            item.paymentNotChange = result;
            item.dayPayment = extractDateTime(item.created_at).date;
            item.hourPayment = extractDateTime(item.created_at).time;
            item.timeDeletePayment = extractDateTime(item.updated_at).date;
          },
        );
      });
    } catch (err: unknown) {
      console.log(err);
    } finally {
      dispatch(setLoadingScreen({valid: false}));
    }
  };
  const handleUpdate = async (id: number) => {
    await dispatch(setLoadingScreen({valid: true}));
    try {
      await handleUpdateStaff(
        id,
        name,
        address,
        phone,
        status,
        access_token,
      ).then(res => {
        const result = res.data.status_code;
        switch (result) {
          case 200:
            Alert.alert(messageApp, changeInfoStaffSuccess);
            break;
          case 404:
            Alert.alert(messageApp, phoneNumberExist);
            break;
          case 400:
            if (res.data.message.phone) {
              Alert.alert(messageApp, phoneNumberNotValid);
            } else if (res.data.message.address) {
              Alert.alert(messageApp, notFullValidAddress);
            } else if (res.data.message.name) {
              Alert.alert(messageApp, notFullValidNameStaff);
            }
            break;
          default:
            Alert.alert(messageApp, changeInfoStaffFail);
            break;
        }
      });
    } catch (err: unknown) {
      console.log(err);
    } finally {
      await dispatch(setLoadingScreen({valid: false}));
      dispatch(setFlagStaff());
      setConfirmButton(current => !current);
    }
  };
  const changePassword = async (id: number, values: {password: string}) => {
    dispatch(setLoadingScreen({valid: true}));
    try {
      const {password} = values;
      handleUpdatePassword(id, password, access_token).then(res => {
        const {status_code} = res.data;
        if (status_code === 200 || status_code === 400) {
          Alert.alert(
            messageApp,
            status_code === 200 ? changePasswordSuccess : changePasswordFail,
          );
          dispatch(setFlagStaff());
          navigation.pop();
        }
      });
    } catch (err: unknown) {
      console.log(err);
    } finally {
      dispatch(setLoadingScreen({valid: false}));
    }
  };
  const handleDelete = async (id: number) => {
    await dispatch(setLoadingScreen({valid: true}));
    try {
      handleDeleleStaff(id, access_token).then(res => {
        const {status_code} = res.data;
        if (status_code === 200) {
          navigation.pop();
          Alert.alert(messageApp, deleteStaffSuccess);
          dispatch(setFlagStaff());
        } else if (status_code === 201) {
          Alert.alert(messageApp, deleteStaffFail);
        }
      });
    } catch (err: unknown) {
      console.log(err);
    } finally {
      setModalVisible(!modalVisible);
      dispatch(setLoadingScreen({valid: false}));
    }
  };
  const handleDeleteBill = async () => {
    await dispatch(setLoadingScreen({valid: true}));
    try {
      deleteBillPayment(idBillDelete, access_token).then(res => {
        const {status_code} = res.data;
        if (status_code === 200) {
          navigation.pop();
          Alert.alert(messageApp, detailStafScreenStaffDeleteSuccess);
        } else {
          Alert.alert(messageApp, detailStafScreenStaffDeleteFail);
        }
      });
    } catch (err: unknown) {
      console.log(err);
    } finally {
      setModalCancelBill(!modalCancelBill);
      dispatch(setLoadingScreen({valid: false}));
    }
  };
  const handleGoback = useCallback(() => {
    navigation.pop(1);
  }, [navigation]);

  const setChangeName = useCallback((text: React.SetStateAction<string>) => {
    setName(text);
    setConfirmButton(false);
  }, []);

  const setChangeAddress = useCallback((text: React.SetStateAction<string>) => {
    setAddress(text);
    setConfirmButton(false);
  }, []);

  const setChangePhone = useCallback((number: React.SetStateAction<string>) => {
    setPhone(number);
    setConfirmButton(false);
  }, []);

  const showModal = (item: React.SetStateAction<undefined>) => {
    dispatch(
      setStateShowOption({valid: false, nameOpiton: 'DetailPaymentStaff'}),
    );
    setDetailPaymentStaff(item);
  };

  let total =
    detailPayment &&
    detailPayment.reduce(
      (
        accumulator: number,
        element: {
          valuetotal: number;
        },
      ) => {
        return accumulator + element.valuetotal;
      },
      0,
    );

  //handle sort detail payment
  const SortTypes = {
    PAYMENT_MAX: highestRevenue,
    PAYMENT_MIN: lowestRevenue,
    PAYMENT_MAX_DAY: latestInvoice,
    PAYMENT_MIN_DAY: oldestInvoice,
  };
  const selectItem = (selectedItem: string) => {
    switch (selectedItem) {
      case SortTypes.PAYMENT_MAX:
        sortPaymentMax();
        break;
      case SortTypes.PAYMENT_MIN:
        sortPaymentMin();
        break;
      case SortTypes.PAYMENT_MAX_DAY:
        sortPaymentMaxDay();
        break;
      case SortTypes.PAYMENT_MIN_DAY:
        sortPaymentMinDay();
        break;
      default:
        break;
    }
  };
  const sortPaymentMax = () => {
    setDetailPayment(
      [...detailPayment].sort(
        (a: {valuetotal: number}, b: {valuetotal: number}) =>
          b.valuetotal - a.valuetotal,
      ),
    );
  };
  const sortPaymentMin = () => {
    setDetailPayment(
      [...detailPayment].sort(
        (a: {valuetotal: number}, b: {valuetotal: number}) =>
          a.valuetotal - b.valuetotal,
      ),
    );
  };
  const sortPaymentMaxDay = () => {
    setDetailPayment(
      [...detailPayment].sort(
        (a: {id: number}, b: {id: number}) => b.id - a.id,
      ),
    );
  };
  const sortPaymentMinDay = () => {
    setDetailPayment(
      [...detailPayment].sort(
        (a: {id: number}, b: {id: number}) => a.id - b.id,
      ),
    );
  };

  useEffect(() => {
    if (phone !== undefined && name !== undefined && address !== undefined) {
      if (name?.length <= 0 || address?.length <= 0 || phone?.length <= 0) {
        setConfirmButton(true);
      }
    }
  }, [name, address, phone]);

  return (
    <SafeAreaView style={DetailStaffCss.container}>
      <View flDirectionRow>
        <View flDirectionRow w_100 mVertical6>
          <View w_88 aItemsCenter>
            <TouchableOpacity
              onPress={() => setModalVisiblePassword(true)}
              style={[
                DetailStaffCss.btnChangePassword,
                {backgroundColor: maincolor},
              ]}>
              <Text textCenter whiteColor fontSize16 fontWeight600>
                {detailStafScreenChangePass}
              </Text>
            </TouchableOpacity>
          </View>
          <View w_10>
            <TouchableOpacity onPress={handleGoback}>
              <Entypo name="log-out" size={38} color={maincolor} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View>
          <View
            flDirectionRow
            w_100
            style={{
              marginTop: 10,
            }}>
            <View w_55 jContentCenter>
              <Text fontSize20 fontWeight600 mainColor>
                {staffScreenStatusStaffWork}
              </Text>
            </View>
            <View w_40>
              <SelectDropdown
                buttonStyle={[
                  DetailStaffCss.btnChangeStatus,
                  {
                    backgroundColor: maincolor,
                  },
                ]}
                buttonTextStyle={{
                  color: backgroundcolor,
                }}
                defaultButtonText={
                  status === 1
                    ? onlineStatus
                    : status === -1
                    ? offlineStatus
                    : status === 0
                    ? stopStatus
                    : ''
                }
                data={countries}
                onSelect={selectedItem => {
                  setConfirmButton(false);
                  switch (selectedItem) {
                    case onlineStatus:
                      setStatus(1);
                      break;
                    case offlineStatus:
                      setStatus(-1);
                      break;
                    case stopStatus:
                      setStatus(0);
                      break;
                    default:
                      break;
                  }
                }}
                buttonTextAfterSelection={selectedItem => selectedItem}
                rowTextForSelection={item => item}
              />
            </View>
          </View>
          <View flDirectionRow mVertical2 w_100>
            <View
              jContentCenter
              style={{
                width:
                  isCheckLang == 0 ? '40%' : isCheckLang == 1 ? '30%' : '35%',
              }}>
              <Text fontSize20 fontWeight600 mainColor>
                {staffScreenNameStaff}:
              </Text>
            </View>
            <View
              style={{
                width:
                  isCheckLang == 0 ? '60%' : isCheckLang == 1 ? '70%' : '65%',
              }}>
              <TextInput
                value={name}
                onChangeText={text => {
                  setChangeName(text);
                }}
                style={[
                  DetailStaffCss.inputStaffName,
                  {
                    borderColor: maincolor,
                    color: blackcolor,
                  },
                ]}
              />
            </View>
          </View>
          <View flDirectionRow aItemsCenter mVertical2 w_100>
            <View
              style={{
                width:
                  isCheckLang == 0 ? '20%' : isCheckLang == 1 ? '25%' : '15%',
              }}>
              <Text fontSize20 fontWeight600 mainColor>
                {staffScreenAddressStaff}:
              </Text>
            </View>
            <View
              style={{
                width:
                  isCheckLang == 0 ? '80%' : isCheckLang == 1 ? '75%' : '85%',
              }}>
              <TextInput
                value={address && address.toString()}
                style={[
                  DetailStaffCss.inputAddressStaff,
                  {
                    borderColor: maincolor,
                    color: blackcolor,
                  },
                ]}
                onChangeText={text => {
                  setChangeAddress(text);
                }}
              />
            </View>
          </View>
          <View flDirectionRow aItemsCenter mVertical2 w_100>
            <View
              style={{
                width:
                  isCheckLang == 0 ? '35%' : isCheckLang == 1 ? '20%' : '25%',
              }}>
              <Text fontSize20 fontWeight600 mainColor>
                {staffScreenPhoneStaff}:
              </Text>
            </View>
            <View
              style={{
                width:
                  isCheckLang == 0 ? '65%' : isCheckLang == 1 ? '80%' : '75%',
              }}>
              <TextInput
                value={phone?.toString()}
                keyboardType={'numeric'}
                style={[
                  DetailStaffCss.inputNumberStaff,

                  {
                    borderColor: maincolor,
                    color: blackcolor,
                  },
                ]}
                onChangeText={text => {
                  setChangePhone(text);
                }}
              />
            </View>
          </View>
        </View>
      </TouchableWithoutFeedback>
      {/* Detail Payment Staff */}
      {(detailPayment && detailPayment.length !== 0) ||
      (detailPaymentDeleted && detailPaymentDeleted.length !== 0) ? (
        <View
          style={{
            marginTop: 4,
          }}>
          <View>
            {showDetailPayment ? (
              <View w_100 flDirectionRow aItemsCenter jContentBetween>
                <View
                  style={{
                    width:
                      isCheckLang == 0
                        ? '40%'
                        : isCheckLang == 1
                        ? '40%'
                        : '40%',
                    paddingLeft: 4,
                    justifyContent: 'center',
                    height: scale(32),
                    borderRadius: scale(8),
                  }}
                  mainBGColor>
                  <Text
                    fontSize16
                    whiteColor
                    onPress={() => {
                      setShowDetailPayment(!showDetailPayment);
                      setCheck(!check);
                    }}
                    style={{
                      ...Platform.select({
                        ios: {
                          // padding: scale(10),
                        },
                        android: {
                          fontSize: isCheckLang == 2 ? scale(16) : scale(18),
                        },
                      }),
                    }}>
                    {detailStafScreenHidestatistics}
                  </Text>
                </View>
                <View
                  style={{
                    marginTop: 2,
                    width:
                      isCheckLang == 0
                        ? '55%'
                        : isCheckLang == 1
                        ? '55%'
                        : '55%',
                  }}>
                  <SelectDropdown
                    data={searchPayment}
                    defaultButtonText={detailStafScreenStaffArrangeBill}
                    buttonStyle={[
                      DetailStaffCss.btnSortPayment,
                      {
                        backgroundColor: maincolor,
                      },
                    ]}
                    buttonTextStyle={{
                      color: backgroundcolor,
                    }}
                    onSelect={selectedItem => selectItem(selectedItem)}
                    buttonTextAfterSelection={selectedItem => selectedItem}
                    rowTextForSelection={item => item}
                  />
                </View>
              </View>
            ) : (
              <View  flDirectionRow jContentBetween>
                <View
                  w_40
                  jContentCenter
                  mainBGColor
                  style={{
                    justifyContent: 'center',
                    height: scale(32),
                    borderRadius: scale(8),
                  }}>
                  <Text
                    mainColor
                    fontSize18
                    whiteColor
                    onPress={() => {
                      setShowDetailPayment(!showDetailPayment);
                      setCheck(false);
                      setShowPaymentDeleted(false);
                    }}
                    style={{marginLeft: 4}}>
                    {detailStafScreenShowstatistics}
                  </Text>
                </View>
                <View
                  jContentCenter
                  w_55
                  mainBGColor
                  style={{
                    height: scale(32),
                    borderRadius: scale(8),
                  }}>
                  <Text
                    fontSize18
                    whiteColor
                    style={{
                      ...Platform.select({
                        ios: {
                          // padding: scale(10),
                        },
                        android: {
                          fontSize: isCheckLang == 2 ? scale(16) : scale(18),
                        },
                      }),
                    }}
                    textCenter
                    onPress={() => {
                      setShowPaymentDeleted(!showPaymentDeleted);
                      setCheck(!check);
                    }}>
                    {showPaymentDeleted
                      ? detailStafScreenStaffHideBillDeleted
                      : detailStafScreenStaffShowBillDeleted}
                  </Text>
                </View>
              </View>
            )}
          </View>
        </View>
      ) : (
        <View mVertical6>
          <Text fontSize18 fontWeight600 redText>
            {detailStafScreenStaffNoRevenue}
          </Text>
        </View>
      )}
      {showDetailPayment ? (
        <>
          <View style={DetailStaffCss.viewSearchBill}>
            <Ionicons
              name="search"
              size={34}
              style={[
                DetailStaffCss.iconSearchBill,
                {
                  color: blackcolor,
                },
              ]}
            />
            <TextInput
              placeholder={detailStafScreenStaffFindBill}
              placeholderTextColor={blackcolor}
              onChangeText={e => {
                setSearchProduct(e);
              }}
              style={{
                color: blackcolor,
              }}
            />
          </View>
          <FlatList
            data={detailPayment.filter(
              (item: {
                id: number;
                user_name: string;
                dayPayment: string;
                reason: string;
                paymentNotChange: string;
                valuetotal: string;
              }) =>
                item.id
                  .toString()
                  .toLowerCase()
                  .includes(searchProduct.toLowerCase()),
            )}
            showsVerticalScrollIndicator={false}
            renderItem={({item}: any) => (
              <View mVertical6 bRadius8 p_10 borderMainColor borderWidth2>
                <View flDirectionRow jContentBetween>
                  <View>
                    <Text fontSize16 blackColor>
                      {detailStafScreenStaffPaidBill} : {item.user_name}
                    </Text>
                  </View>
                  <View>
                    <Text fontSize16 blackColor>
                      {item.id}
                    </Text>
                  </View>
                </View>
                <View flDirectionRow w_100>
                  <View w_50>
                    <Text fontSize16 blackColor>
                      {item.dayPayment}
                    </Text>
                  </View>
                  {/* <View w_50>
                    <Text fontSize16 blackColor>
                      {item.created_at}
                    </Text>
                  </View> */}
                </View>

                <View>
                  <Pressable onPress={() => setModalVisible(true)} />
                </View>
                {/* <View flDirectionRow>
                  <View>
                    <Text fontSize16 blackColor>
                      {detailStafScreenStaffInvoiceStatus}:
                    </Text>
                  </View>
                  <View>
                    <Text fontSize16 blackColor>
                      {item.reason == '0' ? (
                        detailStafScreenStaffNoChange
                      ) : (
                        <Text redText fontWeight700>
                          {' '}
                          {detailStafScreenStaffChanged}
                        </Text>
                      )}
                    </Text>
                  </View>
                </View> */}

                {/* <View flDirectionRow>
                  <View>
                    <Text fontSize16 blackColor>
                      {detailStafScreenStaffChangedReason}:
                    </Text>
                  </View>
                  <View>
                    <Text fontSize16 blackColor>
                      {item.reason == '0' ? null : (
                        <>
                          <Text redText fontWeight700>
                            {' '}
                            {item.reason}
                          </Text>
                        </>
                      )}
                    </Text>
                  </View>
                </View> */}

                {/* <View flDirectionRow>
                  <View>
                    <Text fontSize16 blackColor>
                      {detailStafScreenStaffPriceNoVAT}:{'  '}
                    </Text>
                  </View>
                  <View>
                    <Text redText fontWeight800 fontSize16>
                      {item.reason == 'null'
                        ? item.paymentNotChange
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                        : item.paymentNotChange
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      {item.reason == 'null' ? currencySymbol : currencySymbol}
                    </Text>
                  </View>
                </View> */}

                <View flDirectionRow jContentBetween aItemsCenter>
                  <View>
                    <Text mainColor fontWeight700 fontSize18>
                      <Text redText fontSize16>
                        {detailStafScreenStaffPaymented}:{' '}
                      </Text>
                      {item?.valuetotal
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}{' '}
                      {currencySymbol}
                    </Text>
                  </View>
                  <TouchableOpacity
                    onPress={() => {
                      showModal(item);
                    }}>
                    <Text mainColor fontWeight700 fontSize18>
                      {detailStafScreenStaffDetailBill}
                    </Text>
                  </TouchableOpacity>
                </View>
                <View aItemsFlexStart>
                  <TouchableOpacity
                    onPress={() => {
                      setIdBillDelete(item.id);
                      setModalCancelBill(!modalCancelBill);
                    }}>
                    <Text redText fontWeight700 fontSize18>
                      {detailStafScreenStaffDeleteBill}
                    </Text>
                  </TouchableOpacity>
                </View>
                <Modal
                  animationType="fade"
                  transparent={true}
                  visible={modalCancelBill}>
                  <View flex1 aItemsCenter jContentCenter>
                    <View
                      style={[
                        DetailStaffCss.modalView,
                        {
                          shadowColor: blackcolor,
                        },
                      ]}>
                      <Text
                        style={DetailStaffCss.modalTitle}
                        fontSize22
                        fontWeight600
                        mainColor>
                        {detailStafScreenStaffDeleteBillConfirm}
                      </Text>
                      <View flDirectionRow>
                        <Pressable
                          style={[
                            DetailStaffCss.btnConfirm,
                            {
                              backgroundColor: maincolor,
                            },
                          ]}
                          onPress={handleDeleteBill}>
                          <Text whiteColor fontWeightBold textCenter>
                            {agree}
                          </Text>
                        </Pressable>
                        <Pressable
                          style={[
                            DetailStaffCss.btnConfirm,
                            {
                              backgroundColor: maincolor,
                            },
                          ]}
                          onPress={() => setModalCancelBill(!modalCancelBill)}>
                          <Text whiteColor fontWeightBold textCenter>
                            {cancel}
                          </Text>
                        </Pressable>
                      </View>
                    </View>
                    {isLoadingScreen ? (
                      <View
                        style={[
                          DetailStaffCss.viewLoading,
                          {
                            backgroundColor: modalbackground,
                          },
                        ]}>
                        <ActivityIndicator size="large" color={maincolor} />
                      </View>
                    ) : null}
                  </View>
                </Modal>
              </View>
            )}
          />
        </>
      ) : null}
      {showPaymentDeleted ? (
        <>
          <View style={DetailStaffCss.viewSearchBill}>
            <Ionicons
              name="search"
              size={34}
              style={[
                DetailStaffCss.iconSearchBill,
                {
                  color: blackcolor,
                },
              ]}
            />
            <TextInput
              placeholder={detailStafScreenStaffFindBill}
              placeholderTextColor={blackcolor}
              onChangeText={e => {
                setSearchProduct(e);
              }}
              style={{
                color: blackcolor,
              }}
            />
          </View>
          <FlatList
            data={detailPaymentDeleted.filter(
              (item: {
                id: number;
                user_name: string;
                dayPayment: string;
                reason: string;
                paymentNotChange: string;
                valuetotal: string;
                timeDeletePayment: string;
              }) =>
                item.id
                  .toString()
                  .toLowerCase()
                  .includes(searchProduct.toLowerCase()),
            )}
            showsVerticalScrollIndicator={false}
            renderItem={({item}: any) => (
              <View mVertical6 bRadius8 p_10 borderMainColor borderWidth2>
                <View flDirectionRow jContentBetween>
                  <View>
                    <Text fontSize16 blackColor>
                      {detailStafScreenStaffPaidBill}: {item.user_name}
                    </Text>
                  </View>
                  <View>
                    <Text fontSize16 blackColor>
                      {item.id}
                    </Text>
                  </View>
                </View>
                {/* <View flDirectionRow w_100>
                  <View w_50>
                    <Text fontSize16 blackColor>
                      {detailStafScreenStaffDayCreated}:
                    </Text>
                  </View>
                  <View w_50 aItemsFlexEnd>
                    <Text fontSize16 blackColor>
                      {item.dayPayment}
                    </Text>
                  </View>
                  <Modal
                    animationType="fade"
                    transparent={true}
                    visible={modalCancelBill}>
                    <View flex1 aItemsCenter jContentCenter>
                      <View
                        style={[
                          DetailStaffCss.modalView,
                          {
                            shadowColor: blackcolor,
                          },
                        ]}>
                        <Text
                          style={DetailStaffCss.modalTitle}
                          fontSize22
                          fontWeight600
                          mainColor>
                          {detailStafScreenStaffDeleteBill}
                        </Text>
                        <View flDirectionRow>
                          <Pressable
                            style={[
                              DetailStaffCss.btnConfirm,
                              {
                                backgroundColor: maincolor,
                              },
                            ]}
                            onPress={handleDeleteBill}>
                            <Text whiteColor fontWeightBold textCenter>
                              {agree}
                            </Text>
                          </Pressable>
                          <Pressable
                            style={[
                              DetailStaffCss.btnConfirm,
                              {
                                backgroundColor: maincolor,
                              },
                            ]}
                            onPress={() =>
                              setModalCancelBill(!modalCancelBill)
                            }>
                            <Text whiteColor fontWeightBold textCenter>
                              {cancel}
                            </Text>
                          </Pressable>
                        </View>
                      </View>
                      {isLoadingScreen ? (
                        <View
                          style={[
                            DetailStaffCss.viewLoading,
                            {
                              backgroundColor: modalbackground,
                            },
                          ]}>
                          <ActivityIndicator size="large" color={maincolor} />
                        </View>
                      ) : null}
                    </View>
                  </Modal>
                </View> */}
                <View flDirectionRow w_100>
                  <View w_50>
                    <Text fontSize16 redText fontWeight800>
                      {detailStafScreenStaffDayDeleted}:
                    </Text>
                  </View>
                  <View w_40 aItemsFlexEnd>
                    <Text fontSize16 redText fontWeight800>
                      {item.timeDeletePayment}
                    </Text>
                  </View>
                </View>

                <View>
                  <Pressable onPress={() => setModalVisible(true)} />
                </View>
                {/* <View flDirectionRow>
                  <View>
                    <Text fontSize16 blackColor>
                      {detailStafScreenStaffInvoiceStatus}:
                    </Text>
                  </View>
                  <View>
                    <Text fontSize16 blackColor>
                      {item.reason == '0' ? (
                        detailStafScreenStaffNoChange
                      ) : (
                        <Text redText fontWeight700>
                          {' '}
                          {detailStafScreenStaffChanged}
                        </Text>
                      )}
                    </Text>
                  </View>
                </View> */}

                {/* <View flDirectionRow>
                  <View>
                    <Text fontSize16 blackColor>
                      {detailStafScreenStaffChangedReason}:
                    </Text>
                  </View>
                  <View>
                    <Text fontSize16 blackColor>
                      {item.reason == '0' ? null : (
                        <>
                          <Text redText fontWeight700>
                            {' '}
                            {item.reason}
                          </Text>
                        </>
                      )}
                    </Text>
                  </View>
                </View> */}

                {/* <View flDirectionRow>
                  <View>
                    <Text fontSize16 blackColor>
                      {detailStafScreenStaffPriceNoVAT}:{'  '}
                    </Text>
                  </View>
                  <View>
                    <Text redText fontWeight800 fontSize16>
                      {item.reason == '0'
                        ? item.paymentNotChange
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                        : item.paymentNotChange
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      {item.reason == '0' ? currencySymbol : currencySymbol}
                    </Text>
                  </View>
                </View> */}

                <View flDirectionRow jContentBetween aItemsCenter>
                  <View>
                    <Text mainColor fontWeight700 fontSize18>
                      <Text redText fontSize16>
                        {detailStafScreenStaffPaymented}:{' '}
                      </Text>
                      {item?.valuetotal
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}{' '}
                      {currencySymbol}
                    </Text>
                  </View>
                  <TouchableOpacity
                    onPress={() => {
                      showModal(item);
                    }}>
                    <Text mainColor fontWeight700 fontSize18>
                      {detailStafScreenStaffDetailBill}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          />
        </>
      ) : null}

      {/*
      Total payment staff
      */}
      {detailPayment && detailPayment.length !== 0 && check ? (
        <View flDirectionRow aItemsFlexEnd w_100 mBottom10>
          <View w_40>
            <Text fontSize16 blackColor>
              {detailStafScreenStaffTotalRevenue}
            </Text>
          </View>
          <View w_55 aItemsFlexEnd>
            <Text fontSize18 redText fontWeight700>
              {Math.round(total)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}{' '}
              {currencySymbol}
            </Text>
          </View>
        </View>
      ) : null}

      {check ? (
        <View
          flDirectionRow
          // jContentBetween
          // jContentCenter
          jContentAround
          style={{
            // marginLeft: scale(15),
            // backgroundColor:'red'
          }}>
          <View>
            <TouchableOpacity
              onPress={() => handleUpdate(id)}
              disabled={confirmButton}
              style={[
                DetailStaffCss.btnUpdateInfo,
                {
                  backgroundColor: confirmButton ? graycolor : maincolor,
                },
              ]}>
              <Text textCenter whiteColor fontSize16 fontWeight600>
                {detailStafScreenStaffUpdateInfo}
              </Text>
            </TouchableOpacity>
          </View>

          <View>
            <TouchableOpacity
              onPress={() => setModalVisible(true)}
              style={[
                DetailStaffCss.btnDeleteStaff,
                {
                  backgroundColor: maincolor,
                },
              ]}>
              <Text textCenter whiteColor fontSize16 fontWeight600>
                {detailStafScreenStaffDeleteStaff}
              </Text>
            </TouchableOpacity>
          </View>

          <View>
            <Modal
              animationType="fade"
              transparent={true}
              visible={modalVisible}>
              <View flex1 aItemsCenter jContentCenter>
                <View
                  style={[
                    DetailStaffCss.modalView,
                    {
                      shadowColor: blackcolor,
                    },
                  ]}>
                  <Text
                    style={DetailStaffCss.modalTitle}
                    fontSize22
                    fontWeight600
                    mainColor>
                    {detailStafScreenStaffDeleteStaffConfirm}
                  </Text>
                  <View flDirectionRow>
                    <Pressable
                      style={[
                        DetailStaffCss.btnConfirm,
                        {
                          backgroundColor: maincolor,
                        },
                      ]}
                      onPress={() => handleDelete(id)}>
                      <Text whiteColor fontWeightBold textCenter>
                        {agree}
                      </Text>
                    </Pressable>
                    <Pressable
                      style={[
                        DetailStaffCss.btnConfirm,
                        {
                          backgroundColor: maincolor,
                        },
                      ]}
                      onPress={() => setModalVisible(!modalVisible)}>
                      <Text whiteColor fontWeightBold textCenter>
                        {cancel}
                      </Text>
                    </Pressable>
                  </View>
                </View>
                {isLoadingScreen ? (
                  <View
                    style={[
                      DetailStaffCss.viewLoading,
                      {
                        backgroundColor: modalbackground,
                      },
                    ]}>
                    <ActivityIndicator size="large" color={maincolor} />
                  </View>
                ) : null}
              </View>
            </Modal>
          </View>
        </View>
      ) : (
        ''
      )}

      <View>
        <View>
          <Formik
            initialValues={{
              password: '',
              confirmPassWord: '',
            }}
            validationSchema={SignupSchema}
            onSubmit={() => {}}>
            {({
              handleChange,
              values,
              errors,
              touched,
              isValid,
              dirty,
              setFieldTouched,
              resetForm,
            }) => (
              <Modal
                animationType="fade"
                transparent={true}
                visible={modalVisiblePassword}>
                <View
                  mHorizontal6
                  style={{
                    flex: 1,
                    // backgroundColor:'red',
                    marginTop: hp('10%'),
                  }}>
                  <View
                    style={[
                      DetailStaffCss.modalChangePassword,
                      {
                        shadowColor: blackcolor,
                      },
                    ]}>
                    <View>
                      <Text
                        style={DetailStaffCss.modalTitle}
                        fontSize22
                        fontWeight600
                        mainColor>
                        {detailStafScreenChangePass}
                      </Text>
                      <View mVertical6>
                        <View>
                          <Text
                            fontSize16
                            marginBottom4
                            fontWeight500
                            blackColor>
                            {staffScreenPasswordStaff}
                          </Text>
                        </View>
                        <View
                          style={[
                            DetailStaffCss.inputChangePassword,
                            {
                              borderColor: maincolor,
                            },
                          ]}
                          >
                          <View w_80>
                            <TextInput
                              onChangeText={handleChange('password')}
                              onBlur={() => setFieldTouched('password')}
                              style={[
                                DetailStaffCss.view90Persent,
                                {
                                  color: blackcolor,
                                },
                              ]}
                              secureTextEntry={eye}
                            />
                          </View>
                          <View w_20 aItemsFlexEnd jContentCenter p_Right10>
                            <TouchableOpacity
                              onPress={() => {
                                setEye(!eye);
                              }}>
                              <Ionicons
                                name={eye ? 'eye-off' : 'eye'}
                                size={30}
                                color={maincolor}
                              />
                            </TouchableOpacity>
                          </View>
                        </View>
                        <View>
                          {errors.password && touched.password ? (
                            <View>
                              <Text redText>{errors.password}</Text>
                            </View>
                          ) : null}
                        </View>
                      </View>
                      <View>
                        <View mVertical6>
                          <View>
                            <Text
                              fontSize16
                              marginBottom4
                              fontWeight500
                              blackColor>
                              {staffScreenPasswordConfirmStaff}
                            </Text>
                          </View>
                          <View
                            style={[
                              DetailStaffCss.inputChangePassword,
                              {
                                borderColor: maincolor,
                              },
                            ]}
                            >
                            <View w_80>
                              <TextInput
                                style={[
                                  DetailStaffCss.view90Persent,
                                  {
                                    color: blackcolor,
                                  },
                                ]}
                                onChangeText={handleChange('confirmPassWord')}
                                onBlur={() =>
                                  setFieldTouched('confirmPassWord')
                                }
                                secureTextEntry={eyeConfim}
                              />
                            </View>
                            <View w_20 aItemsFlexEnd jContentCenter p_Right10>
                              <TouchableOpacity
                                onPress={() => {
                                  setEyeConfim(!eyeConfim);
                                }}>
                                <Ionicons
                                  name={eyeConfim ? 'eye-off' : 'eye'}
                                  size={30}
                                  color={maincolor}
                                />
                              </TouchableOpacity>
                            </View>
                          </View>
                        </View>
                        <View>
                          {errors.confirmPassWord && touched.confirmPassWord ? (
                            <View>
                              <Text redText>{errors.confirmPassWord}</Text>
                            </View>
                          ) : null}
                        </View>
                      </View>
                    </View>
                    <View style={DetailStaffCss.footerConfirmPassword}>
                      <TouchableOpacity
                        disabled={!isValid || !dirty}
                        onPress={() => {
                          changePassword(id, values);
                        }}
                        style={[
                          DetailStaffCss.btnConfirmPassWord,
                          {
                            backgroundColor:
                              !isValid || !dirty ? graycolor : maincolor,
                          },
                        ]}>
                        <Text fontSize18 fontWeight600 whiteColor>
                          {detailStafScreenStaffUpdate}
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        onPress={() => {
                          resetForm();
                          setModalVisiblePassword(false);
                        }}
                        style={[
                          DetailStaffCss.btnConfirmPassWord,
                          {
                            backgroundColor: maincolor,
                          },
                        ]}>
                        <Text fontSize18 fontWeight600 whiteColor>
                          {cancel}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
                {/* {isLoadingScreen ? (
                  <View
                    style={[
                      DetailStaffCss.viewLoading,
                      {
                        backgroundColor: modalbackground,
                      },
                    ]}>
                    <ActivityIndicator size="large" color={maincolor} />
                  </View>
                ) : null} */}
              </Modal>
            )}
          </Formik>
        </View>
      </View>

      {nameSelectOpition === 'DetailPaymentStaff' ? (
        <View
          style={[
            DetailStaffCss.showDetailPayment,
            {
              backgroundColor: modalbackground,
            },
          ]}>
          <DetailPaymentStaff item={detailPaymentStaff} />
        </View>
      ) : null}
    </SafeAreaView>
  );
};

export default React.memo(DetailStaff);
