import React, {useContext, useEffect, useMemo, useRef, useState} from 'react';
import {
  ActivityIndicator,
  Alert,
  Image,
  Linking,
  LogBox,
  Modal,
  Platform,
  Pressable,
  TouchableOpacity,
} from 'react-native';
import {useDispatch} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
// import {io, Socket} from 'socket.io-client';

import Ionicons from 'react-native-vector-icons/Ionicons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Entypo from 'react-native-vector-icons/Entypo';
import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';
import Feather from 'react-native-vector-icons/Feather';
import Fontisto from 'react-native-vector-icons/Fontisto';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import AntDesign from 'react-native-vector-icons/AntDesign';

import {AuthContext} from '../../context/AuthContext';
import {HomeCss} from '../HomeScreen/HomeScreenCss';
import {Text, View, ModalUploadLogo} from '../../Component/index';
import {
  setAdminInfo,
  setFlag,
  setLoading,
  setResetInfoAdmin,
  setStateShowOption,
  setTokenAdmin,
  resetState,
} from '../../Redux/Slide';
import {
  checkToken,
  fetchPaymentToDay,
  fetchRevenueToDay,
  handleDisableAdmin,
  handleGetAllStaffRedux,
  setIsValue,
  setResetPrint,
  setResetToken,
  resetStateData,
  fetchListSubProduct,
  fetchListProduct,
} from '../../Redux/GetData';

import {scale} from '../../utils/style/Reponsive';
import {ThunkDispatch} from '@reduxjs/toolkit';
import {useAppSelector} from '../../hooks';
import {languages} from '../../constants/index';
import {BASE_URL} from '../../../src/api/ApiManager';
import Container from '../../../src/Component/Container/Container';
import TextComponent from '../../Component/Text/TextComponent';
// import socketIOClient from 'socket.io-client';

interface IProps {
  navigation: {
    navigate: (screen: string) => void;
  };
}

LogBox.ignoreAllLogs(); //Ignore all log notifications
interface HomeScreen {
  [key: string]: string | undefined;
  homeScreenProduct?: string;
  homeScreenRevenue?: string;
  homeScreenTable?: string;
  homeScreenStaff?: string;
  homeScreenInfomation?: string;
  homeScreenCustomers?: string;
  homeScreenTaxReceipt?: string;
  contact?: string;
  phoneContact?: string;
  homeScreenRevenueToday?: string;
  homeScreenOrdersNumbersToday?: string;
  homeScreenOrders?: string;
  homeScreenBestSalesToday?: string;
  homeScreenAllOrders?: string;
  homeScreenHighestSalesToday?: string;
  homeScreenDeleteAccount?: string;
  cancel?: string;
  agree?: string;
  homeScreenLogout?: string;
  messageDisable?: string;
  messageApp?: string;
  vietVangPhoneNumber?: string;
}

const HomeScreen = (props: IProps) => {
  const {navigate} = props.navigation;

  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const {userInfo, logout} = useContext(AuthContext);
  const {loading, flag, access_token, nameSelectOpition} = useAppSelector(
    state => state.counter,
  );
  const currencySymbol = useAppSelector(state => state.counter?.infoAdmin?.admin?.store?.currency_symbol) || "đ";
  const {revenueToday, paymentToday, listSubProduct, checkTokenAccess} =
    useAppSelector(state => state.getData);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [modalDelete, setModalDelete] = useState<boolean>(false);
  const [urlImages, setUrlImages] = useState<string>(
    `${BASE_URL}/image/default_logo.png`,
  );
  const [food, setFood] = useState<any>([]);
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {maincolor, backgroundcolor, modalbackground} = xmlData;
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): HomeScreen | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: HomeScreen = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    homeScreenProduct,
    homeScreenRevenue,
    homeScreenTable,
    homeScreenStaff,
    homeScreenInfomation,
    homeScreenCustomers,
    homeScreenTaxReceipt,
    contact,
    phoneContact,
    currencySymbol1,
    homeScreenRevenueToday,
    homeScreenOrdersNumbersToday,
    homeScreenOrders,
    homeScreenBestSalesToday,
    homeScreenAllOrders,
    homeScreenHighestSalesToday,
    homeScreenDeleteAccount,
    homeScreenDeleteAccountText1,
    homeScreenDeleteAccountText2,
    cancel,
    agree,
    homeScreenLogout,
    messageDisable,
    messageApp,
    vietVangPhoneNumber,
    homeScreenWareHouse,
    tokenExpiresed,
  }: any = langData;

  // console.log("homeScreenTaxReceipt", homeScreenTaxReceipt);

  const dialCall = () => {
    const prefix = Platform.OS === 'android' ? 'tel:' : 'telprompt:';
    const phoneNumber = `${prefix}0${vietVangPhoneNumber}`;
    Linking.openURL(phoneNumber);
  };
  const showModalDelete = () => {
    setModalDelete(!modalVisible);
  };

  const handleLogout = () => {
    dispatch(setResetToken());
    dispatch(setResetPrint());
    dispatch(setResetInfoAdmin());
    dispatch(resetState()); // Add this line to reset the entire state to default
    dispatch(resetStateData());
    logout();
  };

  //handle disable account admin
  const handleDeleteAdmin = async () => {
    await dispatch(
      handleDisableAdmin({
        access_token,
        messageDisable,
        messageApp,
      }),
    );
    setModalDelete(false);
    handleLogout();
  };

  const getDateData = async () => {
    const [adminInfo, access_token, LogoApp, isCheckLang] = await Promise.all([
      AsyncStorage.getItem('userInfo'),
      AsyncStorage.getItem('access_token'),
      AsyncStorage.getItem('LogoApp'),
      AsyncStorage.getItem('isCheckLang'),
    ]);
    let nameAdmin = JSON.parse(adminInfo!) as any;
    let appLogo = JSON.parse(LogoApp!);
    let tokenadmin = access_token ? JSON.parse(access_token) : null;
    let isCheckLangue = isCheckLang ? JSON.parse(isCheckLang) : null;
    if (LogoApp === null || LogoApp === undefined) {
      setUrlImages(nameAdmin?.admin?.image);
    } else {
      setUrlImages(appLogo);
    }

    if (nameAdmin?.admin) {
      dispatch(setAdminInfo(nameAdmin));
    }

    if (tokenadmin) {
      const requestOptions = {access_token: tokenadmin, messageApp};
      dispatch(setTokenAdmin(tokenadmin));
      dispatch(setIsValue(isCheckLangue));
      dispatch(fetchRevenueToDay(requestOptions));
      dispatch(fetchPaymentToDay(requestOptions));
      dispatch(checkToken(requestOptions));
      dispatch(handleGetAllStaffRedux(requestOptions));
      dispatch(fetchListSubProduct(requestOptions));
       dispatch(fetchListProduct(requestOptions));
    }
  };

  const handleNavigation = (screen: string) => {
    navigate(screen);
  };
  // Call the function with the appropriate screen name
  const handleNavigationProduct = () => {
    handleNavigation('ProductScreen');
  };

  const handleNavigationInfomation = () => {
    handleNavigation('InfomationScreen');
  };

  const handleNavigationInventory = () => {
    handleNavigation('InventoryManagement');
  };

  const handleChart = () => {
    handleNavigation('ChartScreen');
  };

  const handleTable = () => {
    handleNavigation('TableScreen');
  };

  const handleStaff = () => {
    handleNavigation('StaffScreen');
  };

  const moveCustomersScreen = () => {
    handleNavigation('CustommersScreen');
  };

  const handleNavigationElectronicInvoice = () => {
    handleNavigation('ElectronicInvoiceScreen');
  };

  const handleShowInformation = () => {
    handleNavigationInfomation();
  };

  const handleElectronicInvoice = () => {
    handleNavigationElectronicInvoice();
  };

  const handleMoveInventoryScreen = () => {
    handleNavigationInventory();
  };

  useEffect(() => {
    if (paymentToday !== undefined) {
      const titlePayment = paymentToday.flatMap((payment: any) => payment.item);
      const newArray = titlePayment.reduce(
        (arr: {}[], item: {[x: string]: any}) => {
          const keys = Object.keys(item);
          if (keys.length > 1) {
            keys.forEach(key => {
              const newItem: {[x: string]: any} = {}; // Add index signature to newItem
              newItem[key] = {
                ...item[key],
                id: parseInt(item[key].id, 10), // Parse id as an integer
                quantity: parseInt(item[key].quantity, 10), // Parse quantity as an integer
                price: parseFloat(item[key].price), // Parse price as a float
                vat: parseFloat(item[key].vat), // Parse vat as a float
                extra_product_list: item[key].extra_product_list,
              };
              arr.push(newItem);
            });
          } else {
            const key = keys[0];
            const newItem = {
              [key]: {
                ...item[key],
                id: parseInt(item[key].id, 10), // Parse id as an integer
                quantity: parseInt(item[key].quantity, 10), // Parse quantity as an integer
                price: parseFloat(item[key].price), // Parse price as a float
                vat: parseFloat(item[key].vat), // Parse vat as a float
                extra_product_list: item[key].extra_product_list,
              },
            };
            arr.push(newItem);
          }
          return arr;
        },
        [],
      );
      const newArrayList = newArray.map(
        (item: {[s: string]: unknown} | ArrayLike<unknown>) =>
          Object.values(item)[0],
      );
      const newArrayAll = newArrayList.reduce((acc: any[], item: any) => {
        const extraProductList =
          item.extra_product_list?.map((extraProduct: any) => ({
            ...extraProduct,
            id: Number(extraProduct.id),
            price: Number(extraProduct.price),
            vat: Number(extraProduct.vat),
            parent_id: item.id,
            parent_title: item.title,
          })) || [];

        return [...acc, item, ...extraProductList];
      }, []);
      setFood(newArrayAll);
    }
  }, [paymentToday]);
  const result = food.reduce((acc: {[id: string]: any}, item: any) => {
    if (
      item.id !== undefined &&
      item.quantity !== undefined &&
      item.price !== undefined &&
      item.vat !== undefined
    ) {
      if (acc[item.id]) {
        acc[item.id].quantity += item.quantity;
        acc[item.id].vat = item.vat;
        acc[item.id].totalPrice =
          acc[item.id].quantity * item.price +
          (item.price * item.quantity * item.vat) / 100;
      } else {
        acc[item.id] = {
          id: item.id,
          title: item.title,
          price: item.price,
          vat: item.vat,
          quantity: item.quantity,
          totalPrice:
            item.quantity * item.price +
            (item.price * item.quantity * item.vat) / 100,
        };
      }
    }
    return acc;
  }, {});

  const newArray = Object.values(result);

  //Handle product quantity big sales on today

  const max_val = useMemo(() => {
    let maxVal: any = {id: 0, title: '', quantity: 0};
    if (newArray.length > 0) {
      maxVal = newArray.reduce(
        (accumulator: any, element: any) =>
          accumulator.quantity > element.quantity ? accumulator : element,
        maxVal,
      );
    }
    return maxVal;
  }, [newArray]);

  const max_val_Total = useMemo(() => {
    let maxValTotal: any = {quantity: 0, price: 0, vat: 0};
    if (newArray.length > 0) {
      maxValTotal = newArray.reduce(
        (accumulator: any, element: any) =>
          accumulator.quantity * accumulator.price +
            (accumulator.price * accumulator.quantity * accumulator.vat) / 100 >
          element.quantity * element.price +
            (element.price * element.quantity * element.vat) / 100
            ? accumulator
            : element,
        maxValTotal,
      );
    }
    return maxValTotal;
  }, [newArray]);

  const totalMaxVal = useMemo(
    () =>
      max_val_Total.price * max_val_Total.quantity +
      (max_val_Total.price * max_val_Total.quantity * max_val_Total.vat) / 100,
    [max_val_Total],
  );

  const refreshScreen = async () => {
    dispatch(setLoading({valid: true}));
    const timeId = setTimeout(() => {
      dispatch(setLoading({valid: false}));
      dispatch(setFlag());

    }, 1000);
    return () => clearTimeout(timeId);
  };

  useEffect(() => {
    getDateData();
  }, [flag]);
  useEffect(() => {
    dispatch(setFlag());
    getDateData();
  }, []);
  useEffect(() => {
    if (checkTokenAccess == 406) {
      Alert.alert(messageApp, tokenExpiresed);
      handleLogout();
    }
  }, [checkTokenAccess]);
  const showLogout = () => {
    setModalVisible(true);
  };
  // useEffect(() => {
  //   const socket = io('http://localhost:3003');
  //   console.log(socket);
  // }, []);
  // async function getToken() {
  //   const deviceToken = await messaging().getToken();
  // }
  // async function onDisplayNotification() {
  //   const channelId = await notifee.createChannel({
  //     id: 'default',
  //     name: 'Default Channel',
  //   });

  //   // Required for iOS
  //   // See https://notifee.app/react-native/docs/ios/permissions

  //   const notificationId = await notifee.displayNotification({
  //     id: '123',
  //     title: 'Notification Title',
  //     body: 'Main body content of the notification',
  //     android: {
  //       channelId,
  //       smallIcon: 'ic_launcher', // optional, defaults to 'ic_launcher'.
  //     },
  //   });

  //   await notifee.displayNotification({
  //     id: '123',
  //     title: `Thông báo`,
  //     body: 'Tài khoản của bạn còn 3 ngày nữa . Xin vui lòng thành toán',
  //     android: {
  //       channelId,
  //       smallIcon: 'ic_launcher', // optional, defaults to 'ic_launcher'.
  //       largeIcon: 'ic_launcher',
  //     },
  //   });
  // }
  // const getDiviceToken = async () => {
  //   await messaging().registerDeviceForRemoteMessages();
  //   const token = await messaging().getToken();
  //   sendDiviceToKen(access_token, token);
  //   // Alert.alert(token);
  // };

  // useEffect(() => {
  //   (async () => {
  //     if (paymentToday?.length !== 0) {
  //       const channelId = await notifee.createChannel({
  //         id: 'default',
  //         name: 'Default Channel',
  //       });
  //       await notifee.displayNotification({
  //         id: '123',
  //         title: `Thông báo`,
  //         body: 'Tài khoản của bạn còn 3 ngày nữa . Xin vui lòng thành toán',
  //         android: {
  //           channelId,
  //           smallIcon: 'ic_launcher', // optional, defaults to 'ic_launcher'.
  //           largeIcon: 'ic_launcher',
  //         },
  //       });
  //     }
  //   })();
  // }, [paymentToday?.length]);
  // useEffect(() => {
  //   async function onAppBootstrap() {
  //     // Register the device with FCM
  //     await messaging().registerDeviceForRemoteMessages();
  //     // Get the token divice
  //     const token = await messaging().getToken();
  //   }
  //   onAppBootstrap();
  // }, []);
  // async function getToken1() {
  //   const deviceToken = await messaging().getToken();
  // }
  // useEffect(() => {
  //   const setupNotifications = async () => {
  //     const channelId = await notifee.createChannel({
  //       id: 'default',
  //       name: 'Default Channel',
  //     });

  //     await getToken1();

  //     // messaging().onNotificationOpenedApp(remoteMessage => {
  //     //   Alert.alert('Open');
  //     //   console.log('Remote', remoteMessage);
  //     // });

  //     const unsubscrit = messaging().onMessage(async remoteMess => {
  //       await notifee.displayNotification({
  //         id: '123',
  //         title: `số lượng đơn hàng`,
  //         body: 'Updated ',
  //         android: {
  //           channelId,
  //           smallIcon: 'ic_launcher', // optional, defaults to 'ic_launcher'.
  //           largeIcon: 'ic_launcher',
  //         },
  //       });
  //     });

  //     return unsubscrit;
  //   };

  //   setupNotifications(); // Call the async function here

  //   // Return a cleanup function (if needed)
  //   return () => {
  //     // Cleanup code here
  //   };
  // }, []);

  // useEffect(() => {
  //   getDiviceToken();
  // }, [access_token]);

  return (
    // <SafeAreaView style={HomeCss.container}>
    <Container 

    >
      <View flDirectionRow aItemsCenter w_100 >
        <View w_15>
          <TouchableOpacity
            onPress={() => {
              dispatch(setStateShowOption({valid: false, nameOpiton: 'Logo'}));
            }}
            style={HomeCss.viewLogo}>
            <Image
              source={{
                uri:
                  urlImages === null || urlImages === undefined
                    ? `${BASE_URL}/image/default_logo.png`
                    : urlImages,
              }}
              style={HomeCss.headerAvatar}
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>
        <View jContentCenter aItemsCenter w_55 >
          <Text fontSize22 fontWeight600 mainColor textCenter>
            {userInfo.admin && (userInfo.admin.storename.length < 12 ? userInfo.admin.storename :  userInfo.admin.storename.substring(0, 12)+ "...")}
          </Text>
        </View>
        <View w_15>
          <SimpleLineIcons
            name="refresh"
            size={scale(40)}
            color={maincolor}
            onPress={refreshScreen}
          />
        </View>
        <View w_15 aItemsFlexEnd>
          <Entypo
            name="log-out"
            size={scale(40)}
            color={maincolor}
            onPress={showLogout}
          />
        </View>
      </View>

      <View >
        <View w_100 flDirectionRow>
          <View w_33 aItemsCenter>
            <TouchableOpacity
              onPress={handleNavigationProduct}
              style={[
                HomeCss.productItem,
                {
                  backgroundColor: backgroundcolor,
                },
              ]}>
              <Ionicons
                name="restaurant-outline"
                size={scale(26)}
                style={[
                  HomeCss.imgProduct,
                  {
                    color: backgroundcolor,
                    backgroundColor: maincolor,
                  },
                ]}
              />
              <Text fontSize20 fontWeight800 paddingTop2 blackColor>
                {homeScreenProduct}
              </Text>
            </TouchableOpacity>
          </View>
          <View w_33 aItemsCenter>
            <TouchableOpacity
              onPress={handleChart}
              style={[
                HomeCss.productItem,
                {
                  backgroundColor: backgroundcolor,
                },
              ]}>
              <Ionicons
                name="bar-chart-outline"
                size={scale(26)}
                style={[
                  HomeCss.imgProduct,
                  {
                    color: backgroundcolor,
                    backgroundColor: maincolor,
                  },
                ]}
              />
              <Text fontSize20 fontWeight800 paddingTop2 blackColor>
                {homeScreenRevenue}
              </Text>
            </TouchableOpacity>
          </View>
          <View w_33 aItemsCenter>
            <TouchableOpacity
              onPress={handleTable}
              style={[
                HomeCss.productItem,
                {
                  backgroundColor: backgroundcolor,
                },
              ]}>
              <MaterialCommunityIcons
                name="table-chair"
                size={scale(26)}
                style={[
                  HomeCss.imgProduct,
                  {
                    color: backgroundcolor,
                    backgroundColor: maincolor,
                  },
                ]}
              />
              <Text blackColor fontSize20 fontWeight800 paddingTop2>
                {homeScreenTable}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        <View w_100 flDirectionRow>
          <View w_33 aItemsCenter>
            <TouchableOpacity
              onPress={handleStaff}
              style={[
                HomeCss.productItem,
                {
                  backgroundColor: backgroundcolor,
                },
              ]}>
              <Ionicons
                name="person-outline"
                size={scale(26)}
                style={[
                  HomeCss.imgProduct,
                  {
                    color: backgroundcolor,
                    backgroundColor: maincolor,
                  },
                ]}
              />
              <Text blackColor fontSize20 fontWeight800 paddingTop2>
                {homeScreenStaff}
              </Text>
            </TouchableOpacity>
          </View>

          <View w_33 aItemsCenter>
            <TouchableOpacity
              style={[HomeCss.productItem, {backgroundColor: backgroundcolor}]}
              onPress={handleShowInformation}>
              <Fontisto
                name="player-settings"
                size={scale(26)}
                style={[
                  HomeCss.imgProduct,
                  {
                    color: backgroundcolor,
                    backgroundColor: maincolor,
                  },
                ]}
              />
              <Text blackColor fontSize20 fontWeight800 paddingTop2>
                {homeScreenInfomation}
              </Text>
            </TouchableOpacity>
          </View>
          <View w_33 aItemsCenter>
            <TouchableOpacity
              style={[HomeCss.productItem, {backgroundColor: backgroundcolor}]}
              onPress={moveCustomersScreen}>
              <FontAwesome
                name="address-card"
                size={scale(26)}
                style={[
                  HomeCss.imgProduct,
                  {
                    color: backgroundcolor,
                    backgroundColor: maincolor,
                  },
                ]}
              />
              <Text blackColor fontSize20 fontWeight800 paddingTop2>
                {homeScreenCustomers}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        <View w_100 flDirectionRow>
          <View w_33 aItemsCenter>
            <TouchableOpacity
              onPress={handleMoveInventoryScreen}
              style={[
                HomeCss.productItem,
                {
                  backgroundColor: backgroundcolor,
                },
              ]}>
              <MaterialIcons
                name="inventory"
                size={scale(26)}
                style={[
                  HomeCss.imgProduct,
                  {
                    color: backgroundcolor,
                    backgroundColor: maincolor,
                  },
                ]}
              />
              <Text blackColor fontSize20 fontWeight800 paddingTop2>
                {homeScreenWareHouse}
              </Text>
            </TouchableOpacity>
          </View>
          {
          isCheckLang !== 2 ?  
          <View w_33 aItemsCenter>
          <TouchableOpacity
            onPress={handleElectronicInvoice}
            style={[
              HomeCss.productItem,
              {
                backgroundColor: backgroundcolor,
              },
            ]}>
            <AntDesign
              name="export2"
              size={scale(26)}
              style={[
                HomeCss.imgProduct,
                {
                  color: backgroundcolor,
                  backgroundColor: maincolor,
                },
              ]}
            />
            <Text blackColor fontSize20 fontWeight800 paddingTop2>
            {homeScreenTaxReceipt}
            </Text>
          </TouchableOpacity>
        </View> 

          :<></>
          }
        </View>

        <View>
          <View mTop6>
            <Text blackColor fontSize20>
              {homeScreenRevenueToday}
              <Text mainColor fontWeight600>
                {` `}
                {revenueToday !== undefined
                  ? revenueToday
                      .toString()
                      .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  : 0}
              </Text>
              {` `}
              {currencySymbol}
            </Text>
          </View>
          <View mTop6>
            <Text blackColor fontSize20>
              {homeScreenOrdersNumbersToday}
              <Text mainColor fontWeight600>
                {` `}
                {paymentToday?.length > 0 ? paymentToday?.length : 0}
              </Text>
              {` `}
              {homeScreenOrders}
            </Text>
          </View>
          <View mTop6>
            <Text blackColor fontSize20>
              {homeScreenBestSalesToday}
              <Text mainColor fontWeight600>
                {` `}
                {max_val.title
                  ? max_val.title.length < 18
                    ? `${max_val.title}`
                    : `${max_val.title.substring(0, 18)}...`
                  : null}
              </Text>
              <Text blackColor fontWeight600>
                {isCheckLang !== 2 ? `.` : `。` }
                {` `}
              </Text>
              {homeScreenAllOrders}
              {` `}
              {max_val.quantity ? max_val.quantity : 0}
            </Text>
          </View>
          <View mTop6>
            <Text blackColor fontSize20>
              {homeScreenHighestSalesToday}
              <Text mainColor fontWeight600>
                {` `}
                {max_val_Total.title
                  ? max_val_Total.title.length < 18
                    ? `${max_val_Total.title}`
                    : `${max_val_Total.title.substring(0, 18)}...`
                  : null}
              </Text>
              <Text mainColor fontWeight600 />
              {homeScreenRevenue}
              {` `}
              {totalMaxVal
                ? totalMaxVal.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                : 0}
              {` `}
              {currencySymbol}
            </Text>
          </View>
        </View>
      </View>

      <View flex1 jContentCenter aItemsCenter mTop8>
        <Modal animationType="fade" transparent={true} visible={modalVisible}>
          <View flex1 jContentCenter aItemsCenter mTop8>
            <View aItemsCenter modalView>
              <Text marginBottom15 textCenter mainColor fontSize22>
                {homeScreenLogout}
                {/* {userInfo.admin && userInfo.admin.storename} */}
              </Text>
              <View flDirectionRow>
                <Pressable
                  style={[
                    HomeCss.button,
                    {
                      backgroundColor: maincolor,
                    },
                  ]}
                  onPress={handleLogout}>
                  <Text fontWeightBold whiteColor textCenter>
                    {agree}
                  </Text>
                </Pressable>
                <Pressable
                  style={[
                    HomeCss.button,
                    {
                      backgroundColor: maincolor,
                    },
                  ]}
                  onPress={() => setModalVisible(!modalVisible)}>
                  <Text fontWeightBold whiteColor textCenter>
                    {cancel}
                  </Text>
                </Pressable>
              </View>
            </View>
          </View>
        </Modal>
      </View>

      {/* Modal confirm delete account admin */}
      <View flex1 jContentCenter aItemsCenter mTop8>
        <Modal transparent={true} visible={modalDelete} animationType="fade">
          <View flex1 jContentCenter aItemsCenter mTop8>
            <View aItemsCenter modalView>
              <Text fontSize22 textCenter mainColor>
                {homeScreenDeleteAccountText1}
              </Text>
              <Text fontSize22 marginBottom15 textCenter mainColor>
                {homeScreenDeleteAccountText2}
              </Text>
              <View flDirectionRow>
                <Pressable
                  style={[
                    HomeCss.button,
                    {
                      backgroundColor: maincolor,
                    },
                  ]}
                  onPress={handleDeleteAdmin}>
                  <Text fontWeightBold whiteColor textCenter>
                    {agree}
                  </Text>
                </Pressable>
                <Pressable
                  style={[
                    HomeCss.button,
                    {
                      backgroundColor: maincolor,
                    },
                  ]}
                  onPress={() => setModalDelete(!modalDelete)}>
                  <Text fontWeightBold whiteColor textCenter>
                    {cancel}
                  </Text>
                </Pressable>
              </View>
            </View>
          </View>
        </Modal>
      </View>
      {loading ? (
        <View
          style={[
            HomeCss.loading,
            {
              backgroundColor: modalbackground,
            },
          ]}>
          <ActivityIndicator size="large" color={maincolor} />
        </View>
      ) : null}

      <View>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginHorizontal: scale(18),
            marginBottom: scale(4),
          }}>
          <View>
            <TouchableOpacity
              style={[
                HomeCss.iconPhone,
                {
                  backgroundColor: maincolor,
                  borderWidth: 0,
                  borderColor: maincolor,
                },
              ]}
              onPress={showModalDelete}>
              <MaterialCommunityIcons
                name="delete-forever"
                size={scale(30)}
                style={{
                  color: backgroundcolor,
                }}
              />
            </TouchableOpacity>
          </View>
          <View>
            <Text contactText textCenter blackColor>
              {contact}
            </Text>
            <Text contactText blackColor>{phoneContact}</Text>

            {/* <TextComponent
              text={contact}
              color={maincolor}
              size={22}
              weight="600"
            />

            <TextComponent
              text={phoneContact}
              color={maincolor}
              size={22}
              weight="600"
            /> */}
          </View>
          <View>
            <TouchableOpacity
              style={[
                HomeCss.iconPhone,
                {
                  borderColor: maincolor,
                  backgroundColor: maincolor,
                },
              ]}>
              <Feather
                name="phone-call"
                size={scale(25)}
                style={{
                  color: backgroundcolor,
                }}
                onPress={dialCall}
              />
            </TouchableOpacity>
          </View>
        </View>
      </View>
      {nameSelectOpition === 'Logo' ? (
        <View style={HomeCss.loadingScreen}>
          <ModalUploadLogo urlImages={urlImages} />
        </View>
      ) : null}
    </Container>
    // </SafeAreaView>
  );
};

export default React.memo(HomeScreen);
