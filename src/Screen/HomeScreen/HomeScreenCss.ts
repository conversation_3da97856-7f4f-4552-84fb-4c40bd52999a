import { StyleSheet } from 'react-native';
import { heightWindow, scale } from '../../utils/style/Reponsive';

export const HomeCss = StyleSheet.create({
  container: {
    flex: 1,
    marginHorizontal: scale(10),
    marginTop: scale(20),
  },
  button: {
    width: scale(100),
    borderRadius: scale(20),
    padding: scale(10),
    elevation: 2,
    marginHorizontal: scale(5),
  },
  productItem: {
    borderRadius: scale(12),
    width: '95%',
    height: heightWindow * 0.12,
    marginVertical: scale(8),
    justifyContent: 'center',
    alignItems: 'center',
  },
  imgProduct: {
    width: scale(44),
    lineHeight: scale(44),
    textAlign: 'center',
    marginTop: scale(9),
  },
  headerAvatar: {
    width: scale(50),
    height: scale(50),
    borderRadius: scale(25),
  },
  loading: {
    position: 'absolute',
    justifyContent: 'center',
    alignContent: 'center',
    top: -scale(20),
    bottom: 0,
    right: -scale(20),
    left: -scale(20),
  },
  iconPhone: {
    height: scale(50),
    width: scale(50),
    borderRadius: scale(25),
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    marginBottom: scale(15),
  },

  loadingScreen: {
    position: 'absolute',
    top: -scale(20),
    bottom: 0,
    right: -scale(10),
    left: -scale(10),
    justifyContent: 'center',
  },
  viewLogo: {
    width: scale(50),
    height: scale(50),
    alignItems: 'center',
  },
});
