import {
  <PERSON>List,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableHighlight,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect} from 'react';
import Container from '../../Component/Container/Container';
import SectionComponent from '../../Component/Section/SectionComponent';
import TitleComponent from '../../Component/Title/TitleComponent';
import {useAppSelector} from '../../hooks';
import ColComponent from '../../Component/Col/ColComponent';
import RowComponent from '../../Component/Row/RowComponent';
import HeaderComponent from '../../Component/Header/HeaderComponent';
import {extractDateTime, scale, setWidth} from '../../utils/style/Reponsive';
import {globalStyles} from '../../styles/globalStyles';
import {ActivityIndicator} from 'react-native';
import InputComponent from '../../Component/Input/InputComponent';
import {useDispatch} from 'react-redux';
import {ThunkDispatch} from '@reduxjs/toolkit';
import {
  fetchListRecept,
  fetchTaxCode,
  handleLoadingRecept,
  handlePageCurrentRecept,
} from '../../Redux/GetData';
import TextComponent from '../../Component/Text/TextComponent';
import {languages} from '../../constants';
import {Platform} from 'react-native';

interface IHandleProduct {
  [key: string]: string | undefined;
}
const ElectronicInvoice = (props: any) => {
  const {navigate} = props.navigation;
  const [state, setState] = React.useState<any>({
    loading: false,
    value: '',
    searchText: '',
  });
  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const {access_token} = useAppSelector(state => state.counter);
  const {listRecept, pageCurrentRecept, isLoadingRecept} = useAppSelector(
    state => state.getData,
  );
  let filteredData = [];
  filteredData = listRecept.filter((item: {payment_code: string}) =>
    item.payment_code?.includes(state.searchText),
  );
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);

  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): IHandleProduct | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: IHandleProduct = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {detailStafScreenStaffDetailBill,listInvoice,enterInvoice,dateCreateInvoice,
    totalPriceInvoice,paymentCodeInvoice

  }: any = langData;

  const refreshScreen = () => {
    dispatch(handleLoadingRecept());
    setTimeout(() => {
      dispatch(handleLoadingRecept());
    }, 1000);
  };
  const {maincolor, backgroundcolor, modalbackground} = xmlData;
  const getData = async () => {
    dispatch(
      fetchListRecept({
        access_token,
        pageCurrentRecept,
        messageApp: '',
      }),
    );
  };
  const searchPaymentCode = ()=>{
    dispatch(fetchTaxCode({access_token,
      messageApp: '',
      searchText: state.searchText,
    }))
}
  useEffect(() => {                                         
    getData();
  }, []);
  useEffect(() => {
    getData();
  }, [pageCurrentRecept]);
  const moveToInventoryDetailScreen = (item: any) => {
    navigate('ElectroicInvoiceDetail', {item});
  };
  const renderItem = ({
    item,
  }: {
    item: {
      id: string;
      valuetotal?: any;
      payment_code?: any;
      updated_at?: any;
    };
  }) => {
    return (
      <TouchableHighlight
        underlayColor="transparent"
        activeOpacity={0.9}
        onPress={() => moveToInventoryDetailScreen(item)}>
        <View
          style={{
            marginVertical: scale(6),
            borderWidth: 2,
            borderRadius: scale(8),
            borderColor: maincolor,
            padding: scale(8),
          }}>
          <RowComponent justify="space-evenly" styles={{flex: 5}}>
            <View
              style={{
                flex: 1,
                alignItems: 'flex-start',
              }}>
              <TextComponent text={`Id: ${item?.id}`} color="black" />
            </View>
            <View
              style={{
                flex: 4,
                alignItems: 'flex-end',
              }}>
              <TextComponent
                text={`${paymentCodeInvoice} : ${item?.payment_code}`}
                color="black"
              />
            </View>
          </RowComponent>
          <RowComponent justify="space-evenly" styles={{flex: 2}}>
            <View
              style={{
                flex: 1,
                alignItems: 'flex-start',
              }}>
              <TextComponent
                text={dateCreateInvoice}
                color="black"
                size={scale(15)}
                weight="700"
              />
            </View>
            <View
              style={{
                flex: 2,
                alignItems: 'flex-end',
              }}>
              <TextComponent
                text={`${totalPriceInvoice}: ${item?.valuetotal
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}đ`}
                color="black"
                size={scale(15)}
                weight="700"
              />
            </View>
          </RowComponent>
          <RowComponent>
            <View
              style={{
                flex: 1,
                alignItems: 'flex-start',
              }}>
              <TextComponent
                text={extractDateTime(item.updated_at).date}
                color="black"
                size={scale(15)}
                weight="700"
              />
            </View>
            <View
              style={{
                flex: 1,
                alignItems: 'flex-end',
              }}>
              <TextComponent
                text={detailStafScreenStaffDetailBill}
                color={maincolor}
                size={scale(18)}
                weight="700"
              />
            </View>
          </RowComponent>
        </View>
      </TouchableHighlight>
    );
  };

  const handleLoadMore = () => {
    dispatch(handlePageCurrentRecept());
  };

  return (
    <View style={{flex: 1}}>
      <Container
        styles={{
          flex: 20,
        }}>
        <SectionComponent
          styles={{
            flex: Platform.OS === 'ios' ? 1 : 1.5,
          }}>
          <HeaderComponent
            text={listInvoice}
            color={maincolor}
            size={scale(26)}
            // refreshScreen={refreshScreen}
            // refreshBtn
            
          />
        </SectionComponent>
        <SectionComponent
          styles={{
            flex: 19,
          }}>
          <View
            style={{
              flex: Platform.OS === 'ios' ? 1 : 1.5,
            }}>
            <InputComponent
              searchText={state.searchText}
              placeholder={enterInvoice}
              editable
              onChange={searchText => setState({...state, searchText})}
              clearText={() => setState({...state, searchText: ''})}
              searchIcon = {state.searchText ? true :false}
              onSearch={state.searchText ? searchPaymentCode : undefined}
              size={(scale(22))}
              cleanButton = {state.searchText ? true :false}
            />
          </View>

          <View style={{flex: 18}}>
            <FlatList
              style={{flex: 1}}
              data={filteredData}
              renderItem={renderItem}
              keyExtractor={item => item.id}
              onEndReached={handleLoadMore}
              onEndReachedThreshold={1}
            />
          </View>
        </SectionComponent>
      </Container>
      {isLoadingRecept ? (
        <View
          style={[
            globalStyles.loading,
            {
              backgroundColor: modalbackground,
            },
          ]}>
          <ActivityIndicator size="large" color={maincolor} />
        </View>
      ) : null}
    </View>
  );
};

export default ElectronicInvoice;
