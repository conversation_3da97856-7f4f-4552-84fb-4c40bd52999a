import {
  ActivityIndicator,
  <PERSON>ing,
  SafeAreaView,
  ScrollView,
  StatusBar,
  TextInput,
  TouchableOpacity,
  Platform,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import SelectDropdown from 'react-native-select-dropdown';

import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';
import Entypo from 'react-native-vector-icons/Entypo';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {
  Text,
  View,
  ModalAddCustomers,
  DetailCustomers,
} from '../../Component/index';

import {CustommersScreenCss} from './CustommersScreenCss';
import {useDispatch} from 'react-redux';
import {setFlagCustomers, setShowCreateCustomers} from '../../Redux/Slide';

import {
  heightWindow,
  scale,
  toNonAccentVietnamese,
} from '../../utils/style/Reponsive';
import {useAppSelector} from '../../hooks';
import {ThunkDispatch} from '@reduxjs/toolkit';
import {handleGetListCustomers} from '../../api/handleApi';
import {languages} from '../../constants';

interface IProps {
  navigation: {
    pop: (count?: number) => void;
  };
}
interface IListItem {
  admin_id: number;
  create_date: string;
  created_at: string;
  id: number;
  name: string;
  phone: number;
  point: string;
  searchTitle: string;
  total_money: number;
  updated_at: string;
}
interface IStaffScreen {
  [key: string]: string | undefined;
  custommersScreenTitle?: string;
  custommersScreenListCustommers?: string;
  customersScreenNewCustomer?: string;
  customersScreenOldCustomer?: string;
  customersScreenPaymentMax?: string;
  customersScreenPaymentMin?: string;
  customersScreenFindCustomer?: string;
  customersScreenSortList?: string;
}
const CustommersScreen = (props: IProps) => {
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {
    backgroundcolor,
    blackcolor,
    diamondmember,
    goldmember,
    maincolor,
    platiummember,
    sivermember,
    modalbackground,
  } = xmlData;
  const {flagListCustomers, access_token, nameCustomer, flagCustomers} =
    useAppSelector(state => state.counter);

  const {navigation} = props;
  const [listCustomers, setListCustomers] = useState<IListItem[]>([]);
  const [searchCustomers, setSearchCustomers] = useState<string>('');
  const [detailCustomer, setDetailCustomer] = useState<any>();
  const [checkValue, setCheckValue] = useState<boolean>(false);
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): IStaffScreen | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: IStaffScreen = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    customersScreenTitle,
    customersScreenListCustommers,
    customersScreenNewCustomer,
    customersScreenOldCustomer,
    customersScreenPaymentMax,
    customersScreenPaymentMin,
    customersScreenFindCustomer,
    customersScreenSortList,
  }: any = langData;
  const listSearchCustomers = [
    customersScreenNewCustomer,
    customersScreenOldCustomer,
    customersScreenPaymentMax,
    customersScreenPaymentMin,
  ];
  const SortTypes = {
    CREATE_TIME_MAX: customersScreenNewCustomer,
    CREATE_TIME_MIN: customersScreenOldCustomer,
    PAYMENT_MAX: customersScreenPaymentMax,
    PAYMENT_MIN: customersScreenPaymentMin,
  };
  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const handleGoback = useCallback(() => {
    navigation.pop(1);
  }, [navigation]);

  const dialCall = (number: number) => {
    const prefix = Platform.OS === 'android' ? 'tel:' : 'telprompt:';
    const phoneNumber = `${prefix}${number}`;
    Linking.openURL(phoneNumber);
  };

  const createCustomers = useCallback(() => {
    dispatch(
      setShowCreateCustomers({
        valid: false,
        nameOpiton: 'CreateCustomers',
      }),
    );
  }, [dispatch]);

  const detailCustomers = useCallback(
    (item: IListItem) => {
      setDetailCustomer(item);
      dispatch(
        setShowCreateCustomers({
          valid: false,
          nameOpiton: 'DetailCustomers',
        }),
      );
    },
    [dispatch],
  );

  const getListCustomers = () => {
    dispatch(setFlagCustomers());
    try {
      handleGetListCustomers(access_token)
        .then(res => {
          setListCustomers(
            res?.data?.customer.map(
              (item: {searchTitle: string; name: string}) => {
                item.searchTitle = toNonAccentVietnamese(
                  item.name.toLowerCase(),
                );
                return item;
              },
            ),
          );
        })
        .catch((err: string) => {
          console.log(err);
        })
        .finally(() => {
          dispatch(setFlagCustomers());
        });
    } catch (err: unknown) {
      console.log(err);
    }
  };
  useEffect(() => {
    getListCustomers();
  }, []);

  useEffect(() => {
    getListCustomers();
  }, [flagListCustomers]);

  const selectItem = (selectedItem: string) => {
    setCheckValue(checkValue => !checkValue);
    switch (selectedItem) {
      case SortTypes.CREATE_TIME_MAX:
        sortCreateOld();
        break;
      case SortTypes.CREATE_TIME_MIN:
        sortCreateNew();
        break;
      case SortTypes.PAYMENT_MAX:
        sortPaymentMax();
        break;
      case SortTypes.PAYMENT_MIN:
        sortPaymentMin();
        break;
      default:
        break;
    }
  };

  const sortCreateOld = () => {
    setListCustomers(
      [...listCustomers].sort(
        (
          a: {
            created_at: string;
          },
          b: {
            created_at: string;
          },
        ) => b.created_at.localeCompare(a.created_at),
      ),
    );
  };

  const sortCreateNew = () => {
    setListCustomers(
      [...listCustomers].sort(
        (
          a: {
            created_at: string;
          },
          b: {
            created_at: string;
          },
        ) => a.created_at.localeCompare(b.created_at),
      ),
    );
  };

  const sortPaymentMax = () => {
    setListCustomers(
      [...listCustomers].sort(
        (
          a: {
            total_money: number;
          },
          b: {
            total_money: number;
          },
        ) => b.total_money - a.total_money,
      ),
    );
  };

  const sortPaymentMin = () => {
    setListCustomers(
      [...listCustomers].sort(
        (
          a: {
            total_money: number;
          },
          b: {
            total_money: number;
          },
        ) => a.total_money - b.total_money,
      ),
    );
  };
  const refreshScreen = useCallback(() => {
    dispatch(setFlagCustomers());
    const timeId = setTimeout(() => {
      dispatch(setFlagCustomers());
      getListCustomers();
    }, 1500);
    return () => clearTimeout(timeId);
  }, []);

  return (
    <SafeAreaView style={CustommersScreenCss.container}>
      <StatusBar backgroundColor="white" barStyle="dark-content" />
      <View
        style={{
          marginHorizontal: scale(6),
        }}>
        <View flDirectionRow  aItemsCenter jContentBetween>
          <TouchableOpacity
            onPress={createCustomers}
            style={[
              CustommersScreenCss.btnHandleCreateCustomer,
              {
                backgroundColor: maincolor,
              },
            ]}>
            <Text textCenter whiteColor fontSize16 fontWeight600>
              {customersScreenTitle}
            </Text>
          </TouchableOpacity>
          <View>
            <SimpleLineIcons
              name="refresh"
              size={40}
              color={maincolor}
              onPress={refreshScreen}
            />
          </View>
          <View>
            <TouchableOpacity onPress={handleGoback}>
              <Entypo name="log-out" size={40} color={maincolor} />
            </TouchableOpacity>
          </View>
        </View>
        <View>
          <Text textCenter fontSize28 fontWeight700 mainColor>
            {customersScreenListCustommers}
          </Text>
        </View>
        <View w_100 flDirectionRow>
          <View
            searchBar
            h_40
            style={{
              width: '55%',
            }}>
            <Ionicons
              name="search"
              size={34}
              style={CustommersScreenCss.iconSearch}
              color={blackcolor}
            />
            <TextInput
              placeholder={customersScreenFindCustomer}
              onChangeText={setSearchCustomers}
              placeholderTextColor={blackcolor}
              style={{
                color: blackcolor,
              }}
            />
          </View>
          <View
            style={{
              width: '1%',
            }}
          />
          <View w_40>
            {/* <DropDownComponents /> */}
            <SelectDropdown
              data={listSearchCustomers}
              defaultButtonText={customersScreenSortList}
              buttonStyle={[
                CustommersScreenCss.selectDropdown,
                {
                  backgroundColor: maincolor,
                },
              ]}
              buttonTextStyle={{
                color: backgroundcolor,
              }}
              onSelect={selectedItem => selectItem(selectedItem)}
              buttonTextAfterSelection={selectedItem => selectedItem}
              rowTextForSelection={item => item}
            />
          </View>
        </View>

        <ScrollView
          style={{
            height: heightWindow * 0.75,
          }}
          showsVerticalScrollIndicator={false}>
          {listCustomers &&
            listCustomers
              .filter(
                (item: {name: string; phone: number; searchTitle: string}) =>
                  item.name.includes(searchCustomers) ||
                  item.phone.toString().includes(searchCustomers) ||
                  item.searchTitle.includes(searchCustomers.toLowerCase()),
              )
              .map((item: IListItem, index: number) => (
                <View
                  listCustomers
                  key={index}
                  style={{
                    backgroundColor:
                      item.total_money < 10000000
                        ? backgroundcolor
                        : item.total_money < 25000000
                        ? sivermember
                        : item.total_money < 50000000
                        ? goldmember
                        : item.total_money < 100000000
                        ? platiummember
                        : diamondmember,
                  }}>
                  <View flDirectionRow jContentBetween aItemsCenter>
                    <View w_30>
                      <TouchableOpacity
                        onPress={() => detailCustomers(item)}
                        style={CustommersScreenCss.view100Percentage}>
                        <View w_20>
                          <Text
                            numberOfLines={1}
                            blackColor
                            fontWeight600
                            fontSize18
                            paddingLeft6>
                            {item.name.length < 15
                              ? item.name
                              : `${item.name.substring(0, 15)}...`}
                          </Text>
                        </View>
                        <View w_40 aItemsCenter>
                          <Text
                            numberOfLines={1}
                            blackColor
                            fontWeight600
                            fontSize18>
                            {item.point == null
                              ? 0
                              : item.point.length < 6
                              ? item.point
                                  .toString()
                                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                              : `${item.point
                                  .substring(0, 6)
                                  .toString()
                                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}...`}
                          </Text>
                        </View>
                      </TouchableOpacity>
                    </View>

                    <View w_70>
                      <View flDirectionRow>
                        <View jContentCenter aItemsCenter w_80>
                          <Text
                            blackColor
                            fontWeight600
                            fontSize16
                            onPress={() => {
                              dialCall(item.phone);
                            }}>
                            {item.phone}
                          </Text>
                        </View>
                        <View aItemsCenter>
                          <MaterialIcons
                            name="call"
                            size={25}
                            color="#000"
                            onPress={() => {
                              dialCall(item.phone);
                            }}
                          />
                        </View>
                      </View>
                    </View>
                  </View>
                </View>
              ))}
        </ScrollView>
      </View>
      {flagCustomers ? (
        <View
          style={[
            CustommersScreenCss.viewLoading,
            {
              backgroundColor: modalbackground,
            },
          ]}>
          <ActivityIndicator size="large" color={maincolor} />
        </View>
      ) : null}
      {nameCustomer === 'CreateCustomers' ? (
        <View
          style={[
            CustommersScreenCss.modalOption,
            {
              backgroundColor: modalbackground,
            },
          ]}>
          <ModalAddCustomers />
        </View>
      ) : null}
      {nameCustomer === 'DetailCustomers' ? (
        <View
          style={[
            CustommersScreenCss.modalOption,
            {
              backgroundColor: backgroundcolor,
            },
          ]}>
          <DetailCustomers detailCustomer={detailCustomer} />
        </View>
      ) : null}
    </SafeAreaView>
  );
};

export default React.memo(CustommersScreen);
