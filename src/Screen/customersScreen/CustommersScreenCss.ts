import { StyleSheet } from 'react-native';
import { scale, widthWindow } from '../../utils/style/Reponsive';

export const CustommersScreenCss = StyleSheet.create({
  container: {
    flex: 1,
  },

  btnHandleCreateCustomer: {
    marginVertical: 10,
    height: 48,
    width: widthWindow * 0.42,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 10,
  },

  viewLoading: {
    position: 'absolute',
    justifyContent: 'center',
    alignContent: 'center',
    top: -20,
    bottom: 0,
    right: -20,
    left: -20,
  },
  iconSearch: {
    paddingHorizontal: 8,
  },
  modalOption: {
    position: 'absolute',
    top: -0,
    bottom: 0,
    right: -0,
    left: -0,
    alignContent: 'center',
    justifyContent: 'center',
  },
  selectDropdown: {
    justifyContent: 'center',

    borderRadius: 8,
    marginVertical: scale(6),
    height: scale(44),
    width: '100%',
  },
  view100Percentage: {
    width: '100%',
    flexDirection: 'row',
  },
});
