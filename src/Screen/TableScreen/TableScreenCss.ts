import { StyleSheet } from 'react-native';
import { scale } from '../../utils/style/Reponsive';
import { heightPercentageToDP as hp , widthPercentageToDP as wp} from 'react-native-responsive-screen'

export const TableScreenCss = StyleSheet.create({
  container: {
    flex: 1,
  },
  modalView: {
    width: wp('96%'),
    borderRadius: scale(20),
    padding: scale(20),
    alignItems: 'center',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  buttonHeader: {
    width: '33%',
    height: scale(42),
    borderRadius: scale(20),
    elevation: 2,
    justifyContent: 'center',
    marginVertical: scale(4),
  },
  button: {
    height: scale(42),
    width: '33%',
    borderRadius: scale(20),
    elevation: 2,
    justifyContent: 'center',
    marginHorizontal: scale(12),
    marginVertical: scale(4),
    marginTop: scale(10),
  },

  buttonDeleteTable: {
    width: '33%',
    height: scale(42),
    borderRadius: scale(20),
    elevation: scale(2),
    justifyContent: 'center',
    marginHorizontal: scale(6),
    marginVertical: scale(4),
  },
  loadingView: {
    position: 'absolute',
    justifyContent: 'center',
    alignContent: 'center',
    top: -scale(20),
    bottom: 0,
    right: -scale(20),
    left: -scale(20),
  },
  checkDeleteTable: {
    marginLeft: scale(50),
  },
  gridItem: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    elevation: 2, // Bóng cho Android
    shadowColor: '#000', // Bóng cho iOS
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
});
