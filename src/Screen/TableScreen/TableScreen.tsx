import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {
  ActivityIndicator,
  Alert,
  Modal,
  Pressable,
  ScrollView,
  TouchableOpacity, StyleSheet
} from 'react-native';

import {useDispatch} from 'react-redux';
import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {Text, View, Confirm} from '../../Component/index';

import { DraggableGrid } from 'react-native-draggable-grid';
import {
  checkOutTable,
  deleteTable,
  deleteTables,
  getListTables,
  sortRankTable,
} from '../../api/handleApi';
import {
  setFlagTable,
  setLoadingScreen,
  setStateShowOption,
} from '../../Redux/Slide';

import {TableScreenCss} from './TableScreenCss';
import {heightWindow, scale, widthWindow} from '../../utils/style/Reponsive';
import {useAppSelector} from '../../hooks';
import {ThunkDispatch} from '@reduxjs/toolkit';
import {languages} from '../../constants';
import Container from '../../Component/Container/Container';
interface IItem {
  TotalPrice: number;
  category_id: number;
  check: number;
  id: string;
  image: string;
  price: string;
  quantity: string;
  status: number;
  title: string;
  vat: number;
  qtyStatus: number;
}
interface IListItem {
  id: number;
  price: number;
  check: number;
  vat: number;
  quantity: number;
  image: string;
  title: string;
  category_id: number;
  status: number;
  qtyStatus: number;
}

interface IUser {
  admin_id: number;
  created_at: string;
  id: number;
  listitem?: string | IListItem[];
  name_user: string;
  status: number;
  tablename: number;
  updated_at: string;
  user_id: string;
  userordered?: {Name: string; id: number}[];
  checked: boolean;
}

interface TablesScreen {
  [key: string]: string | undefined;
  tableScreenAddTable?: string;
  tableScreenDeleteTable?: string;
  tableScreenDeleteTables?: string;
  tableScreenListTables?: string;
  tableScreenTableNumber?: string;
  tableScreenNameFood?: string;
  tableScreenQuantityFood?: string;
  tableScreenPriceFood?: string;
  tableScreenCancelOrders?: string;
  tableScreenExitTable?: string;
  tableScreenWarning?: string;
  cancel?: string;
  agree?: string;
  tableScreenMessDeleteTables?: string;
}
interface SortRankItem {
  id: string;
  sort_rank: string;
}
interface SortRanks {
  sortedData: SortRankItem[];
}
const TableScreen = () => {
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {
    maincolor,
    blackcolor,
    modalbackground,
    graycolor,
    useronline,
    userstop,
    backgroundcolor,
  } = xmlData;
  const [modalVisibleDelete, setModalVisibleDelete] = useState<boolean>(false);
  const [modalDetailProduct, setModalDetailProduct] = useState<boolean>(false);
  const [modalCancelTable, setModalCancelTable] = useState<boolean>(false);
  // const [modalDeleteTables, setModalDeleteTables] = useState<boolean>(false);
  const [nameTable, setNameTable] = useState<string>('');

  const [userId, setUserId] = useState<string>();
  const [tableIdOrder, setTableIdOrder] = useState<number>();
  const [dataDetail, setDataDetail] = useState<IItem[]>([]);
  const [idTableDelete, setIdTableDelete] = useState<number | undefined>();
  const [state, setState] = useState<{
    data: IUser[];
    modalVisibleDelete: boolean;
  }>({
    data: [],
    modalVisibleDelete: false,
  });
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): TablesScreen | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: TablesScreen = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    tableScreenAddTable,
    tableScreenDeleteTable,
    tableScreenDeleteTables,
    tableScreenListTables,
    tableScreenTableNumber,
    tableScreenNameFood,
    tableScreenQuantityFood,
    tableScreenPriceFood,
    tableScreenCancelOrders,
    tableScreenExitTable,
    tableScreenWarning,
    cancel,
    agree,
    deleteTableSuccess,
    deleteTableFail,
    messageApp,
    cancelOrderSuccess,
    cancelOrderFail,
    deleteMutilTableFail,
    deleteMutilTableSuccess,
    doNotChooseTableDelete,
    tableScreenMessDeleteTables,
  }: any = langData;
  const {flagTable, isLoadingScreen, access_token, nameSelectOpition} =
    useAppSelector(state => state.counter);
  let nameOpiton = nameSelectOpition;
  //Handle get list Table
  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();

  const getListTable = async () => {
    try {
      getListTables(access_token)
      .then(res => {
        if (res.data.status_code === 200) {
          const filteredSortedData = res.data?.listtable
            ?.filter((item: { status: number }) => item.status !== -1)
            ?.sort((a: { sort_rank?: string }, b: { sort_rank?: string  }) => {
              const rankA = parseInt(a.sort_rank ?? '9999');
              const rankB = parseInt(b.sort_rank ?? '9999');
              return rankA - rankB;
            });
    
          setState({
            ...state,
            data: filteredSortedData,
          });
        }
      })
        .catch((err: string) => {
          console.log(err);
        });
    } catch (err: unknown) {
      console.log(err);
    }
  };
  useEffect(() => {
    state.data &&
      state.data.sort(
        (
          a: {
            tablename: number;
          },
          b: {
            tablename: number;
          },
        ) => {
          return a.tablename - b.tablename;
        },
      );
    getListTable();
  }, [flagTable]);

  const cancelOrderTable: () => void = async () => {
    dispatch(
      setLoadingScreen({
        valid: true,
      }),
    );
    try {
      checkOutTable(tableIdOrder, userId, access_token)
        .then(res => {
          if (res.data.status_code === 200) {
            Alert.alert(messageApp, cancelOrderSuccess);
          } else if (res.data.status_code === 404) {
            Alert.alert(messageApp, cancelOrderFail);
          } else if (res.data.status_code === 400) {
            Alert.alert(messageApp, res.data.message);
          }
        })
        .catch((err: string) => {
          console.log(err);
        })
        .finally(() => {
          dispatch(setLoadingScreen({valid: false}));
          dispatch(setFlagTable());
          setModalCancelTable(!modalCancelTable);
          setModalDetailProduct(!modalDetailProduct);
        });
    } catch (err: unknown) {
      console.log(err);
    }
  };

  const createTable: () => void = () => {
    dispatch(setStateShowOption({valid: false, nameOpiton: 'ShowCreateTable'}));
  };

  const handleDetailProduct = (item: IUser) => {
    if (item.listitem === null) {
      setIdTableDelete(item.id);
      setModalVisibleDelete(!modalVisibleDelete);
    }
    if (item.listitem !== null) {
      if (item.listitem === null) {
        setModalDetailProduct(!modalDetailProduct);
      } else {
        const jsonObject =
          typeof item.listitem === 'string' ? JSON.parse(item.listitem) : null;
        const itemArray: IItem[] = jsonObject
          ? Object.values(jsonObject.item)
          : [];

        setDataDetail(itemArray);
      }
      setModalDetailProduct(!modalDetailProduct);
    }
  };

  const handleDeleteTable: () => void = async () => {
    try {
      dispatch(
        setLoadingScreen({
          valid: true,
        }),
      );
      deleteTable(idTableDelete, access_token).then(res => {
        if (res.data.status_code === 200) {
          dispatch(setFlagTable());
          Alert.alert(messageApp, deleteTableSuccess);
        } else {
          Alert.alert(messageApp, deleteTableFail);
        }
      });
    } catch (err: unknown) {
      console.log(err);
    } finally {
      dispatch(setLoadingScreen({valid: false}));
    }
    setModalVisibleDelete(!modalVisibleDelete);
  };


  // const listtable =
  //   state.data &&
  //   state.data
  //     .filter((item: {checked: boolean}) => item.checked === true)
  //     .map((item: {id: number}) => item.id);

  // const handleDeleteTables = async () => {
  //   try {
  //     dispatch(setLoadingScreen({valid: true}));
  //     deleteTables(listtable, access_token)
  //       .then(res => {
  //         if (res.data.status_code === 200) {
  //           Alert.alert(messageApp, deleteMutilTableSuccess);
  //         } else if (res.data.status_code === 400) {
  //           Alert.alert(messageApp, doNotChooseTableDelete);
  //         } else {
  //           Alert.alert(messageApp, deleteMutilTableFail);
  //         }
  //       })
  //       .catch((err: string) => {
  //         console.log(err);
  //       })
  //       .finally(() => {
  //         dispatch(setLoadingScreen({valid: false}));
  //         setModalDeleteTables(false);
  //         dispatch(setFlagTable());
  //       });
  //   } catch (err: unknown) {
  //     console.log(err);
  //   }
  // };

  const refreshScreen = useCallback(() => {
    dispatch(setLoadingScreen({valid: true}));
    const timerId = setTimeout(() => {
      dispatch(setLoadingScreen({valid: false}));
      dispatch(setFlagTable());
    }, 1000);

    return () => {
      clearTimeout(timerId);
    };
  }, [dispatch, setFlagTable, setLoadingScreen]);
  const NUM_ITEMS = state.data.length || 0;

  type Item = {
    key: any;
    label: any;
    id: string;
    tablename?: string;
  };

  const initialData: Item[] = [...Array(NUM_ITEMS)].map((d, index) => {
    return {
      key: `item-${index}`,
      label: String(++index) + '',
      id: `item-${index}`,
      item:d
    };
  });
  const RenderCardItem = () => {
    const [data, setData] = useState(initialData);
    const stableListProduct: any = useMemo(() => state.data, [state.data]);
    const debounceSetSortedData = useCallback(
      (updatedSortRanks: SortRanks) => {
        setTimeout(() => {
          sortRankTable(access_token, updatedSortRanks);
        }, 1000);
      },
      [access_token],
    );
    const renderItem = (item: any ) => {
      const itemIndex = item.key.split('-')[1];
      const table = stableListProduct[itemIndex];
      return (
        <View
        style={{flexDirection:'row',flexWrap:'wrap' }}
        >
          <View
            style={[
              {
                width: 100, height: 100,
                margin: 5,
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: 10,
              },
            ]}>
            
              <MaterialCommunityIcons
                name="table-chair"
                size={scale(60)}
                color={
                  stableListProduct[item.key.split('-')[1]].listitem === null
                    ? userstop
                    : useronline
                }
              />

            <Text
              fontSize18
              fontWeight600
              textCenter
              blackColor
              numberOfLines={1}>
              {stableListProduct[item?.key?.split('-')[1]].tablename.toString()
                .length < 6
                ? `${stableListProduct[item?.key?.split('-')
                [1]]?.tablename}`
                : `${stableListProduct[item?.key?.split('-')[1]]?.tablename
                    .toString()
                    .substring(0, 6)}...`}

            </Text>
          </View>
        </View>
      );
    };

    return (
      <DraggableGrid
      numColumns={3}
      data={data}
      renderItem={renderItem}
      delayLongPress={500} 
      onDragRelease={(newData) => {
        setData(newData);
        const sortedData: SortRankItem[] = newData.map((item, index) => ({
          id: stableListProduct[item.key.split('-')[1]].id.toString(),
          sort_rank: index.toString(),
        }));
        debounceSetSortedData({ sortedData });
      }}
      onItemPress={(item) => {
        const itemIndex = item.key.split('-')[1];
        const extraData = stableListProduct[itemIndex];

        const enrichedItem = {
          ...item,
          ...extraData,
        };

        handleDetailProduct(enrichedItem);
      }}
    />
    );
  };

  return (
    <Container isScroll>
      <View>
        <View flDirectionRow jContentAround>
          <TouchableOpacity
            style={[
              TableScreenCss.buttonHeader,
              {
                backgroundColor: maincolor,
              },
            ]}
            onPress={() => {
              createTable();
            }}>
            <Text whiteColor fontWeightBold textCenter fontSize18>
              {tableScreenAddTable}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            disabled={true}
            style={[
              TableScreenCss.buttonDeleteTable,
              {
                // backgroundColor: checked ? graycolor : maincolor,
                width: isCheckLang === 2 ? '40%' : '40%',
              },
            ]}
            // onPress={() => {
            //   setModalDeleteTables(true);
            // }}
            >
            {/* <Text whiteColor fontWeightBold textCenter fontSize18>
              {tableScreenDeleteTables}
            </Text> */}
          </TouchableOpacity>
          <View jContentCenter>
            <SimpleLineIcons
              name="refresh"
              size={scale(40)}
              color={maincolor}
              onPress={refreshScreen}
            />
          </View>
        </View>
        <View>
          <Text fontSize28 fontWeight700 textCenter mainColor>
            {tableScreenListTables}
          </Text>
        </View>
      </View>
      <RenderCardItem />
        {/* {isLoadingScreen ? (
        <View
          style={[
            TableScreenCss.loadingView,
            {
              backgroundColor: modalbackground,
            },
          ]}>
          <ActivityIndicator size="large" color={maincolor} />
        </View>
      ) : null} */}
      {/* Customer Show Table */}
      {nameOpiton === 'ShowCreateTable' ? <Confirm /> : null}

      {/* Modal delete */}
      <View flex1 jContentCenter aItemsCenter mHorizontal10>
        <Modal
          transparent={true}
          visible={modalVisibleDelete}
          animationType="fade">
          <View flex1 jContentCenter aItemsCenter mTop22 mHorizontal10>
            <View
              modalView
              w_100
              style={{
                width: widthWindow * 0.95,
              }}>
              <Text
                fontSize22
                fontWeight600
                blackColor
                textCenter
                marginBottom15>
                {tableScreenDeleteTable} {nameTable ? nameTable : ''}
              </Text>
              <View flDirectionRow jContentCenter aItemsCenter jContentAround>
                <Pressable
                  style={[
                    TableScreenCss.buttonHeader,
                    {
                      backgroundColor: maincolor,
                    },
                  ]}
                  onPress={handleDeleteTable}>
                  <Text whiteColor fontWeightBold textCenter fontSize18>
                    {agree}
                  </Text>
                </Pressable>
                <Pressable
                  style={[
                    TableScreenCss.buttonHeader,
                    {
                      backgroundColor: maincolor,
                    },
                  ]}
                  onPress={() => setModalVisibleDelete(false)}>
                  <Text whiteColor fontWeightBold textCenter fontSize18>
                    {cancel}
                  </Text>
                </Pressable>
              </View>
            </View>
          </View>
          {isLoadingScreen ? (
            <View
              style={[
                TableScreenCss.loadingView,
                {
                  backgroundColor: modalbackground,
                },
              ]}>
              <ActivityIndicator size="large" color={maincolor} />
            </View>
          ) : null}
        </Modal>
      </View>
      {/* Modal Detail Product on Table */}
      <View flex1 jContentCenter aItemsCenter mHorizontal10>
        <Modal
          animationType="fade"
          transparent={true}
          visible={modalDetailProduct}>
          <View flex1 jContentCenter aItemsCenter mTop22>
            <View
              style={[
                TableScreenCss.modalView,
                {
                  backgroundColor: backgroundcolor,
                  shadowColor: blackcolor,
                },
              ]}>
              <Text
                fontSize22
                fontWeight600
                blackColor
                textCenter
                marginBottom15>
                {tableScreenTableNumber} {nameTable ? nameTable : ''}
              </Text>
              <ScrollView
                style={{
                  height: heightWindow * 0.33,
                  width: widthWindow * 0.8,
                }}
                showsVerticalScrollIndicator={false}
                showsHorizontalScrollIndicator={false}>
                <View>
                  <View flDirectionRow>
                    <View w_30>
                      <Text fontSize20 fontWeight600 blackColor>
                        {tableScreenNameFood}
                      </Text>
                    </View>
                    <View w_30 aItemsCenter>
                      <Text fontSize20 fontWeight600 blackColor>
                        {tableScreenQuantityFood}
                      </Text>
                    </View>
                    <View w_30>
                      <Text fontSize20 fontWeight600 blackColor>
                        {tableScreenPriceFood}
                      </Text>
                    </View>
                  </View>
                  <View>
                    {dataDetail ? (
                      <>
                        {dataDetail.map(
                          (item: {
                            id: string;
                            title: string;
                            quantity: string;
                            qtyStatus: number;
                            price: string;
                          }) => {
                            return (
                              <View flDirectionRow w_100 p_2 key={item.id}>
                                <View w_30>
                                  <Text fontSize16 fontWeight500 blackColor>
                                    {item.title}
                                  </Text>
                                </View>
                                <View w_30 aItemsCenter>
                                  <Text fontSize16 fontWeight500 blackColor>
                                    {item.quantity}
                                  </Text>
                                </View>
                                <View w_30>
                                  <Text fontSize16 fontWeight500 blackColor>
                                    {item.price}
                                  </Text>
                                </View>
                              </View>
                            );
                          },
                        )}
                      </>
                    ) : null}
                  </View>
                </View>
              </ScrollView>
              <View flDirectionRow w_100>
                <View
                  style={[
                    TableScreenCss.buttonHeader,
                    {
                      backgroundColor: maincolor,
                      width:
                        isCheckLang == 0
                          ? '40%'
                          : isCheckLang == 1
                          ? '40%'
                          : '40%',
                      marginLeft: widthWindow * 0.09,
                    },
                  ]}>
                  <Pressable
                    onPress={() => {
                      setModalCancelTable(true);
                    }}>
                    <Text whiteColor fontWeightBold textCenter fontSize18>
                      {tableScreenCancelOrders}
                    </Text>
                  </Pressable>
                </View>

                <View
                  style={[
                    TableScreenCss.buttonHeader,
                    {
                      backgroundColor: maincolor,
                      width:
                        isCheckLang == 0
                          ? '40%'
                          : isCheckLang == 1
                          ? '40%'
                          : '40%',
                      marginLeft: 8,
                    },
                  ]}>
                  <Pressable
                    onPress={() => setModalDetailProduct(!modalDetailProduct)}>
                    <Text whiteColor fontWeightBold textCenter fontSize18>
                      {tableScreenExitTable}
                    </Text>
                  </Pressable>
                </View>

                {/* Modal cancel order */}
                <View flex1 jContentCenter aItemsCenter mTop22 mHorizontal10>
                  <Modal
                    transparent={true}
                    visible={modalCancelTable}
                    animationType="fade">
                    <View
                      flex1
                      jContentCenter
                      aItemsCenter
                      mTop22
                      mHorizontal10>
                      <View
                        style={[
                          TableScreenCss.modalView,
                          {
                            backgroundColor: backgroundcolor,
                            shadowColor: blackcolor,
                          },
                        ]}>
                        <Text
                          fontSize22
                          fontWeight600
                          blackColor
                          textCenter
                          marginBottom15>
                          {tableScreenWarning}
                        </Text>
                        <View flDirectionRow w_100 jContentAround>
                          <Pressable
                            style={[
                              TableScreenCss.buttonHeader,
                              {
                                backgroundColor: maincolor,
                              },
                            ]}
                            onPress={cancelOrderTable}>
                            <Text
                              whiteColor
                              fontWeightBold
                              textCenter
                              fontSize18>
                              {agree}
                            </Text>
                          </Pressable>
                          <Pressable
                            style={[
                              TableScreenCss.buttonHeader,
                              {
                                backgroundColor: maincolor,
                              },
                            ]}
                            onPress={() => setModalCancelTable(false)}>
                            <Text
                              whiteColor
                              fontWeightBold
                              textCenter
                              fontSize18>
                              {cancel}
                            </Text>
                          </Pressable>
                        </View>
                      </View>
                    </View>
                    {isLoadingScreen ? (
                      <View
                        style={[
                          TableScreenCss.loadingView,
                          {
                            backgroundColor: modalbackground,
                          },
                        ]}>
                        <ActivityIndicator size="large" color={maincolor} />
                      </View>
                    ) : null}
                  </Modal>
                </View>
              </View>
            </View>
          </View>
        </Modal>
      </View>
      {/* Modal mutil delete table */}
      {/* <View flex1 jContentCenter aItemsCenter mHorizontal10>
        <Modal
          transparent={true}
          visible={modalDeleteTables}
          animationType="fade">
          <View flex1 jContentCenter aItemsCenter mTop22 mHorizontal10>
            <View
              style={[
                TableScreenCss.modalView,
                {
                  backgroundColor: backgroundcolor,
                  shadowColor: blackcolor,
                },
              ]}>
              <Text
                fontSize22
                fontWeight600
                blackColor
                textCenter
                marginBottom15>
                {tableScreenMessDeleteTables}
              </Text>
              <View flDirectionRow w_100 aItemsCenter jContentAround>
                <Pressable
                  style={[
                    TableScreenCss.buttonHeader,
                    {
                      backgroundColor: maincolor,
                    },
                  ]}
                  onPress={handleDeleteTables}>
                  <Text whiteColor fontWeightBold textCenter fontSize18>
                    {agree}
                  </Text>
                </Pressable>
                <Pressable
                  style={[
                    TableScreenCss.buttonHeader,
                    {
                      backgroundColor: maincolor,
                    },
                  ]}
                  onPress={() => setModalDeleteTables(false)}>
                  <Text whiteColor fontWeightBold textCenter fontSize18>
                    {cancel}
                  </Text>
                </Pressable>
              </View>
            </View>
          </View>
          {isLoadingScreen ? (
            <View
              style={[
                TableScreenCss.loadingView,
                {
                  backgroundColor: modalbackground,
                },
              ]}>
              <ActivityIndicator size="large" color={maincolor} />
            </View>
          ) : null}
        </Modal>
      </View> */}
    </Container>
  );
};
const styles = StyleSheet.create({
  button: {
    width: 150,
    height: 100,
    backgroundColor: 'blue',
  },
  wrapper: {
    paddingTop: 100,
    width: '100%',
    height: '100%',
    justifyContent: 'center',
  },
  item: {
    width: 100,
    height: 100,
    borderRadius: 8,
    backgroundColor: 'red',
    justifyContent: 'center',
    alignItems: 'center',
  },
  item_text: {
    fontSize: 40,
    color: '#FFFFFF',
  },
});

export default React.memo(TableScreen);
