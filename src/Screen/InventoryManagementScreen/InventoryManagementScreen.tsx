import {
  FlatList,
  Image,
  ActivityIndicator,
  SafeAreaView,
  TouchableOpacity,
  TextInput,
  TouchableHighlight,
} from 'react-native';
import React, {useCallback, useEffect} from 'react';
import {useAppSelector} from '../../hooks';
import {Text, View} from '../../Component/index';
import Entypo from 'react-native-vector-icons/Entypo';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useDispatch} from 'react-redux';

import {scale, setWidth} from '../../utils/style/Reponsive';
import {Platform} from 'react-native';
import {InventoryManagementScreenCss} from './InventoryManagementScreenCss';
import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';

import {ThunkDispatch} from '@reduxjs/toolkit';
import {getInventoryList, getInventoryListAll} from '../../Redux/GetData';
import {languages} from '../../constants';
import { heightPercentageToDP as hp , widthPercentageToDP as wp} from 'react-native-responsive-screen'

const InventoryManagementScreen = (props: any) => {
  const {navigation} = props;

  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const [loadingPage, setLoadingPage] = React.useState(false);
  const [pageCurrent, setPageCurrent] = React.useState(1);
  const [searchText, setSearchText] = React.useState('');
  const [loadingData, setLoadingData] = React.useState(false);
  let filteredData = [];
  const listInventory = useAppSelector(state => state.getData.listInventory);
  filteredData = listInventory.filter(
    (item: {product: {product_code: string}}) =>
      item.product?.product_code?.includes(searchText),
  );
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const {access_token} = useAppSelector(state => state.counter);
  const {maincolor, blackcolor, modalbackground} = xmlData;
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;

  const checkLangue = (): any | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: any = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    ProductCode,
    ProductNameInventory,
    QuantityInventory,
    CreateDeliveryImport,
    WarehouseExportTitle,
    WarehouseFindProduct,
  }: any = langData;
  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  useEffect(() => {
    setLoadingPage(true);
    getData();
    return () => {
      setLoadingPage(false);
    };
  }, []);
  const handleGoBack = () => {
    navigation.pop(1);
  };
  const getData = async () => {
    dispatch(
      getInventoryList({
        access_token,
        pageCurrent,
        messageApp: '',
      }),
    );
  };
  useEffect(() => {
    setLoadingPage(true);
    getData();
    return () => {
      setLoadingPage(false);
    };
  }, [pageCurrent]);

  const moveToInventoryDetailScreen = useCallback(
    (item: any) => {
      navigation.navigate('InventoryDetailScreen', item);
    },
    [navigation],
  );

  const renderItem = ({
    item,
  }: {
    item: {
      product: {
        image: string;
        product_code: string;
        title: string;
      };
      quantity: number;
    };
  }) => {
    return (
      <TouchableHighlight
        underlayColor="transparent"
        activeOpacity={0.9}
        onPress={() => moveToInventoryDetailScreen(item)}>
        <View style={InventoryManagementScreenCss.card} flDirectionRow>
          <View w_35 aItemsCenter jContentCenter>
            <Image
              source={{
                uri: item.product.image,
              }}
              style={{
                height: setWidth(28),
                width: setWidth(28),
                borderRadius: 20,
                borderWidth: 3,
                borderColor: maincolor,
              }}
            />
          </View>
          <View w_55 jContentCenter>
            <View>
              <View>
                <Text
                  style={{
                    fontSize: setWidth(4.5),
                    fontWeight: Platform.OS === 'ios' ? 'bold' : '800',
                    color: maincolor,
                    marginVertical: 2,
                  }}>
                  {ProductCode} :
                </Text>
              </View>

              <View>
                <Text
                  style={{
                    fontSize: setWidth(4.5),
                    fontWeight: Platform.OS === 'ios' ? 'bold' : '800',
                    color: maincolor,
                    marginVertical: 2,
                  }}>
                  {item.product.product_code.length < 16
                    ? `${item.product.product_code}`
                    : `${item.product.product_code.substring(0, 16)}...`}
                </Text>
              </View>
              <Text
                style={{
                  fontSize: setWidth(4.5),
                  fontWeight: Platform.OS === 'ios' ? 'bold' : '800',
                  color: maincolor,
                  marginVertical: 2,
                }}>
                {ProductNameInventory} :
                {item.product.title.length < 16
                  ? `${item.product.title}`
                  : `${item.product.title.substring(0, 16)}...`}
              </Text>
              <Text
                style={{
                  fontSize: setWidth(4.5),
                  fontWeight: 'bold',
                  marginVertical: 2,
                  color: 'black',
                }}>
                {QuantityInventory} : {item.quantity}
              </Text>
            </View>
          </View>
          <View style={{
            width: wp('5%'),
          }}></View>
        </View>
      </TouchableHighlight>
    );
  };

  useEffect(() => {
    if (listInventory.length == 0) {
      setLoadingPage(false);
    }
  }, [listInventory.length]);

  const renderFooter = () => {
    return (
      <View style={InventoryManagementScreenCss.loader}>
        {loadingPage ? <ActivityIndicator size="large" /> : null}
      </View>
    );
  };

  const handleLoadMore = () => {
    setPageCurrent(pageCurrent + 1);
    setLoadingPage(true);
  };
  const handleInputWarehouse = () => {
    navigation.navigate('ManagementWarehouseScreen');
  };
  const handleOutputWarehouse = () => {
    navigation.navigate('ExportWarehouseScreen');
  };
  const refreshScreen = async () => {
    dispatch(
      getInventoryListAll({
        access_token,
        messageApp: '',
      }),
    );
    setLoadingData(true);
    getData();
    filteredData = listInventory.filter(
      (item: {product: {product_code: string}}) =>
        item.product.product_code.includes(searchText),
    );
    const timeId = setTimeout(() => {
      setLoadingData(false);
    }, 1000);
    return () => clearTimeout(timeId);
  };
  return (
    <SafeAreaView style={InventoryManagementScreenCss.container}>
      <View flex1 mHorizontal10
      style={{
        width: '96%',
      }}
      >
        <View flDirectionRow jContentCenter aItemsCenter>
          <View w_35 jContentCenter>
            <TouchableOpacity
              onPress={handleInputWarehouse}
              style={[
                {
                  backgroundColor: maincolor,
                  marginVertical: scale(10),
                  height: scale(48),
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: scale(10),
                  width: '95%',
                },
              ]}>
              <Text fontSize16 textCenter fontWeight700 whiteColor>
                {CreateDeliveryImport}
              </Text>
            </TouchableOpacity>
          </View>
          <View w_35 jContentCenter>
            <TouchableOpacity
              style={[
                {
                  backgroundColor: maincolor,
                  marginVertical: scale(10),
                  height: scale(48),
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: scale(10),
                  width: '95%',
                },
              ]}
              onPress={handleOutputWarehouse}>
              <Text fontSize16 textCenter fontWeight700 whiteColor>
                {WarehouseExportTitle}
              </Text>
            </TouchableOpacity>
          </View>
          <View w_15>
            <SimpleLineIcons
              name="refresh"
              size={scale(40)}
              color={maincolor}
              onPress={refreshScreen}
            />
          </View>
          <View w_10>
            <TouchableOpacity
              onPress={handleGoBack}
              style={InventoryManagementScreenCss.btnBackScreen}>
              <Entypo name="log-out" size={40} color={maincolor} />
            </TouchableOpacity>
          </View>
        </View>
        <View aItemsCenter mHorizontal10>
          <View
            flDirectionRow
            borderBlackColor
            borderWidth1
            mTop8
            bRadius5
            aItemsCenter
            style={{
              width: wp('96%'),
            }}>
            <Ionicons
              name="search"
              size={34}
              style={{
                paddingHorizontal: scale(16),
              }}
              color={blackcolor}
            />
            <TextInput
              placeholder={WarehouseFindProduct}
              onChangeText={setSearchText}
              placeholderTextColor={blackcolor}
              style={{
                color: blackcolor,
              }}
            />
          </View>
        </View>
        <FlatList
          style={{flex: 1}}
          data={filteredData}
          renderItem={renderItem}
          keyExtractor={(item, index) => index.toString()}
          ListFooterComponent={renderFooter}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
        />
      </View>
      {loadingData ? (
        <View
          style={[
            {
              position: 'absolute',
              justifyContent: 'center',
              alignContent: 'center',
              top: -scale(20),
              bottom: 0,
              right: -scale(20),
              left: -scale(20),
              backgroundColor: modalbackground,
            },
          ]}>
          <ActivityIndicator size="large" color={maincolor} />
        </View>
      ) : null}
    </SafeAreaView>
  );
};

export default InventoryManagementScreen;
