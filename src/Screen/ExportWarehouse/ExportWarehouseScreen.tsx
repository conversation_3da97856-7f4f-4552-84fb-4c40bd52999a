import {
  Alert,
  Keyboard,
  SafeAreaView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import {
  Text,
  View,
  DropDownComponents,
  ModalCreateAcency,
} from '../../Component/index';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import React, {useEffect, useState} from 'react';
import {ExportWarehouseScreenCss} from './ExportWarehouseScreenCss';
import {useAppSelector} from '../../hooks';
import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';
import {scale} from '../../utils/style/Reponsive';
import Entypo from 'react-native-vector-icons/Entypo';
import {ThunkDispatch} from '@reduxjs/toolkit';
import {useDispatch} from 'react-redux';
import {ActivityIndicator} from 'react-native';
import {languages} from '../../constants';
import {
  fetchListProduct,
  getInventoryListAll,
  getListAgencies,
  handleResetProductName,
} from '../../Redux/GetData';
import {exportWarehouseReceipt} from '../../api/handleApi';
import {setStateShowOption} from '../../Redux/Slide';

interface IAddProducts {
  [key: string]: string | undefined;
  componentAddProductChooseCategory?: string;
  componentAddProductCategories?: string;
  componentAddProductName?: string;
  componentAddProductPrice?: string;
  componentAddProductVat?: string;
  componentAddProductImage?: string;
  componentAddProductCreate?: string;
}

const ExportWarehouseScreen = (props: any) => {
  const {navigation} = props;

  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {maincolor, graycolor, modalbackground, blackcolor} = xmlData;
  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const listProduct = useAppSelector(state => state.getData.listProduct);
  const access_token = useAppSelector(state => state.counter.access_token);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const listAgencies = useAppSelector(state => state.getData.listAgencies);
  const nameOpition = useAppSelector(state => state.counter.nameSelectOpition);
  const currencySymbol = useAppSelector(state => state.counter?.infoAdmin?.admin?.store?.currency_symbol) || "đ";
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;

  const checkLangue = (): IAddProducts | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: IAddProducts = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    messageApp,
    deliveryNote,
    handleProductScreenName,
    staffScreenAddressStaff,
    ExportWarehouseQuantityFood,
    ProductNotCreated,
    ProductNotSelected,
    Cash,
    Transfer,
    Bank,
    Export,
    Return,
    CancelOutput,
    WarningDiscount,
    WarehouseSuccess,
    WarehouseReturnSuccess,
    WarehouseCancelSuccess,
    WarehouseExport,
    WarehouseExportError,
    RepresentativeName,
    AgencyPhoneNumber,
    AgencyName,
    CodeFormInput,
    CodeFormExport,
    PriceExport,
    PriceExportCancel,
    DiscountPrice,
    TotalPrice,
    PaymentMethod,
    NoteExport,
    CreateDeliveryExport,
    CreateDeliveryBack,
    CreateDeliveryCancel,
    CancelDelivery,
    TotalPriceProduct,
    AgencyHaveNotSelected,
  }: any = langData;

  const [state, setState] = useState<any>({
    loadingData: false,
    productName: '',
    billCode: '',
    price: 0,
    quantity: 0,
    discount: 0,
    paymentMethod: '',
    paymentId: 0,
    note: '',
    listProduct: [],
    listAgencies: [],
    checkInput: true,
    totalPaymentPrice: 0,
    agencyId: 0,
    productId: 0,
    totalPrice: 0,
    representName: '',
    address: '',
    phone: '',
    outputType: 1,
    outputTypesValue: 'export',
    agencyName: '',
    firstPrice: 0,
    priceCancel: 0,
  });

  if (state.price && state.quantity) {
    state.totalPaymentPrice =
      parseInt(state.price) * parseInt(state.quantity) -
      parseInt(state.discount);
  }

  if (state.firstPrice && state.quantity) {
    state.priceCancel = parseInt(state.firstPrice) * parseInt(state.quantity);
  }

  if (state.price && state.quantity) {
    state.totalPrice = parseInt(state.price) * parseInt(state.quantity);
  }

  const radioButtons: any = [
    {
      id: 1,
      label: Cash,
      value: 'cash',
    },
    {
      id: 2,
      label: Transfer,
      value: 'transfer',
    },
    {
      id: 3,
      label: Bank,
      value: 'bank',
    },
  ];

  const outputType: any = [
    {
      id: 1,
      label: Export,
      value: 'export',
    },
    {
      id: 2,
      label: Return,
      value: 'return',
    },
    {
      id: 3,
      label: CancelOutput,
      value: 'cancel',
    },
  ];

  const handleSelectProductName = (item: {
    value: string;
    key: number;
    firstPrice: number;
  }) => {
    setState({
      ...state,
      productName: item.value,
      productId: item.key,
      firstPrice: item.firstPrice,
    });
  };

  const handleSelectPayment = (item: {value: string; id: number}) => {
    setState({
      ...state,
      paymentMethod: item.value,
      paymentId: item.id,
    });
  };

  const handleSelectType = (item: {value: string; id: number}) => {
    if (state.outputType == 1) {
      if (
        state.productName &&
        state.billCode &&
        state.representName &&
        state.address &&
        state.phone &&
        state.price &&
        state.quantity &&
        state.paymentMethod
      ) {

        setState({
          ...state,
          outputType: item.id,
          outputTypesValue: item.value,
          checkInput: false,
        });
      } else {
        setState({
          ...state,
          outputType: item.id,
          outputTypesValue: item.value,
          checkInput: true,
        });
      }
    } else if (state.outputType == 2) {
      if (
        state.productName &&
        state.agencyName &&
        state.billCode &&
        state.quantity &&
        state.price &&
        state.discount &&
        state.paymentMethod
      ) {
        setState({
          ...state,
          outputType: item.id,
          outputTypesValue: item.value,
          checkInput: false,
        });
      } else {
        setState({
          ...state,
          outputType: item.id,
          outputTypesValue: item.value,
          checkInput: true,
        });
      }
    } else if (state.outputType == 3) {
      if (state.productName && state.quantity && state.firstPrice) {
        setState({
          ...state,
          outputType: item.id,
          outputTypesValue: item.value,
          checkInput: false,
        });
      } else {
        setState({
          ...state,
          outputType: item.id,
          outputTypesValue: item.value,
          checkInput: true,
        });
      }
    }
  };

  const handleGoBack = () => {
    navigation.pop(1);
  };

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  const handleCancer = () => {
    setState({
      ...state,
      loadingData: false,
      productName: '',
      billCode: '',
      price: 0,
      quantity: 0,
      discount: 0,
      paymentMethod: '',
      note: '',
      checkInput: true,
      totalPaymentPrice: 0,
      agencyId: 0,
      productId: 0,
      totalPrice: 0,
      representName: '',
      address: '',
      phone: '',
      agencyName: '',
      paymentId: 0,
      outputType: 1,
      outputTypesValue: 'export',
      firstPrice: 0,
      priceCancel: 0,
    });
    dispatch(handleResetProductName());
  };

  useEffect(() => {
    dispatch(fetchListProduct({access_token, messageApp}));
    dispatch(getListAgencies({access_token, messageApp}));
  }, []);

  useEffect(() => {
    if (state.totalPaymentPrice < 0) {
      Alert.alert(messageApp, WarningDiscount);
    }
  }, [state.totalPaymentPrice]);
  useEffect(() => {
    setState((prevState: any) => ({
      ...prevState,
      listProduct:
        listProduct == undefined
          ? []
          : listProduct.map(
              (item: {
                id: string;
                title: string;
                firstPrice: any;
                price: number;
              }) => {
                if (item.firstPrice) {
                  return {
                    key: item.id,
                    value: item.title,
                    firstPrice: item.firstPrice.price,
                  };
                } else {
                  return {
                    key: item.id,
                    value: item.title,
                    firstPrice: item.price,
                  };
                }
              },
            ),
      listAgencies: listAgencies,
    }));
  }, [listProduct, listAgencies]);

  useEffect(() => {
    if (state.outputType == 1) {
      if (
        state.productName &&
        state.billCode &&
        state.price &&
        state.quantity &&
        state.paymentMethod &&
        state.representName &&
        state.address &&
        state.phone &&
        state.outputType !== 0
      ) {
        setState({
          ...state,
          checkInput: false,
        });
      } else {
        setState({
          ...state,
          checkInput: true,
        });
      }
    } else if (state.outputType == 2) {
      if (
        state.productName &&
        state.billCode &&
        state.price &&
        state.quantity &&
        state.paymentMethod &&
        state.agencyName &&
        state.outputType
      ) {
        setState({
          ...state,
          checkInput: false,
        });
      } else {
        setState({
          ...state,
          checkInput: true,
        });
      }
    } else if (state.outputType == 3) {
      if (state.productName && state.quantity) {
        setState({
          ...state,
          checkInput: false,
        });
      } else {
        setState({
          ...state,
          checkInput: true,
        });
      }
    }
  }, [
    state.productName,
    state.billCode,
    state.price,
    state.quantity,
    state.paymentMethod,
    state.representName,
    state.address,
    state.phone,
    state.agencyName,
    state.outputType,
  ]);

  const exportWarehouse = () => {
    exportWarehouseReceipt(access_token, state).then(res => {
      setState({
        ...state,
        loadingData: true,
      });
      if (res.data.status_code === 200) {
        handleCancer();
        dispatch(
          getInventoryListAll({
            access_token,
            messageApp: '',
          }),
        );
        if (state.outputType == 1) {
          Alert.alert(messageApp, WarehouseSuccess);
        } else if (state.outputType == 2) {
          Alert.alert(messageApp, WarehouseReturnSuccess);
        } else if (state.outputType == 3) {
          Alert.alert(messageApp, WarehouseCancelSuccess);
        }
      } else if (res.data.status_code === 404) {
        setState({
          ...state,
          loadingData: false,
          paymentId: 0,
          outputType: 1,
          outputTypesValue: 'export',
        });
        Alert.alert(messageApp, WarehouseExport);
      } else {
        setState({
          ...state,
          loadingData: false,
          paymentId: 0,
          outputType: 1,
          outputTypesValue: 'export',
        });
        Alert.alert(messageApp, WarehouseExportError);
      }
    });
  };

  const showModal: () => void = () => {
    dispatch(setStateShowOption({valid: false, nameOpiton: 'CreateAgency'}));
  };

  const handleSelectAgencyName = (item: {value: string; key: number}) => {
    setState({
      ...state,
      agencyName: item.value,
      agencyId: item.key,
    });
  };

  return (
    <SafeAreaView style={[ExportWarehouseScreenCss.container]}>
      <KeyboardAwareScrollView
        style={[
          ExportWarehouseScreenCss.container,
          {
            marginHorizontal: scale(4),
          },
        ]}
        contentContainerStyle={{flexGrow: 1}}
        keyboardShouldPersistTaps="handled">
        <TouchableWithoutFeedback onPress={dismissKeyboard}>
          <View flex1>
            <View w_100 flDirectionRow>
              <View w_70 jContentCenter aItemsCenter>
                <View
                  style={[
                    {
                      height: scale(48),
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRadius: scale(10),
                      width: '100%',
                    },
                  ]}>
                  <Text fontSize28 textCenter fontWeight700 mainColor>
                    {deliveryNote}
                  </Text>
                </View>
              </View>

              <View w_30 jContentCenter aItemsCenter>
                <TouchableOpacity
                  style={ExportWarehouseScreenCss.btnBackScreen}
                  onPress={handleGoBack}>
                  <Entypo name="log-out" size={40} color={maincolor} />
                </TouchableOpacity>
              </View>
            </View>

            <View w_100 flDirectionCol>
              <View w_100 flDirectionRow mVertical4>
                <View w_100 flDirectionRow>
                  {outputType.map((item: any) => (
                    <TouchableOpacity
                      key={item.id}
                      style={[styles.radioButton]}
                      onPress={() => handleSelectType(item)}>
                      <View
                        style={[
                          styles.radioButtonOuter,
                          state.outputType === item.id
                            ? styles.outerSelected
                            : null,
                        ]}>
                        {state.outputType === item.id && (
                          <View style={styles.radioButtonInner} />
                        )}
                      </View>
                      <Text>{item.label}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
              <View w_100>
                <View w_100>
                  <Text blackColor fontSize16 fontWeight800>
                    {handleProductScreenName}
                  </Text>
                </View>
                <View w_100 flDirectionRow>
                  <View w_100>
                    <DropDownComponents
                      value={
                        state.listProduct.length === 0
                          ? ProductNotCreated
                          : state?.productName == ''
                          ? ProductNotSelected
                          : state?.productName
                      }
                      data={state.listProduct}
                      onSelect={handleSelectProductName}
                      widthComponent="100%"
                    />
                  </View>
                </View>
              </View>
              {state.outputType == 1 ? (
                <>
                  <View w_100 flDirectionRow mVertical4>
                    <View w_35>
                      <Text blackColor fontSize16 fontWeight800>
                        {RepresentativeName}
                      </Text>
                    </View>
                    <View
                      flDirectionRow
                      w_65
                      borderWidth1
                      bRadius5
                      aItemsCenter
                      h_40
                      borderMainColor>
                      <TextInput
                        onChangeText={(e: string) => {
                          setState({
                            ...state,
                            representName: e,
                          });
                        }}
                        style={[
                          ExportWarehouseScreenCss.inputText,
                          {
                            color: blackcolor,
                          },
                        ]}
                        value={
                          state.representName
                            ? state.representName.toString()
                            : undefined
                        }
                      />
                    </View>
                  </View>
                  <View w_100 flDirectionRow mVertical4>
                    <View w_35>
                      <Text blackColor fontSize16 fontWeight800>
                        {staffScreenAddressStaff}
                      </Text>
                    </View>
                    <View
                      flDirectionRow
                      w_65
                      borderWidth1
                      bRadius5
                      aItemsCenter
                      h_40
                      borderMainColor>
                      <TextInput
                        onChangeText={(e: string) => {
                          setState({
                            ...state,
                            address: e,
                          });
                        }}
                        style={[
                          ExportWarehouseScreenCss.inputText,
                          {
                            color: blackcolor,
                          },
                        ]}
                        value={
                          state.address ? state.address.toString() : undefined
                        }
                      />
                    </View>
                  </View>
                  <View w_100 flDirectionRow mVertical4>
                    <View w_35>
                      <Text blackColor fontSize16 fontWeight800>
                        {AgencyPhoneNumber}
                      </Text>
                    </View>
                    <View
                      flDirectionRow
                      w_65
                      borderWidth1
                      bRadius5
                      aItemsCenter
                      h_40
                      borderMainColor>
                      <TextInput
                        onChangeText={(e: string) => {
                          setState({
                            ...state,
                            phone: e,
                          });
                        }}
                        style={[
                          ExportWarehouseScreenCss.inputText,
                          {
                            color: blackcolor,
                          },
                        ]}
                        keyboardType={'numeric'}
                        value={state.phone ? state.phone.toString() : undefined}
                      />
                    </View>
                  </View>
                </>
              ) : state.outputType == 2 ? (
                <>
                  <View w_100>
                    <View w_100>
                      <Text blackColor fontSize16 fontWeight800>
                        {AgencyName}
                      </Text>
                    </View>
                    <View w_100 flDirectionRow>
                      <View w_90>
                        <DropDownComponents
                          value={
                            state.agencyName == ''
                              ? AgencyHaveNotSelected
                              : state.agencyName
                          }
                          data={state.listAgencies}
                          onSelect={handleSelectAgencyName}
                          widthComponent="100%"
                        />
                      </View>
                      <View w_10 jContentCenter aItemsCenter>
                        <TouchableOpacity onPress={showModal}>
                          <SimpleLineIcons
                            name="plus"
                            size={30}
                            color={maincolor}
                          />
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                </>
              ) : state.outputType == 3 ? (
                <View>
                  <Text></Text>
                </View>
              ) : null}

              {state.outputType == 1 || state.outputType == 2 ? (
                <>
                  <View w_100 flDirectionRow mVertical4>
                    <View w_35>
                      <Text blackColor fontSize16 fontWeight800>
                        {CodeFormExport}
                      </Text>
                    </View>
                    <View
                      flDirectionRow
                      w_65
                      borderWidth1
                      bRadius5
                      aItemsCenter
                      h_40
                      borderMainColor>
                      <TextInput
                        onChangeText={(e: string) => {
                          setState({
                            ...state,
                            billCode: e,
                          });
                        }}
                        style={[
                          ExportWarehouseScreenCss.inputText,
                          {
                            color: blackcolor,
                          },
                        ]}
                        keyboardType={'numeric'}
                        value={
                          state.billCode ? state.billCode.toString() : undefined
                        }
                      />
                    </View>
                  </View>
                </>
              ) : (
                <></>
              )}

              <View w_100 flDirectionRow mVertical4>
                <View w_35>
                  <Text blackColor fontSize16 fontWeight800>
                    {ExportWarehouseQuantityFood}
                  </Text>
                </View>
                <View
                  flDirectionRow
                  w_65
                  borderWidth1
                  bRadius5
                  aItemsCenter
                  h_40
                  borderMainColor>
                  <TextInput
                    onChangeText={(e: string) => {
                      setState({
                        ...state,
                        quantity: e,
                      });
                    }}
                    style={[
                      ExportWarehouseScreenCss.inputText,
                      {
                        color: blackcolor,
                      },
                    ]}
                    keyboardType={'numeric'}
                    value={
                      state.quantity ? state.quantity.toString() : undefined
                    }
                  />
                </View>
              </View>

              {state.outputType == 1 || state.outputType == 2 ? (
                <>
                  <View w_100 flDirectionRow mVertical4>
                    <View w_35>
                      <Text blackColor fontSize16 fontWeight800>
                        {PriceExport}
                      </Text>
                    </View>
                    <View
                      flDirectionRow
                      w_65
                      borderWidth1
                      bRadius5
                      aItemsCenter
                      h_40
                      borderMainColor>
                      <TextInput
                        onChangeText={(e: string) => {
                          setState({
                            ...state,
                            price: e,
                          });
                        }}
                        style={[
                          ExportWarehouseScreenCss.inputText,
                          {
                            color: blackcolor,
                          },
                        ]}
                        keyboardType={'numeric'}
                        value={state.price ? state.price.toString() : undefined}
                      />
                    </View>
                  </View>
                </>
              ) : (
                <>
                  <View w_100 flDirectionRow mVertical4>
                    <View w_35>
                      <Text blackColor fontSize16 fontWeight800>
                        {PriceExportCancel}
                      </Text>
                    </View>
                    <View flDirectionRow w_65>
                      <View
                        flDirectionRow
                        aItemsCenter
                        borderMainColor
                        w_100
                        style={{
                          justifyContent: 'flex-end',
                          paddingRight: scale(10),
                        }}>
                        <Text fontSize20>
                          {state.firstPrice}
                          {currencySymbol}
                        </Text>
                      </View>
                    </View>
                  </View>
                </>
              )}
              {state.outputType == 1 || state.outputType == 2 ? (
                <>
                  <View w_100 flDirectionRow mVertical4>
                    <View w_35>
                      <Text blackColor fontSize16 fontWeight800>
                        {DiscountPrice}
                      </Text>
                    </View>
                    <View
                      flDirectionRow
                      w_65
                      borderWidth1
                      bRadius5
                      aItemsCenter
                      h_40
                      borderMainColor>
                      <TextInput
                        onChangeText={(e: string) => {
                           if (e === '' || parseInt(e) < 0) {
                                                  e = '0';
                                                }
                                                // Nếu giá trị nhập vào lớn hơn giá trị tổng thì gán giá trị là 0 và hiển thị cảnh báo
                                                if (parseInt(e) > state.totalPrice) {
                                                  e = '0';
                                                  Alert.alert(messageApp, WarningDiscount);
                                                }
                          setState({
                            ...state,
                            discount: e,
                          });
                        }}
                        style={[
                          ExportWarehouseScreenCss.inputText,
                          {
                            color: blackcolor,
                          },
                        ]}
                        keyboardType={'numeric'}
                        value={
                          state.discount ? state.discount.toString() : undefined
                        }
                      />
                    </View>
                  </View>
                  <View w_100 flDirectionRow mVertical4>
                    <View w_35>
                      <Text blackColor fontSize16 fontWeight800>
                        {TotalPrice}
                      </Text>
                    </View>
                    <View
                      flDirectionRow
                      w_65
                      aItemsCenter
                      borderMainColor
                      style={{
                        justifyContent: 'flex-end',
                        paddingRight: scale(10),
                      }}>
                      <Text fontSize20>
                        {state.totalPaymentPrice}
                        {currencySymbol}
                      </Text>
                    </View>
                  </View>
                  <View w_100 mVertical4>
                    <View>
                      <Text blackColor fontSize16 fontWeight800>
                        {PaymentMethod}
                      </Text>
                    </View>
                    <View w_100 flDirectionRow>
                      {radioButtons.map((item: any) => (
                        <TouchableOpacity
                          key={item.id}
                          style={[styles.radioButton]}
                          onPress={() => handleSelectPayment(item)}>
                          <View
                            style={[
                              styles.radioButtonOuter,
                              state.paymentId === item.id
                                ? styles.outerSelected
                                : null,
                            ]}>
                            {state.paymentId === item.id && (
                              <View style={styles.radioButtonInner} />
                            )}
                          </View>
                          <Text>{item.label}</Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </View>
                  <View
                    w_100
                    flDirectionRow
                    style={{height: scale(80)}}
                    mVertical4>
                    <View w_35>
                      <Text blackColor fontSize16 fontWeight800>
                        {NoteExport}
                      </Text>
                    </View>
                    <View
                      flDirectionRow
                      w_65
                      borderWidth1
                      bRadius5
                      aItemsCenter
                      borderMainColor>
                      <TextInput
                        onChangeText={(e: string) => {
                          setState({
                            ...state,
                            note: e,
                          });
                        }}
                        style={[
                          {
                            color: blackcolor,
                            borderRadius: scale(5),
                            paddingLeft: scale(10),
                            fontSize: scale(14),
                            width: '100%',
                          },
                        ]}
                        value={state.note ? state.note.toString() : undefined}
                        multiline
                      />
                    </View>
                  </View>
                </>
              ) : (
                <>
                  <View w_100 flDirectionRow mVertical4>
                    <View w_35>
                      <Text blackColor fontSize16 fontWeight800>
                        {TotalPriceProduct}
                      </Text>
                    </View>
                    <View
                      flDirectionRow
                      w_65
                      aItemsCenter
                      borderMainColor
                      style={{
                        justifyContent: 'flex-end',
                        paddingRight: scale(10),
                      }}>
                      <Text fontSize20>
                        {state.priceCancel}
                        {currencySymbol}
                      </Text>
                    </View>
                  </View>
                  <View
                    w_100
                    flDirectionRow
                    style={{height: scale(80)}}
                    mVertical4>
                    <View w_35>
                      <Text blackColor fontSize16 fontWeight800>
                        {NoteExport}
                      </Text>
                    </View>
                    <View
                      flDirectionRow
                      w_65
                      borderWidth1
                      bRadius5
                      aItemsCenter
                      borderMainColor>
                      <TextInput
                        onChangeText={(e: string) => {
                          setState({
                            ...state,
                            note: e,
                          });
                        }}
                        style={[
                          {
                            color: blackcolor,
                            borderRadius: scale(5),
                            paddingLeft: scale(10),
                            fontSize: scale(14),
                            width: '100%',
                          },
                        ]}
                        value={state.note ? state.note.toString() : undefined}
                        multiline
                      />
                    </View>
                  </View>
                </>
              )}
            </View>

            <View w_100 flDirectionRow jContentAround mVertical4>
              <View w_45>
                <TouchableOpacity
                  style={[
                    ExportWarehouseScreenCss.btnAddProduct,
                    {
                      backgroundColor: state.checkInput ? graycolor : maincolor,
                    },
                  ]}
                  disabled={state.checkInput}
                  onPress={exportWarehouse}>
                  <Text whiteColor fontWeight800 fontSize18>
                    {state.outputType == 1
                      ? CreateDeliveryExport
                      : state.outputType == 2
                      ? CreateDeliveryBack
                      : CreateDeliveryCancel}
                  </Text>
                </TouchableOpacity>
              </View>
              <View w_45>
                <TouchableOpacity
                  style={[
                    ExportWarehouseScreenCss.btnAddProduct,
                    {
                      backgroundColor: false ? graycolor : maincolor,
                    },
                  ]}
                  onPress={handleCancer}>
                  <Text whiteColor fontWeight800 fontSize18>
                    {CancelDelivery}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAwareScrollView>
      {state.loadingData ? (
        <View
          style={[
            {
              position: 'absolute',
              justifyContent: 'center',
              alignContent: 'center',
              backgroundColor: modalbackground,
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
            },
          ]}>
          <ActivityIndicator size="large" color={maincolor} />
        </View>
      ) : null}
      {nameOpition === 'CreateAgency' ? (
        <View loadingScreen>
          <ModalCreateAcency />
        </View>
      ) : null}
    </SafeAreaView>
  );
};

export default React.memo(ExportWarehouseScreen);
const styles = StyleSheet.create({
  radioButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: scale(2),
    borderColor: 'transparent',
    borderRadius: scale(12),
    marginHorizontal: scale(4),
  },

  radioButtonOuter: {
    height: scale(24),
    width: scale(24),
    borderRadius: scale(12),
    borderWidth: scale(2),
    borderColor: 'gray',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: scale(15),
  },
  outerSelected: {
    borderColor: 'black',
  },
  radioButtonInner: {
    height: scale(12),
    width: scale(12),
    borderRadius: scale(6),
    backgroundColor: 'black',
  },
});
