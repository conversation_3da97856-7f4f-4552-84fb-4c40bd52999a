import { StyleSheet } from 'react-native';
import { scale } from '../../utils/style/Reponsive';
import { heightPercentageToDP as hp , widthPercentageToDP as wp} from 'react-native-responsive-screen'

export const ChartScreenCss = StyleSheet.create({
  container: {
    flex: 1,
    marginHorizontal: wp('1%'),
  },
  btnChooseDay: {
    width: '33%',
    height: scale(30),
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 0.7,
    borderRadius: scale(10),
  },

  btnSeeRevenue: {
    width: '33%',
    height: scale(30),
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 0.7,
    borderRadius: scale(10),
  },

  loadingView: {
    position: 'absolute',
    justifyContent: 'center',
    alignContent: 'center',
    top: -scale(20),
    bottom: 0,
    right: -scale(20),
    left: -scale(20),
  },
});
