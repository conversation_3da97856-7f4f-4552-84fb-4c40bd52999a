import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  Alert,
  Button,
  Platform,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import {useDispatch} from 'react-redux';
import moment from 'moment';
import {Text, View} from '../../Component/index';

import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>} from 'react-native-chart-kit';
import DatePicker from 'react-native-date-picker';
import {ChartScreenCss} from './CharScreenCss';
import {
  setFlag<PERSON>hart,
  setFlagMonth,
  setFlagWeek,
  setShowRevenueDayByDay,
  setShowRevenueMonth,
  setShowRevenueWeeks,
  setStateShowOption,
  setTotalDiscount,
  setTotalSurcharge,
} from '../../Redux/Slide';
import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';
import {
  fetchRevenueLast2Day,
  fetchRevenueLast3Day,
  fetchRevenueLast4Day,
  fetchRevenueLast5Day,
  fetchRevenueLast6Day,
  fetchRevenueLastDay,
  fetchRevenueOpionalDate,
  fetchRevenueStartAndEndDay,
  fetchRevenueToDay,
  handleGetRevenueFifthWeek,
  handleGetRevenueFirstWeek,
  handleGetRevenueFourtWeek,
  handleGetRevenueLastMouth,
  handleGetRevenueOnMouth,
  handleGetRevenueOnWeek,
  handleGetRevenueSecondWeek,
  handleGetRevenueThirdWeek,
} from '../../Redux/GetData';
import {ThunkDispatch} from '@reduxjs/toolkit';
import {useAppSelector} from '../../hooks';
import {heightWindow, scale, widthWindow} from '../../utils/style/Reponsive';
import {getPaymentByDateToDate} from '../../api/handleApi';
import {languages} from '../../../src/constants';
interface IProps {
  navigation: {
    navigate: any;
  };
}
interface IChartScreen {
  [key: string]: string | undefined;
  custommersScreenTitle?: string;
  custommersScreenListCustommers?: string;
  customersScreenNewCustomer?: string;
  customersScreenOldCustomer?: string;
  customersScreenPaymentMax?: string;
  customersScreenPaymentMin?: string;
  customersScreenFindCustomer?: string;
  customersScreenSortList?: string;
}
interface IValue {
  extra_product_list?: any[];
  // Add other properties as needed
}
const ChartScreen = (props: IProps) => {
  const {navigation} = props;
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {
    backgroundcolor,
    blackcolor,
    maincolor,
    graycolor,
    messageApp,
    modalbackground,
  } = xmlData;
  const {access_token, nameSelectOpition, flagChart, flagWeek} = useAppSelector(
    state => state.counter,
  );
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): IChartScreen | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: IChartScreen = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    chartScreenmonday,
    chartScreentuesday,
    chartScreenwednesday,
    chartScreenthursday,
    chartScreenfriday,
    chartScreensaturday,
    chartScreensunday,
    chartScreenlegendWeek,
    chartScreenFirstWeek,
    chartScreenSecondWeek,
    chartScreenThirdWeek,
    chartScreenFourtWeek,
    chartScreenFifthWeek,
    chartScreenlegenMonth,
    chartScreenMessageReason,
    chartScreenRevenueDay,
    chartScreenRevenueWeek,
    chartScreenRevenueMonth,
    chartScreenDetail,
    homeScreenRevenueToday,
    chartScreenLastMonth,
    chartScreenDayRevenue,
    chartScreenSeeRevenue,
    chartScreenEndDay,
    chartScreenStartDay,
    chartScreenSeeDetailCustomizableDay,
    chartScreenRevenuemanagement,chartScreenOther
  }: any = langData;
  const {
    revenueToday,
    revenueOptionalDate,
    revenueStartAndEndDay,
    revenueLastday,
    revenueLast2day,
    revenueLast3day,
    revenueLast4day,
    revenueLast5day,
    revenueLast6day,
    revenueOnMouth,
    revenueLastMouth,
    revenueOnWeek,
    revenueFirstWeek,
    revenueSecondWeek,
    revenueThirdWeek,
    revenueFourthWeek,
    revenueFifthWeek,
  } = useAppSelector(state => state.getData);
  const currencySymbol = useAppSelector(state => state.counter?.infoAdmin?.admin?.store?.currency_symbol) || "đ";
  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const [open, setOpen] = useState<boolean>(false);
  const [openStartDay, setOpenStartDay] = useState<boolean>(false);
  const [openEndDay, setOpenEndDay] = useState<boolean>(false);
  const [showTotalValue, setShowTotalValue] = useState<boolean>(false);
  const [showSelectDay, setShowSelectDay] = useState<boolean>(false);
  const [revenueDayByDay, setRevenueDayByDay] = useState<number>(0);
  const [date, setDate] = useState(new Date());
  const [customDay, setCustomDay] = useState<string>('');
  const [startDay, getStartDay] = useState<string>('');
  const [endDay, getEndDay] = useState<string>('');
  const [isValidDay, setIsValidDay] = useState<boolean>(true);
  const [food, setFood] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [monday, setMonday] = useState<number | undefined>(0);
  const [tuesday, setTuesday] = useState<number | undefined>(0);
  const [wednesday, setWednesday] = useState<number | undefined>(0);
  const [thursday, setThursday] = useState<number | undefined>(0);
  const [friday, setFriday] = useState<number | undefined>(0);
  const [saturday, setSaturday] = useState<number | undefined>(0);
  const [sunday, setSunday] = useState<number | undefined>(0);
  const chooseDay = [startDay, endDay];
  food.forEach((item: any) => {
    item.totalPrice = item.quantity * item.price * (1 + (item.vat || 0) / 100);
    item.totalPrice = parseFloat(item.totalPrice.toFixed(2));
  });
  // Revenue for week
  const dataOnMonthWeek = {
    labels: [
      chartScreenmonday,
      chartScreentuesday,
      chartScreenwednesday,
      chartScreenthursday,
      chartScreenfriday,
      chartScreensaturday,
      chartScreensunday,
    ],
    datasets: [
      {
        data: [
          monday,
          tuesday,
          wednesday,
          thursday,
          friday,
          saturday,
          sunday,
        ].map(value => value || 0),
      },
    ],
    legend: [chartScreenlegendWeek],
  };
  //handle get total value on week
  const dateOnWeek = new Date();
  const current_day = dateOnWeek.getDay();
  const startOfMonth = moment().startOf('month').format('YYYY-MM-DD');
  const dateToDay = moment(Date.now()).format('YYYY-MM-DD');
  const dateLastDay = moment(Date.now()).add(-1, 'day').format('YYYY-MM-DD');
  const dateLast2Day = moment(Date.now()).add(-2, 'day').format('YYYY-MM-DD');
  const dateLast3Day = moment(Date.now()).add(-3, 'day').format('YYYY-MM-DD');
  const dateLast4Day = moment(Date.now()).add(-4, 'day').format('YYYY-MM-DD');
  const dateLast5Day = moment(Date.now()).add(-5, 'day').format('YYYY-MM-DD');
  const dateLast6Day = moment(Date.now()).add(-6, 'day').format('YYYY-MM-DD');

  //Show Chart View
  useEffect(() => {
    const getRevenue = async () => {
      switch (current_day) {
        case 0:
          await dispatch(
            fetchRevenueLast6Day({
              access_token,
              dateLast5Day: dateLast6Day,
              messageApp,
            }),
          );
          setMonday(revenueLast6day);
          await dispatch(
            fetchRevenueLast5Day({access_token, dateLast5Day, messageApp}),
          );
          setTuesday(revenueLast5day);
          await dispatch(
            fetchRevenueLast4Day({
              access_token,
              dateLast3Day: dateLast4Day,
              messageApp,
            }),
          );
          setWednesday(revenueLast4day);
          await dispatch(
            fetchRevenueLast3Day({access_token, dateLast3Day, messageApp}),
          );
          setThursday(revenueLast3day);
          await dispatch(
            fetchRevenueLast2Day({access_token, dateLast2Day, messageApp}),
          );
          setFriday(revenueLast2day);
          await dispatch(
            fetchRevenueLastDay({access_token, dateLastDay, messageApp}),
          );
          setSaturday(revenueLastday);
          await dispatch(
            fetchRevenueToDay({access_token, dateToDay, messageApp}),
          );
          setSunday(revenueToday);
          break;
        case 1:
          await dispatch(
            fetchRevenueToDay({access_token, dateToDay, messageApp}),
          );
          setMonday(revenueToday);
          break;
        case 2:
          await dispatch(
            fetchRevenueLastDay({access_token, dateLastDay, messageApp}),
          );
          setMonday(revenueLastday);
          await dispatch(
            fetchRevenueToDay({access_token, dateToDay, messageApp}),
          );
          setTuesday(revenueToday);
          break;
        case 3:
          await dispatch(
            fetchRevenueLast2Day({access_token, dateLast2Day, messageApp}),
          );
          setMonday(revenueLast2day);
          await dispatch(
            fetchRevenueLastDay({access_token, dateLastDay, messageApp}),
          );
          setTuesday(revenueLastday);
          await dispatch(
            fetchRevenueToDay({access_token, dateToDay, messageApp}),
          );
          setWednesday(revenueToday);
          break;
        case 4:
          await dispatch(
            fetchRevenueLast3Day({access_token, dateLast3Day, messageApp}),
          );
          setMonday(revenueLast3day);
          await dispatch(
            fetchRevenueLast2Day({access_token, dateLast2Day, messageApp}),
          );
          setTuesday(revenueLast2day);
          await dispatch(
            fetchRevenueLastDay({access_token, dateLastDay, messageApp}),
          );
          setWednesday(revenueLastday);
          await dispatch(
            fetchRevenueToDay({access_token, dateToDay, messageApp}),
          );
          setThursday(revenueToday);
          break;
        case 5:
          await dispatch(
            fetchRevenueLast4Day({
              access_token,
              dateLast3Day: dateLast4Day,
              messageApp,
            }),
          );
          setMonday(revenueLast4day);
          await dispatch(
            fetchRevenueLast3Day({access_token, dateLast3Day, messageApp}),
          );
          setTuesday(revenueLast3day);
          await dispatch(
            fetchRevenueLast2Day({access_token, dateLast2Day, messageApp}),
          );
          setWednesday(revenueLast2day);
          await dispatch(
            fetchRevenueLastDay({access_token, dateLastDay, messageApp}),
          );
          setThursday(revenueLastday);
          await dispatch(
            fetchRevenueToDay({access_token, dateToDay, messageApp}),
          );
          setFriday(revenueToday);
          break;
        case 6:
          await dispatch(
            fetchRevenueLast5Day({access_token, dateLast5Day, messageApp}),
          );
          setMonday(revenueLast5day);
          await dispatch(
            fetchRevenueLast4Day({
              access_token,
              dateLast3Day: dateLast4Day,
              messageApp,
            }),
          );
          setTuesday(revenueLast4day);
          await dispatch(
            fetchRevenueLast3Day({access_token, dateLast3Day, messageApp}),
          );
          setWednesday(revenueLast3day);
          await dispatch(
            fetchRevenueLast2Day({access_token, dateLast2Day, messageApp}),
          );
          setThursday(revenueLast2day);
          await dispatch(
            fetchRevenueLastDay({access_token, dateLastDay, messageApp}),
          );
          setFriday(revenueLastday);
          await dispatch(
            fetchRevenueToDay({access_token, dateToDay, messageApp}),
          );
          setSaturday(revenueToday);
          break;
        default:
          break;
      }
    };
    getRevenue();
  }, [
    current_day,
    access_token,
    dateLast6Day,
    dateLast5Day,
    dateLast4Day,
    dateLast3Day,
    dateLast2Day,
    dateLastDay,
    dateToDay,
    messageApp,
    flagWeek,
  ]);
  const showModalWeeks = () => {
    setShowTotalValue(false);
    getStartDay('');
    getEndDay('');
    setIsValidDay(true);
    setLoading(true);
    const timeId = setTimeout(() => {
      dispatch(setShowRevenueWeeks({valid: false, nameOpiton: 'RevenueWeeks'}));
      dispatch(setFlagWeek());
      setLoading(false);
    }, 2500);
    return () => clearTimeout(timeId);
  };
  //Show Select Calculator
  const showModalDayByDay = () => {
    dispatch(
      setShowRevenueDayByDay({valid: false, nameOpiton: 'RevenueDayByDay'}),
    );
    setShowSelectDay(false);
  };

  // handle revenue on today
  const handleSelectTotalDay = (date: Date) => {
    const dateTimeSelectDay = moment(date).format('YYYY-MM-DD');
    dispatch(
      fetchRevenueOpionalDate({
        access_token,
        date: dateTimeSelectDay,
        messageApp,
      }),
    );
  };

  //handleSelectDay value
  const handleSelectTotal = (date: Date) => {
    const dateCustomDay: string = moment(date).format('YYYY-MM-DD');
    setCustomDay(dateCustomDay);
  };

  // Select  any revenue day

  const handleShowSelectDay = () => {
    setOpen(!open);
  };

  const handleShowChooseStartDay = () => {
    setOpenStartDay(!open);
  };

  const handleShowChooseEndDay = () => {
    setOpenEndDay(!open);
  };

  // Revenue for month
  const dataOnMonthChair = {
    labels: [
      chartScreenFirstWeek,
      chartScreenSecondWeek,
      chartScreenThirdWeek,
      chartScreenFourtWeek,
      chartScreenFifthWeek,
    ],
    datasets: [
      {
        data: [
          revenueFirstWeek,
          revenueSecondWeek,
          revenueThirdWeek,
          revenueFourthWeek,
          revenueFifthWeek,
        ].map(value => value || 0),
      },
    ],
    legend: [chartScreenlegenMonth],
  };

  const handleShowValueDay = () => {
    setShowSelectDay(true);
  };

  //handle get End Day
  const handleGetEndDay = (date: Date) => {
    const dateTimeEndday = moment(date).format('YYYY-MM-DD');
    getEndDay(dateTimeEndday);
  };

  //handle get Start Day
  const handleGetStartDay = (date: Date) => {
    const dateTimeStartDay = moment(date).format('YYYY-MM-DD');
    getStartDay(dateTimeStartDay);
  };

  //getRevenue startDay and endday
  const getRevenue = async () => {
    const start = new Date(startDay);
    const end = new Date(endDay);
    if (start.getTime() <= end.getTime()) {
      getTotalValueStartAndEndDay();
      showModalDayByDay();
      setShowTotalValue(true);
      setIsValidDay(true);
      try {
        await dispatch(
          fetchRevenueStartAndEndDay({
            date_start: startDay,
            date_end: endDay,
            access_token,
            messageApp,
          }),
        );
      } catch (err: unknown) {
        console.log(err);
      }
    } else {
      setShowTotalValue(true);
      Alert.alert(messageApp, chartScreenMessageReason);
      dispatch(setShowRevenueDayByDay({valid: false, nameOpiton: ''}));
      setIsValidDay(true);
    }
  };

  //handle getTotal detail startDay and endday
  const getTotalValueStartAndEndDay = async () => {
    setIsValidDay(true);
    try {
      async function fetchAndProcessData() {
        const res = await getPaymentByDateToDate(
          startDay,
          endDay,
          access_token,
        );
        if (res.data.revenue.paid_amount > 0) {
          const listRevenue = res.data.revenue.arrayProduct;
          const inputArray = Object.values(listRevenue);
          const totalDicount = res.data.revenue.totalDicount;
          const totalSurcharge = res.data.revenue.totalSurcharge;
          const paid_amount = res.data.revenue.paid_amount;
          dispatch(setTotalDiscount(totalDicount));
          dispatch(setTotalSurcharge(totalSurcharge)) ;
          const listFoods = inputArray.map((item: any) => ({
            title: item.name,
            price: parseInt(item.price),
            quantity: parseInt(item.quantity),
            vat: parseInt(item.vat),
            total_price : parseInt(item.total_price),
          }));
          setFood(listFoods);
          // setRevenueDayByDay(listFoods.reduce((acc, item) => acc + item.total_price, 0));
          setRevenueDayByDay(res.data.revenue.paid_amount);
        }else{
          setRevenueDayByDay(0);
          setShowRevenueDayByDay({valid: false, nameOpiton: ''});
        }
      }

      fetchAndProcessData();
    } catch (err: unknown) {
      console.log(err);
    }
  };

  //fix bugs handle chooseDay
  useEffect(() => {
    if (startDay !== '' && endDay !== '') {
      setIsValidDay(false);
      // getStartDay('');
    }
  }, [startDay, endDay]);

  // Create color
  const generateLightColorRgb: () => string = () => {
    const [red, green, blue] = Array.from({length: 3}, () =>
      Math.floor(((1 + Math.random()) * 256) / 2),
    );
    return `rgb(${red}, ${green}, ${blue})`;
  };
  food.sort(
    (
      a: {
        totalPrice: number;
      },
      b: {
        totalPrice: number;
      },
    ) => {
      return b.totalPrice - a.totalPrice;
    },
  );
  let newArr = food.slice(0, 6);
  let otherTotalPrice = 0;
  for (let i = 6; i < food.length; i++) {
    otherTotalPrice += food[i].totalPrice;
  }
  if (food.length > 6) {
    newArr.push({
      title: chartScreenOther,
      totalPrice: otherTotalPrice,
      color: generateLightColorRgb(),
      legendFontColor: blackcolor,
      legendFontSize: parseInt('15', 10),
    });
  }
  for (let i = 0; i < newArr.length; i++) {
    const foodItem = newArr[i];
    if (!foodItem.population) {
      foodItem.population = foodItem.totalPrice;
    }
    foodItem.name = foodItem.title;
    foodItem.color = generateLightColorRgb();
    foodItem.legendFontColor = blackcolor;
    foodItem.legendFontSize = parseInt('15', 10);
  }

  const handleGetDetaiFoodStartAndEndday = () => {
    navigation.navigate('DetailFood', {food, startDay, endDay,revenueDayByDay});
  };

  //handle get all revenue month with all week
  const showModalMonth = async () => {
    setShowTotalValue(false);
    getStartDay('');
    getEndDay('');
    setIsValidDay(true);
    setLoading(true);

    const timeId = setTimeout(async () => {
      dispatch(
        setShowRevenueMonth({
          valid: false,
          nameOpiton: 'RevenueMonth',
        }),
      );

      const weekRanges = [
        {start: 0, end: 6},
        {start: 7, end: 13},
        {start: 14, end: 20},
        {start: 21, end: 27},
        {start: 28, end: moment(startOfMonth).daysInMonth() - 1},
      ];

      await Promise.all(
        weekRanges.map(async range => {
          const date_start = moment(startOfMonth)
            .add(range.start, 'day')
            .format('YYYY-MM-DD');
          const date_end = moment(startOfMonth)
            .add(range.end, 'day')
            .format('YYYY-MM-DD');

          switch (range.start) {
            case 0:
              await dispatch(
                handleGetRevenueFirstWeek({
                  date_start,
                  date_end,
                  access_token,
                  messageApp,
                }),
              );
              break;
            case 7:
              await dispatch(
                handleGetRevenueSecondWeek({
                  date_start,
                  date_end,
                  access_token,
                  messageApp,
                }),
              );
              break;
            case 14:
              await dispatch(
                handleGetRevenueThirdWeek({
                  date_start,
                  date_end,
                  access_token,
                  messageApp,
                }),
              );
              break;
            case 21:
              await dispatch(
                handleGetRevenueFourtWeek({
                  date_start,
                  date_end,
                  access_token,
                  messageApp,
                }),
              );
              break;
            case 28:
              await dispatch(
                handleGetRevenueFifthWeek({
                  date_start,
                  date_end,
                  access_token,
                  messageApp,
                }),
              );
              break;
            default:
              break;
          }
        }),
      );

      setShowSelectDay(false);
      dispatch(setFlagMonth());
      setLoading(false);
    }, 2500);

    return () => {
      clearTimeout(timeId);
    };

  };

  //get all revenue today and on month
  const getAllRevenue = () => {
    const dateTimeStartOnMonth = moment(dateToDay)
      .startOf('month')
      .format('YYYY-MM-DD');
    const dateTimeEndOnMonth = moment(Date.now()).format('YYYY-MM-DD');
    let currentDay = dateOnWeek.getDay();
    switch (currentDay) {
      case 0:
        dispatch(
          handleGetRevenueOnWeek({
            date_start: moment(Date.now()).add(-6, 'day').format('YYYY-MM-DD'),
            date_end: dateToDay,
            access_token,
            messageApp,
          }),
        );
        break;
      case 1:
        dispatch(
          handleGetRevenueOnWeek({
            date_start: dateToDay,
            date_end: dateToDay,
            access_token,
            messageApp,
          }),
        );
        break;
      case 2:
        dispatch(
          handleGetRevenueOnWeek({
            date_start: moment(Date.now()).add(-1, 'day').format('YYYY-MM-DD'),
            date_end: dateToDay,
            access_token,
            messageApp,
          }),
        );
        break;
      case 3:
        dispatch(
          handleGetRevenueOnWeek({
            date_start: moment(Date.now()).add(-2, 'day').format('YYYY-MM-DD'),
            date_end: dateToDay,
            access_token,
            messageApp,
          }),
        );
        break;
      case 4:
        dispatch(
          handleGetRevenueOnWeek({
            date_start: moment(Date.now()).add(-3, 'day').format('YYYY-MM-DD'),
            date_end: dateToDay,
            access_token,
            messageApp,
          }),
        );
        break;
      case 5:
        dispatch(
          handleGetRevenueOnWeek({
            date_start: moment(Date.now()).add(-4, 'day').format('YYYY-MM-DD'),
            date_end: dateToDay,
            access_token,
            messageApp,
          }),
        );
        break;
      case 6:
        dispatch(
          handleGetRevenueOnWeek({
            date_start: moment(Date.now()).add(-5, 'day').format('YYYY-MM-DD'),
            date_end: dateToDay,
            access_token,
            messageApp,
          }),
        );
        break;
      default:
        break;
    }
    dispatch(
      handleGetRevenueOnMouth({
        date_start: dateTimeStartOnMonth,
        date_end: dateTimeEndOnMonth,
        access_token,
        messageApp,
      }),
    );
  };

  useEffect(() => {
    getAllRevenue();
  }, [flagChart]);

  //get revenue last month
  useEffect(() => {
    const dateTimeStartLastMonth = moment(dateToDay)
      .add(-1, 'month')
      .startOf('month')
      .format('YYYY-MM-DD');
    const dateTimeEndLastMonth = moment(dateToDay)
      .endOf('month')
      .add(-1, 'month')
      .format('YYYY-MM-DD');
    const fetchData = async () => {
      await dispatch(
        handleGetRevenueLastMouth({
          date_start: dateTimeStartLastMonth,
          date_end: dateTimeEndLastMonth,
          access_token,
          messageApp,
        }),
      );
    };
    fetchData();
  }, [dateToDay, access_token]);

  //refresh screen
  const refreshScreen = () => {
    setLoading(true);
    const timeId = setTimeout(async () => {
      await Promise.all([
        dispatch(setFlagChart()),
        dispatch(fetchRevenueToDay({access_token, messageApp})),
      ]);
      setLoading(false);
    }, 2000);
    return () => {
      clearTimeout(timeId);
    };
  };

  return (
    <SafeAreaView style={ChartScreenCss.container}>
      <View  jContentAround flDirectionRow aItemsCenter mVertical6>
        <View mainBGColor jContentCenter bRadius5>
          <Button
            title={chartScreenRevenueDay}
            onPress={handleShowSelectDay}
            color={Platform.OS === 'android' ? maincolor : backgroundcolor}
          />
        </View>
        <View mainBGColor bRadius5 jContentCenter>
          <Button
            title={chartScreenRevenueWeek}
            onPress={showModalWeeks}
            color={Platform.OS === 'android' ? maincolor : backgroundcolor}
          />
        </View>
        <View mainBGColor bRadius5 jContentCenter>
          <Button
            title={chartScreenRevenueMonth}
            onPress={showModalMonth}
            color={Platform.OS === 'android' ? maincolor : backgroundcolor}
          />
        </View>
        <View>
          <SimpleLineIcons
            name="refresh"
            size={40}
            color={maincolor}
            onPress={refreshScreen}
          />
        </View>
      </View>

      <View>
        <Text fontSize28 textCenter fontWeight700 mainColor>
          {chartScreenRevenuemanagement}
        </Text>
      </View>
      <View w_100>
        {nameSelectOpition === 'RevenueWeeks' ? (
          <View>
            <LineChart
              data={dataOnMonthWeek}
              width={widthWindow * 0.95}
              height={heightWindow * 0.3}
              chartConfig={{
                backgroundColor: blackcolor,
                backgroundGradientTo: blackcolor,
                color: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
                decimalPlaces: 0,
                propsForDots: {
                  r: '4',
                  strokeWidth: '4',
                },
                propsForLabels: {
                  fontSize: scale(widthWindow * 0.016),
                  fontWeight: 'bold',
                  ...Platform.select({
                    android: {
                      fontSize: scale(8.5),
                    },
                    ios: {
                      fontSize: scale(widthWindow * 0.016),
                    },
                  }),
                },
              }}
              segments={4}
              withVerticalLines={false}
            />
          </View>
        ) : null}
        {nameSelectOpition === 'RevenueMonth' ? (
          <View>
            <BarChart
              data={dataOnMonthChair}
              width={widthWindow * 0.95}
              height={heightWindow * 0.3}
              chartConfig={{
                backgroundColor: blackcolor,
                backgroundGradientTo: blackcolor,
                color: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
                decimalPlaces: 0,
                propsForLabels: {
                  ...Platform.select({
                    android: {
                      fontSize: scale(8.5),
                    },
                    ios: {
                      fontSize: scale(widthWindow * 0.016),
                    },
                  }),
                  fontWeight: 'bold',
                },
                strokeWidth: 1,
              }}
              segments={4}
              yAxisLabel=""
              yAxisSuffix=""
            />
          </View>
        ) : null}
      </View>

    
      {
      <View>
      <View>
        <Text fontSize18 blackColor>
          {chartScreenSeeDetailCustomizableDay}
        </Text>
        {showTotalValue || chooseDay[0] ? (
          <Text fontSize18 blackColor>
            {chartScreenStartDay}{' '}
            <Text fontWeight600>
              {moment(chooseDay[0]).format('DD-MM-YYYY')}
            </Text>
          </Text>
        ) : null}
        {false || (showTotalValue || chooseDay[1]) ? (
          <Text fontSize18 blackColor>
            {chartScreenEndDay}{' '}
            <Text fontWeight600>
              {moment(chooseDay[1]).format('DD-MM-YYYY')}
            </Text>
          </Text>
        ) : null}
      </View>
      <View flDirectionRow jContentBetween>
        <View
          style={[
            ChartScreenCss.btnChooseDay,
            {
              borderColor: backgroundcolor,
              backgroundColor: '#19D200',
            },
          ]}>
          <TouchableOpacity onPress={handleShowChooseStartDay}>
            <Text whiteColor fontSize16>
              {chartScreenStartDay}
            </Text>
          </TouchableOpacity>
        </View>
        <View>
          <DatePicker
            modal
            open={openStartDay}
            date={date}
            mode="date"
            onConfirm={(date: Date) => {
              setOpenStartDay(false);
              setDate(date);
              handleGetStartDay(date);
              if (startDay && endDay !== undefined) {
                setIsValidDay(false);
              }
            }}
            onCancel={() => {
              setOpen(false);
              setOpenStartDay(false);
            }}
          />
        </View>
        <View
          style={[
            ChartScreenCss.btnChooseDay,
            {
              borderColor: backgroundcolor,
              backgroundColor: '#19D200',
            },
          ]}>
          <TouchableOpacity onPress={handleShowChooseEndDay}>
            <Text whiteColor fontSize16>
              {chartScreenEndDay}
            </Text>
          </TouchableOpacity>
        </View>
        <View>
          <DatePicker
            modal
            open={openEndDay}
            date={date}
            mode="date"
            onConfirm={(date: Date) => {
              setOpenEndDay(false);
              setDate(date);
              handleGetEndDay(date);
              if (endDay && startDay !== undefined) {
                setIsValidDay(false);
              }
            }}
            onCancel={() => {
              setOpen(false);
              setOpenEndDay(false);
            }}
          />
        </View>
        <View
          style={[
            isValidDay === false
              ? [
                  ChartScreenCss.btnChooseDay,
                  {
                    backgroundColor: '#19D200',
                  },
                ]
              : [
                  ChartScreenCss.btnSeeRevenue,
                  {
                    backgroundColor: graycolor,
                  },
                ],
            {
              borderColor: backgroundcolor,
            },
          ]}>
          <TouchableOpacity
            disabled={isValidDay}
            onPress={() => {
              getRevenue();
            }}>
            <Text fontSize16 whiteColor>
              {chartScreenSeeRevenue}{' '}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View> 
      }
      {nameSelectOpition === 'RevenueDayByDay' ? (
          <View w_100>
            <View>
              <Text textCenter mainColor fontSize18 fontWeight600>
                <Text fontWeight600>
                  {revenueDayByDay
                    ?.toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  {currencySymbol}
                </Text>
                {revenueDayByDay !== 0 ? (
                  <Text onPress={handleGetDetaiFoodStartAndEndday}>
                    {' '}
                    {chartScreenDetail} {'>>>'}{' '}
                  </Text>
                ) : (
                  ''
                )}
              </Text>
            </View>
            <View>
              {revenueDayByDay ? (
                <PieChart
                  data={newArr}
                  width={widthWindow * 0.95}
                  height={heightWindow * 0.24}
                  chartConfig={{
                    backgroundGradientFrom: '#1E2923',
                    backgroundGradientFromOpacity: 0,
                    backgroundGradientTo: '#08130D',
                    backgroundGradientToOpacity: 0.5,
                    color: (opacity = 1) => `rgba(26, 255, 146, ${opacity})`,
                    strokeWidth: 3,
                    barPercentage: 0.5,
                    useShadowColorFromDataset: false,
                  }}
                  accessor="population"
                  paddingLeft="0"
                  backgroundColor="transparent"
                />
              ) : (
                ''
              )}
            </View>
          </View>
        ) : null}
      <View w_100 mTop8>
        <Text fontSize18 blackColor>
          {homeScreenRevenueToday}
          <Text fontWeight600 mainColor>
            {' '}
            <Text>
              {revenueToday?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            </Text>
          </Text>{' '}
          {currencySymbol}
        </Text>
      </View>
      <View w_100 mTop8>
        <Text fontSize18 blackColor>
          {chartScreenlegendWeek}
          <Text fontWeight600 mainColor>
            {'  '}
            {revenueOnWeek?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          </Text>{' '}
          {currencySymbol}
        </Text>
      </View>
      <View w_100 mTop8>
        <Text fontSize18 blackColor>
          {chartScreenlegenMonth}
          <Text fontWeight600 mainColor>
            {' '}
            {revenueOnMouth?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          </Text>{' '}
          {currencySymbol}
        </Text>
      </View>
      <View w_100 mTop8>
        <Text fontSize18 blackColor>
          {chartScreenLastMonth}
          <Text fontWeight600 mainColor>
            {' '}
            {revenueLastMouth?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          </Text>{' '}
          {currencySymbol}
        </Text>
      </View>

      {showSelectDay ? (
        <View w_100 mTop8>
          <Text fontSize18 blackColor fontWeight800>
            {chartScreenDayRevenue} {customDay}
            <Text fontWeight600 mainColor>
              {' '}
              {revenueOptionalDate
                ?.toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            </Text>
            <Text blackColor fontWeight500>
              {' '}
              {currencySymbol}
            </Text>
          </Text>
        </View>
      ) : null}
      <DatePicker
        modal
        open={open}
        date={date}
        mode="date"
        onConfirm={(date: Date) => {
          setOpen(false);
          setDate(date);
          handleSelectTotal(date);
          handleSelectTotalDay(date);
          handleShowValueDay();
        }}
        onCancel={() => {
          setOpen(false);
        }}
      />
      {loading ? (
        <View
          style={[
            ChartScreenCss.loadingView,
            {
              backgroundColor: modalbackground,
            },
          ]}>
          <ActivityIndicator size="large" color={maincolor} />
        </View>
      ) : null}
    </SafeAreaView>
  );
};
export default React.memo(ChartScreen);
