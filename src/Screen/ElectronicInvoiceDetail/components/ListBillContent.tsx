import {StyleSheet, View} from 'react-native';
import TextComponent from '../../../Component/Text/TextComponent';
import {scale} from '../../..//utils/style/Reponsive';
import RowComponent from '../../..//Component/Row/RowComponent';

interface Props {
  name: string;
  quantity: string;
  price: string;
  vat: string;
  total_price: string;
  weight?:
    | 'bold'
    | '700'
    | 'normal'
    | '100'
    | '200'
    | '300'
    | '400'
    | '500'
    | '600'
    | '800'
    | '900';
    size?: number;
}
const ListBillContent = (props: Props) => {
  const {name, quantity, price, vat, total_price, weight,size} = props;
  console.log("total_price",total_price)
  return (
    <RowComponent styles={{flex: 1}} justify="space-around">
      <View
        style={{
          flex: 3,
          alignItems: 'flex-start',

        }}>
        <TextComponent
          text={name}
          color="black"
          size={scale(17)}
          weight={weight ?? '700'}
          
        />
      </View>
      <View style={{flex: 1,
          alignItems: 'center',

      }}>
        <TextComponent
          text={quantity}
          color="black"
          size={scale(17)}
          weight={weight ?? '700'}
        />
      </View>
      <View
        style={{
          flex: 2,
          alignItems: 'center',

        }}>
        <TextComponent
          text={price}
          color="black"
          size={ price?.length < 8 ? scale(17) : scale(15)}
          weight={weight ?? '700'}
        />
      </View>
      <View
        style={{
          flex: 1,
          alignItems: 'center',
        }}>
        <TextComponent
          text={vat}
          color="black"
          size={scale(17)}
          weight={weight ?? '700'}
        />
      </View>
      <View style={{flex: 3, alignItems: 'flex-end',
      }}>
        <TextComponent
          text={total_price}
          color="black"
          size={ total_price?.length < 12 ? scale(17) : scale(15)}
          weight={weight ?? '700'}
        />
      </View>
    </RowComponent>
  );
};

export default ListBillContent;

