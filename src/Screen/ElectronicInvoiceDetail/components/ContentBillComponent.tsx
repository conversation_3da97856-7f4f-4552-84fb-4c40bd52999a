import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import RowComponent from '../../../Component/Row/RowComponent';
import TextComponent from '../../../Component/Text/TextComponent';
import {scale} from '../../../utils/style/Reponsive';
import {StyleProp, ViewStyle} from 'react-native';
interface Props {
  title: string;
  content: string;
  weight?:
    | 'bold'
    | 'normal'
    | '100'
    | '200'
    | '300'
    | '400'
    | '500'
    | '600'
    | '700'
    | '800'
    | '900';
  size?: number;
  style?: StyleProp<ViewStyle>;
}
const ContentBillComponent = (props: Props) => {
  const {title, content, weight, size, style} = props;
  return (
    <RowComponent justify="space-evenly" styles={[{flex: 1}, style]}>
      <View
        style={{
          flex: 1,
          alignItems: 'flex-start',
        }}>
        <TextComponent
          text={title}
          color="black"
          weight={weight ? weight : '700'}
          size={size ?? scale(15)}
        />
      </View>
      <View
        style={{
          flex: 1,
          alignItems: 'flex-end',
        }}>
        <TextComponent
          text={content}
          color="black"
          size={scale(15)}
          weight="700"
        />
      </View>
    </RowComponent>
  );
};

export default ContentBillComponent;

const styles = StyleSheet.create({});
