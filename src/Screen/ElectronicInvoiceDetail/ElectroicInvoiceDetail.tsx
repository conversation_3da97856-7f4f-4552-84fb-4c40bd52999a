import {ScrollView, StyleSheet, Text, TextInput, View} from 'react-native';
import React, {useState} from 'react';
import {Platform} from 'react-native';
import Container from '../../Component/Container/Container';
import SectionComponent from '../../Component/Section/SectionComponent';
import RowComponent from '../../Component/Row/RowComponent';
import HeaderComponent from '../../Component/Header/HeaderComponent';
import TextComponent from '../../Component/Text/TextComponent';
import {globalStyles} from '../../styles/globalStyles';

import {
  extractDateTime,
  getToday,
  numberToWords,
  scale,
  calculateTotalPrice,
  formatOutput,
} from '../../utils/style/Reponsive';
import {useAppSelector} from '../../hooks';
import ContentBillComponent from './components/ContentBillComponent';
import TitleComponent from '../../Component/Title/TitleComponent';
import {FlatList} from 'react-native';
import ListBillContent from './components/ListBillContent';
import InputComponent from '../../Component/Input/InputComponent';
import {Alert} from 'react-native';
import axios from 'axios';
import {TouchableOpacity} from 'react-native';
import {BASE_URL} from '../../api/ApiManager';
import {ActivityIndicator} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {languages} from '../../constants';

interface IHandleProduct {
  [key: string]: string | undefined;
}
const ElectroicInvoiceDetail = (props: any) => {
  const access_token = useAppSelector(state => state.counter.access_token);
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {maincolor, backgroundcolor, modalbackground} = xmlData;
  const {
    id,
    payment_code,
    reason,
    updated_at,
    valuetotal,
    discount,
    payment_details,
  } = props.route.params.item;
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);

  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);

  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): IHandleProduct | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: IHandleProduct = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    electronicInvoiceIndividual,
    electronicInvoiceCompany,
    electronicInvoiceTaxCodeEmpty,
    electronicInvoiceSuccess,
    electronicInvoiceFail,
    electronicInvoiceTaxCodeValid,
    electronicInvoiceTaxCodeInvalid,
    electronicInvoiceSuccessRegister,
    electronicInvoiceEmailInvalid,
    electronicInvoiceError,
    electronicInvoiceDetail,
    electronicInvoiceNumber,
    electronicInvoiceCode,
    electronicInvoiceDate,
    electronicInvoiceTotal,
    electronicInvoiceTaxCode,
    electronicInvoiceEnterTaxCode,
    electronicInvoiceCustomerName,
    electronicInvoiceCompanyName,
    electronicInvoiceEmail,
    electronicInvoiceAddress,
    electronicInvoiceProductName,
    electronicInvoiceProductQuantity,
    electronicInvoiceProductPrice,
    electronicInvoiceProductVat,
    electronicInvoiceRegister,
    electronicInvoiceListProduct,
  }: any = langData;
  const [state, setState] = useState({
    taxCode: '',
    email: '',
    address: '',
    nameCompany: '',
    btnConfirm: false,
    customerType: electronicInvoiceIndividual,
    isLoading: false,
  });
  console.log('state', state);
  const handleCustomerTypeChange = (type: string) => {
    setState(prevState => ({...prevState, customerType: type}));
  };

  const getData = async () => {
    if (state.taxCode === '') {
      Alert.alert(electronicInvoiceTaxCodeEmpty);
      return;
    }
    try {
      const response = await axios.get(
        `https://api.vietqr.io/v2/business/${state.taxCode}`,
      );
      if (response.data.code === '00') {
        Alert.alert(electronicInvoiceSuccess, electronicInvoiceTaxCodeValid);
        setState({
          ...state,
          nameCompany: response.data.data.name,
          address: response.data.data.address,
        });
      } else {
        Alert.alert(electronicInvoiceFail, electronicInvoiceTaxCodeInvalid);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleBillElectronic = async () => {
    setState({...state, isLoading: true});
    try {
      const request = await axios({
        method: 'post',
        url: `${BASE_URL}/api/admin/electronic_bill/issue_sign_and_send_electronic_bill`,
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${access_token}`,
        },
        data: {
          ngaylap: getToday(),
          mauso: '1',
          kyhieu: 'C24MCB',
          dnmua_ten: state.nameCompany,
          dnmua_tennguoimua: state.nameCompany,
          dnmua_mst: state.taxCode,
          dnmua_sdt: null,
          dnmua_diachi: state.address,
          dnmua_email: state.email,
          thanhtoanphuongthuc_ten: 'Ti\u1ec1n M\u1eb7t',
          thanhtoan_phuongthuc: '1',
          thanhtoan_thoihan: null,
          tiente_ma: 'VND',
          ghichu: reason,
          payment_id: id,
          tongtien_chuavat: calculateTotalPrice(payment_details),
          tienthue: valuetotal - calculateTotalPrice(payment_details),
          tongtien_chietkhau: discount,
          tongtien_covat: valuetotal,
          tongtien_cochu: numberToWords(valuetotal),
          dschitiet: formatOutput(payment_details),
        },
      });
      if (request.data.status_code == 200) {
        Alert.alert(electronicInvoiceSuccess, electronicInvoiceSuccessRegister);
      } else if (request.data.status_code == 400) {
        Alert.alert(electronicInvoiceFail, electronicInvoiceEmailInvalid);
      } else {
        Alert.alert(electronicInvoiceFail, electronicInvoiceError);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setState({...state, isLoading: false});
    }
  };

  const commonConditionsMet = state.email !== '' && state.nameCompany !== '';

  if (state.customerType === electronicInvoiceIndividual) {
    state.btnConfirm = commonConditionsMet;
    if (commonConditionsMet) {
      state.taxCode = '';
      state.address = '';
    }
  } else if (state.customerType === electronicInvoiceCompany) {
    state.btnConfirm =
      commonConditionsMet && state.taxCode !== '' && state.address !== '';
  } else {
    return;
  }

  const renderItem = ({
    item,
  }: {
    item: {
      id: string;
      valuetotal?: string;
      payment_code?: string;
      updated_at?: string;
      price?: any;
      quantity?: any;
      total_price?: any;
      products: {title: string; vat: string};
    };
  }) => {
    return (
      <ListBillContent
        name={item.products.title}
        quantity={item.quantity}
        price={`${item?.price
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}đ`}
        vat={item.products.vat}
        total_price={`${item?.total_price
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}đ`}
        weight="500"
      />
    );
  };
  return (
    <KeyboardAwareScrollView
      style={{
        flex: 1,
      }}
      contentContainerStyle={{flexGrow: 1}}
      keyboardShouldPersistTaps="handled">
      <View
        style={{
          flex: 1,
        }}>
        <Container
          styles={{
            flex: 20,
          }}>
          <SectionComponent
            styles={{
              flex: Platform.OS === 'ios' ? 1 : 0.5,
            }}>
            <HeaderComponent
              text={electronicInvoiceDetail}
              color={maincolor}
              size={scale(26)}
              btnOption
              onCustomerTypeChange={handleCustomerTypeChange}
            />
          </SectionComponent>

          <SectionComponent
            styles={{
              flex: Platform.OS === 'ios' ? 3 : 3,
            }}>
            <ContentBillComponent
              title={electronicInvoiceNumber + ' :'}
              content={id}
            />
            <ContentBillComponent
              title={electronicInvoiceCode + ' :'}
              content={payment_code}
            />
            <ContentBillComponent
              title={electronicInvoiceDate + ' :'}
              content={extractDateTime(updated_at).date}
            />
            <ContentBillComponent
              title={electronicInvoiceTotal + ' :'}
              content={
                valuetotal
                  ? `${valuetotal
                      .toString()
                      .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}đ`
                  : `0đ`
              }
            />
            <ContentBillComponent
              title={electronicInvoiceTaxCode + ' :'}
              content={
                discount
                  ? `${discount
                      .toString()
                      .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}đ`
                  : `0đ`
              }
              weight="600"
              size={scale(17)}
            />
            <RowComponent
              styles={{
                flex: Platform.OS === 'ios' ? 1.5 : 1,
              }}>
              <InputComponent
                searchText={state.taxCode}
                placeholder={electronicInvoiceEnterTaxCode}
                onChange={searchText =>
                  setState({...state, taxCode: searchText})
                }
                clearText={() => setState({...state, taxCode: ''})}
                searchIcon={state.taxCode ? true : false}
                size={scale(22)}
                style={{
                  borderWidth: 2,
                  borderRadius: 4,
                }}
                onSearch={() => getData()}
                editable={
                  state.customerType == electronicInvoiceIndividual
                    ? false
                    : true
                }
                cleanButton={state.taxCode ? true : false}
              />
            </RowComponent>
            <RowComponent
              styles={{
                flex: 1.5,
                marginTop: scale(2),
              }}>
              <InputComponent
                searchText={state.nameCompany}
                placeholder={
                  state.customerType == electronicInvoiceIndividual
                    ? electronicInvoiceCustomerName
                    : electronicInvoiceCompanyName
                }
                onChange={searchText =>
                  setState({...state, nameCompany: searchText})
                }
                clearText={() => setState({...state, nameCompany: ''})}
                size={scale(22)}
                style={{
                  borderWidth: 2,
                  borderRadius: 4,
                }}
                cleanButton={state.nameCompany ? true : false}
              />
            </RowComponent>
            <RowComponent
              styles={{
                flex: 1.5,
                marginTop: scale(2),
              }}>
              <InputComponent
                searchText={state.email}
                placeholder={electronicInvoiceEmail}
                onChange={searchText => setState({...state, email: searchText})}
                clearText={() => setState({...state, email: ''})}
                size={scale(20)}
                style={{
                  borderWidth: 2,
                  borderRadius: 4,
                }}
                cleanButton={state.email ? true : false}
              />
            </RowComponent>
            <RowComponent
              styles={{
                flex: 1.5,
                marginTop: scale(2),
              }}>
              <InputComponent
                searchText={state.address}
                placeholder={electronicInvoiceAddress}
                onChange={searchText =>
                  setState({...state, address: searchText})
                }
                clearText={() => setState({...state, address: ''})}
                size={scale(22)}
                style={{
                  borderWidth: 2,
                  borderRadius: 4,
                }}
                editable={
                  state.customerType == electronicInvoiceIndividual
                    ? false
                    : true
                }
                cleanButton={state.address ? true : false}
              />
            </RowComponent>
          </SectionComponent>

          <SectionComponent
            styles={{
              flex: Platform.OS === 'ios' ? 6 : 7,
            }}>
            <View
              style={{
                flex: 0.5,
                alignItems: 'center',
              }}>
              <TitleComponent
                text={electronicInvoiceListProduct}
                size={scale(22)}
                color={maincolor}
                weight="700"
              />
            </View>
            <View
              style={{
                flex: 0.5,
              }}>
              <ListBillContent
                name={electronicInvoiceProductName}
                quantity={electronicInvoiceProductQuantity}
                price={electronicInvoiceProductPrice}
                vat={electronicInvoiceProductVat}
                total_price={electronicInvoiceTotal}
              />
            </View>
            <View
              style={{
                flex: 6,
              }}>
              <FlatList
                style={{flex: 1}}
                data={payment_details}
                renderItem={renderItem}
                keyExtractor={item => item.id}
                onEndReachedThreshold={1}
              />
            </View>
          </SectionComponent>

          <SectionComponent
            styles={{
              flex: Platform.OS === 'ios' ? 1 : 0.7,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <TouchableOpacity
              style={[
                {
                  backgroundColor: state.btnConfirm ? maincolor : 'gray',
                  marginVertical: scale(2),
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: scale(10),
                  flex: 1,
                  width: '100%',
                  height: scale(44),
                },
              ]}
              disabled={!state.btnConfirm}
              onPress={handleBillElectronic}>
              <Text
                style={{
                  color: 'white',
                  fontSize: scale(20),
                  fontWeight: '600',
                }}>
                {electronicInvoiceRegister}
              </Text>
            </TouchableOpacity>
          </SectionComponent>
        </Container>
        {state.isLoading ? (
          <View
            style={[
              globalStyles.loading,
              {
                backgroundColor: modalbackground,
              },
            ]}>
            <ActivityIndicator size="large" color={maincolor} />
          </View>
        ) : null}
      </View>
    </KeyboardAwareScrollView>
  );
};

export default ElectroicInvoiceDetail;
