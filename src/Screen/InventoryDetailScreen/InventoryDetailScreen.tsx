import {useState} from 'react';
import {
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Image,
  Modal,
  Pressable,
  TextInput,
  Alert,
} from 'react-native';
import {Text, View} from '../../Component/index';
import {heightWindow, scale, widthWindow} from '../../utils/style/Reponsive';
import {useAppSelector} from '../../hooks';
import Entypo from 'react-native-vector-icons/Entypo';
import {BASE_URL} from '../../api/ApiManager';
import axios from 'axios';
import {ThunkDispatch} from '@reduxjs/toolkit';
import {useDispatch} from 'react-redux';
import {updateInventoryItem} from '../../Redux/GetData';
import {languages} from '../../constants';
import { heightPercentageToDP as hp , widthPercentageToDP as wp} from 'react-native-responsive-screen'

const InventoryDetailScreen = (props: any) => {
  const {navigation, route} = props;
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [checkValue, setCheckValue] = useState<boolean>(true);
  const [valueInput, setValueInput] = useState<string>('');
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);

  const {access_token} = useAppSelector(state => state.counter);
  const listInventory = useAppSelector(state => state.getData.listInventory);

  const inventoryItem = listInventory.find(
    (item: {product_id: any}) => item.product_id == route?.params?.product?.id,
  );

  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const {maincolor, graycolor} = xmlData;
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;

  const checkLangue = (): any | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: any = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    messageApp,
    deliveryNote,
    handleProductScreenName,
    staffScreenAddressStaff,
    ExportWarehouseQuantityFood,
    ProductNotCreated,
    ProductNotSelected,
    CancelDelivery,
    CreateDelivery,
    DetailProduct,
    ProductCode,
    ProductNameInventory,
    PriceInventory,
    WarehouseTaxProduct,
    QuantityInventory,
    AddDelivery,
    QuantityDelivery,
    AddDeliveryExport,
    AddDeliveryError,
  }: any = langData;
  const handleGoBack = () => {
    navigation.pop(1);
  };
  const handleAddQuantity = async () => {
    const request = await axios({
      method: 'post',
      url: `${BASE_URL}/api/admin/input/addInvoiceInput`,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${access_token}`,
      },
      data: {
        quantity: [valueInput.toString()],
        product_id: [route?.params?.product?.id.toString()],
        price: [route?.params?.product?.price.toString()],
        payment_method: 'cash',
        input_status: 'success',
      },
    });
    if (request.data.status_code == 200) {
      const id = route?.params?.product?.id;
      const newQuantity = {
        quantity: inventoryItem.quantity + Number(valueInput),
      };
      dispatch(updateInventoryItem({id, newQuantity}));
      Alert.alert(messageApp, AddDeliveryExport);
      setModalVisible(false);
    } else {
      Alert.alert(messageApp, AddDeliveryError);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View
        style={{
          flex: 1,
        }}>
        <View w_100 flDirectionRow>
          <View w_80 jContentCenter aItemsCenter>
            <Text fontSize28 textCenter fontWeight700 mainColor>
              {DetailProduct}
            </Text>
          </View>
          <View w_15>
            <TouchableOpacity
              onPress={handleGoBack}
              style={styles.btnBackScreen}>
              <Entypo name="log-out" size={40} color={maincolor} />
            </TouchableOpacity>
          </View>
        </View>
        <View w_100 flDirectionRow mVertical2 
        style={{marginLeft: wp('1%')}}>
          <View w_45 jContentCenter aItemsFlexStart>
            <Text fontSize20 fontWeight600 mainColor>
              {ProductCode} :
            </Text>
          </View>
          <View w_50 aItemsFlexEnd>
            <Text fontWeight600 fontSize18>
              {inventoryItem.product?.product_code}
            </Text>
          </View>
        </View>
        <View w_100 flDirectionRow mVertical2>
          <View w_40 aItemsFlexStart
          style={{marginLeft: wp('1%')}}
          >
            <Text fontSize20 fontWeight600 mainColor>
              {ProductNameInventory} :
            </Text>
          </View>
          <View w_55 aItemsFlexEnd jContentCenter>
            <Text fontWeight600 fontSize18>
              {inventoryItem.product?.title}
            </Text>
          </View>
        </View>
        <View w_100 flDirectionRow mVertical2
        style={{marginLeft: wp('1%')}}
        >
          <View w_45 jContentCenter aItemsFlexStart>
            <Text fontSize20 fontWeight600 mainColor>
              {PriceInventory} :
            </Text>
          </View>
          <View w_50 aItemsFlexEnd>
            <Text fontWeight600 fontSize18>
              {inventoryItem.product?.price}đ
            </Text>
          </View>
        </View>
        <View w_100 flDirectionRow mVertical2
        style={{marginLeft: wp('1%')}}>
          <View w_45 jContentCenter aItemsFlexStart>
            <Text fontSize20 fontWeight600 mainColor>
              {WarehouseTaxProduct} :
            </Text>
          </View>
          <View w_50 aItemsFlexEnd>
            <Text fontWeight600 fontSize18>
              {inventoryItem.product?.vat}%
            </Text>
          </View>
        </View>
        <View w_100 flDirectionRow mVertical2>
          <View w_55 jContentCenter aItemsFlexStart
          style={{marginLeft: wp('1%')}}>
            <Text fontSize20 fontWeight600 mainColor>
              {QuantityInventory} :
            </Text>
          </View>
          <View w_40 aItemsFlexEnd>
            <Text fontWeight600 fontSize18>
              {inventoryItem.quantity}
            </Text>
          </View>
        </View>
        <View w_100 flDirectionRow aItemsCenter jContentCenter mTop10>
          <Image
            style={styles.imageProduct}
            resizeMode="contain"
            source={{
              uri: inventoryItem.product.image,
            }}
          />
        </View>
        <View flDirectionRow jContentBetween
        style={{
          width: wp('98%'),
          marginLeft: wp('1%')
        }}
        >
          <TouchableOpacity
            onPress={() => {
              setModalVisible(true);
              setCheckValue(true);
            }}
            style={[
              styles.btnConfirm,
              {
                backgroundColor: maincolor,
              },
            ]}>
            <Text textCenter whiteColor fontSize18 fontWeight600>
              {AddDelivery}
            </Text>
          </TouchableOpacity>
        </View>
        <View flex1 jContentCenter aItemsCenter >
          <Modal animationType="fade" transparent={true} visible={modalVisible}>
            <View flex1 jContentCenter aItemsCenter>
              <View modalView style={styles.modalView}>
                <View 
                style={{
                  width: wp('75%'),
                }}
                >
                  <View flDirectionRow aItemsCenter w_100>
                    <View w_35>
                      <Text fontSize20 fontWeight600 mainColor>
                        {QuantityDelivery}:
                      </Text>
                    </View>
                    <View w_35 mHorizontal10 mVertical6>
                      <TextInput
                        keyboardType={'numeric'}
                        style={[
                          styles.inputPrice,
                          {
                            borderColor: maincolor,
                          },
                        ]}
                        onChangeText={text => {
                          const filteredText = text.replace(/[^0-9]/g, '');
                          if (text.trim() === '') {
                            setValueInput(filteredText);
                            setCheckValue(true);
                          } else {
                            setValueInput(filteredText);
                            setCheckValue(false);
                          }
                        }}
                      />
                    </View>
                  </View>
                </View>
                <View flDirectionRow w_80 jContentAround>
                  <Pressable
                    style={[
                      {
                        backgroundColor: checkValue ? graycolor : maincolor,
                        width: '45%',
                        marginVertical: scale(10),
                        height: scale(48),
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: scale(10),
                      },
                    ]}
                    disabled={checkValue}
                    onPress={() => handleAddQuantity()}>
                    <Text whiteColor fontWeightBold textCenter>
                      {CreateDelivery}
                    </Text>
                  </Pressable>
                  <Pressable
                    style={[
                      {
                        backgroundColor: maincolor,
                        width: '45%',
                        marginVertical: scale(10),
                        height: scale(48),
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: scale(10),
                      },
                    ]}
                    onPress={() => setModalVisible(!modalVisible)}>
                    <Text whiteColor fontWeightBold textCenter>
                      {CancelDelivery}
                    </Text>
                  </Pressable>
                </View>
              </View>
            </View>
          </Modal>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default InventoryDetailScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    // backgroundColor:'red'
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnBackScreen: {
    alignItems: 'flex-end',
    marginVertical: scale(8),
  },
  imageProduct: {
    width: widthWindow * 1,
    height: heightWindow * 0.4,
  },
  btnConfirm: {
    marginVertical: scale(10),
    height: scale(48),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scale(10),
    flex: 1,
  },
  modalView: {
    width: '80%',
    margin: scale(20),
    backgroundColor: 'white',
    alignItems: 'center',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalText: {
    marginBottom: scale(15),
    marginVertical: 7,
  },
  btnDeleteProduct: {
    width: scale(100),
    borderRadius: scale(20),
    padding: scale(10),
    elevation: 2,
    marginHorizontal: 5,
  },
  inputPrice: {
    borderBottomWidth: scale(1),
    height: scale(40),
    textAlign: 'center',
  },
});
