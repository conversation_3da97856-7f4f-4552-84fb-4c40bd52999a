import {
  Alert,
  Keyboard,
  SafeAreaView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import {
  Text,
  View,
  DropDownComponents,
  ModalCreateAcency,
} from '../../Component/index';
import {getInventoryListAll} from '../../Redux/GetData';

import React, {useEffect, useMemo, useState} from 'react';
import {ManagementWarehouseScreenCss} from './ManagementWarehouseScreenCss';
import {useAppSelector} from '../../hooks';
import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';
import {scale} from '../../utils/style/Reponsive';
import Entypo from 'react-native-vector-icons/Entypo';
import {ThunkDispatch} from '@reduxjs/toolkit';
import {useDispatch} from 'react-redux';
import {ActivityIndicator} from 'react-native';
import {languages} from '../../constants';
import {
  fetchListProduct,
  getListAgencies,
  handleResetProductName,
} from '../../Redux/GetData';
import {createWarehouseReceipt} from '../../api/handleApi';
import {setStateShowOption} from '../../Redux/Slide';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

interface IAddProducts {
  [key: string]: string | undefined;
  componentAddProductChooseCategory?: string;
  componentAddProductCategories?: string;
  componentAddProductName?: string;
  componentAddProductPrice?: string;
  componentAddProductVat?: string;
  componentAddProductImage?: string;
  componentAddProductCreate?: string;
}
const ManagementWarehouseScreen = (props: any) => {
  const {navigation} = props;
  const nameOpition = useAppSelector(state => state.counter.nameSelectOpition);

  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {maincolor, graycolor, modalbackground, blackcolor} = xmlData;
  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const listProduct = useAppSelector(state => state.getData.listProduct);
  const listAgencies = useAppSelector(state => state.getData.listAgencies);
  const access_token = useAppSelector(state => state.counter.access_token);
  const listCategory = useAppSelector(state => state.getData.listCategory);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const [selectedId, setSelectedId] = useState<number | undefined>();
  const checkLangue = (): IAddProducts | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: IAddProducts = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    messageApp,
    Cash,
    Transfer,
    Bank,
    WarningDiscount,
    AddDeliveryBillExport,
    WarehouseExportError,
    ExportBill,
    ProductNameInventory,
    ProductNotSelected,
    AgencyName,
    AgenciesHaveNotSelected,
    AgencyHaveNotSelected,
    CodeFormInput,
    ExportWarehouseQuantityFood,
    PriceImport,
    DiscountPrice,
    TotalPrice,
    PaymentMethod,
    NoteExport,
    CreateDeliveryImport,
    CancelDelivery,
    BillExportExist,
  }: any = langData;
  const {componentAddProductChooseCategory}: any = langData;
  const [state, setState] = useState<any>({
    loadingData: false,
    productName: '',
    billCode: '',
    agencyName: '',
    price: 0,
    quantity: 0,
    discount: 0,
    paymentMethod: '',
    note: '',
    listProduct: [],
    listAgencies: [],
    checkInput: true,
    totalPaymentPrice: 0,
    agencyId: 0,
    productId: 0,
    totalPrice: 0,
  });
  const showModal: () => void = () => {
    dispatch(setStateShowOption({valid: false, nameOpiton: 'CreateAgency'}));
  };
  const currencySymbol = useAppSelector(state => state.counter?.infoAdmin?.admin?.store?.currency_symbol) || "đ";
  if (state.price && state.quantity) {
    state.totalPaymentPrice =
      parseInt(state.price) * parseInt(state.quantity) -
      parseInt(state.discount);
  }

  if (state.price && state.quantity) {
    state.totalPrice = parseInt(state.price) * parseInt(state.quantity);
  }
  const radioButtons: {
    id: number;
    label: string;
    value: string;
  }[] = useMemo(
    () => [
      {
        id: 1,
        label: Cash,
        value: 'cash',
      },
      {
        id: 2,
        label: Transfer,
        value: 'transfer',
      },
      {
        id: 3,
        label: Bank,
        value: 'bank',
      },
    ],
    [],
  );

  const handleSelectProductName = (item: {value: string; key: number}) => {
    setState({
      ...state,
      productName: item.value,
      productId: item.key,
    });
  };

  const handleSelectAgencyName = (item: {value: string; key: number}) => {
    setState({
      ...state,
      agencyName: item.value,
      agencyId: item.key,
    });
  };

  const handleSelectPayment = (item: {value: string; id: number}) => {
    setSelectedId(item.id);
    setState({
      ...state,
      paymentMethod: item.value,
    });
  };

  const handleGoBack = () => {
    navigation.pop(1);
  };
  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };
  const handleCancer = () => {
    setState({
      ...state,
      productName: '',
      billCode: '',
      agencyName: '',
      price: '',
      quantity: '',
      discount: '',
      paymentMethod: '',
      note: '',
      loadingData: false,
      totalPaymentPrice: 0,
    });
    dispatch(handleResetProductName());
    setSelectedId(undefined);
  };
  useEffect(() => {
    dispatch(fetchListProduct({access_token, messageApp}));
    dispatch(getListAgencies({access_token, messageApp}));
  }, []);
  useEffect(() => {
    if (state.totalPaymentPrice < 0) {
      Alert.alert(messageApp, WarningDiscount);
    }
    setState({
      ...state,
      totalPaymentPrice: 0,
      quantity: 0,
      price: 0,
    });
  }, [state.totalPaymentPrice]);
  useEffect(() => {
    setState((prevState: any) => ({
      ...prevState,
      listProduct:
        listProduct == undefined
          ? []
          : listProduct.map((item: {id: string; title: string}) => {
              return {key: item.id, value: item.title};
            }),
      listAgencies: listAgencies,
    }));
  }, [listProduct, listAgencies]);

  useEffect(() => {
    if (
      state.productName &&
      state.billCode &&
      state.agencyName &&
      state.price &&
      state.quantity &&
      state.paymentMethod
    ) {
      setState({
        ...state,
        checkInput: false,
      });
    } else {
      setState({
        ...state,
        checkInput: true,
      });
    }
  }, [
    state.productName,
    state.billCode,
    state.agencyName,
    state.price,
    state.quantity,
    state.discount,
    state.paymentMethod,
  ]);

  const createWarehouse = () => {
    createWarehouseReceipt(access_token, state).then(res => {
      setState({
        ...state,
        loadingData: true,
      });
      if (res.data.status_code === 200) {
        handleCancer();
        Alert.alert(messageApp, AddDeliveryBillExport);
        dispatch(
          getInventoryListAll({
            access_token,
            messageApp: '',
          }),
        );
      } else if (res.data.errors.code == 'exists') {
        setState({
          ...state,
          loadingData: false,
        });
        Alert.alert(messageApp, BillExportExist);
      } else {
        setState({
          ...state,
          loadingData: false,
        });
        Alert.alert(messageApp, WarehouseExportError);
      }
    });
  };
  return (
    <SafeAreaView style={[ManagementWarehouseScreenCss.container]}>
      <KeyboardAwareScrollView
        style={[
          ManagementWarehouseScreenCss.container,
          {
            marginHorizontal: scale(4),
          },
        ]}
        contentContainerStyle={{flexGrow: 1}}
        keyboardShouldPersistTaps="handled">
        <TouchableWithoutFeedback onPress={dismissKeyboard}>
          <View flex1>
            <View w_100 flDirectionRow>
              <View w_70 jContentCenter aItemsCenter>
                <View
                  style={[
                    {
                      height: scale(48),
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRadius: scale(10),
                      width: '100%',
                    },
                  ]}>
                  <Text fontSize28 textCenter fontWeight700 mainColor>
                    {ExportBill}
                  </Text>
                </View>
              </View>

              <View w_30 jContentCenter aItemsCenter>
                <TouchableOpacity
                  style={ManagementWarehouseScreenCss.btnBackScreen}
                  onPress={handleGoBack}>
                  <Entypo name="log-out" size={40} color={maincolor} />
                </TouchableOpacity>
              </View>
            </View>

            <View
              w_100
              style={{
                flexDirection: 'column',
              }}>
              <View w_100>
                <View w_100>
                  <Text blackColor fontSize16 fontWeight800>
                    {ProductNameInventory}
                  </Text>
                </View>
                <View w_100 flDirectionRow>
                  <View w_100>
                    <DropDownComponents
                      value={
                        state.listProduct.length === 0
                          ? ProductNotSelected
                          : listCategory?.length == 0
                          ? ProductNotSelected
                          : componentAddProductChooseCategory
                      }
                      data={state.listProduct}
                      onSelect={handleSelectProductName}
                      widthComponent="100%"
                    />
                  </View>
                </View>
              </View>

              <View w_100>
                <View w_100>
                  <Text blackColor fontSize16 fontWeight800>
                    {AgencyName}
                  </Text>
                </View>
                <View w_100 flDirectionRow>
                  <View w_90>
                    <DropDownComponents
                      value={
                        state.listAgencies.length === 0
                          ? AgenciesHaveNotSelected
                          : state.agencyName?.length == 0
                          ? AgencyHaveNotSelected
                          : state.agencyName
                      }
                      data={state.listAgencies}
                      onSelect={handleSelectAgencyName}
                      widthComponent="100%"
                    />
                  </View>
                  <View w_10 jContentCenter aItemsCenter>
                    <TouchableOpacity onPress={showModal}>
                      <SimpleLineIcons
                        name="plus"
                        size={30}
                        color={maincolor}
                      />
                    </TouchableOpacity>
                  </View>
                </View>
              </View>

              <View w_100 flDirectionRow mVertical4>
                <View w_35>
                  <Text blackColor fontSize16 fontWeight800>
                    {CodeFormInput}
                  </Text>
                </View>
                <View
                  flDirectionRow
                  w_65
                  borderWidth1
                  bRadius5
                  aItemsCenter
                  h_40
                  borderMainColor>
                  <TextInput
                    onChangeText={(e: string) => {
                      setState({
                        ...state,
                        billCode: e,
                      });
                    }}
                    style={[
                      ManagementWarehouseScreenCss.inputText,
                      {
                        color: blackcolor,
                      },
                    ]}
                    keyboardType={'numeric'}
                    value={
                      state.billCode ? state.billCode.toString() : undefined
                    }
                  />
                </View>
              </View>

              <View w_100 flDirectionRow mVertical4>
                <View w_35>
                  <Text blackColor fontSize16 fontWeight800>
                    {ExportWarehouseQuantityFood}
                  </Text>
                </View>
                <View
                  flDirectionRow
                  w_65
                  borderWidth1
                  bRadius5
                  aItemsCenter
                  h_40
                  borderMainColor>
                  <TextInput
                    onChangeText={(e: string) => {
                      setState({
                        ...state,
                        quantity: e,
                      });
                    }}
                    style={[
                      ManagementWarehouseScreenCss.inputText,
                      {
                        color: blackcolor,
                      },
                    ]}
                    keyboardType={'numeric'}
                    value={
                      state.quantity ? state.quantity.toString() : undefined
                    }
                  />
                </View>
              </View>
              <View w_100 flDirectionRow mVertical4>
                <View w_35>
                  <Text blackColor fontSize16 fontWeight800>
                    {PriceImport}
                  </Text>
                </View>
                <View
                  flDirectionRow
                  w_65
                  borderWidth1
                  bRadius5
                  aItemsCenter
                  h_40
                  borderMainColor>
                  <TextInput
                    onChangeText={(e: string) => {
                      setState({
                        ...state,
                        price: e,
                      });
                    }}
                    style={[
                      ManagementWarehouseScreenCss.inputText,
                      {
                        color: blackcolor,
                      },
                    ]}
                    keyboardType={'numeric'}
                    value={state.price ? state.price.toString() : undefined}
                  />
                </View>
              </View>
              <View w_100 flDirectionRow mVertical4>
                <View w_35>
                  <Text blackColor fontSize16 fontWeight800>
                    {DiscountPrice}
                  </Text>
                </View>
                <View
                  flDirectionRow
                  w_65
                  borderWidth1
                  bRadius5
                  aItemsCenter
                  h_40
                  borderMainColor>
                  <TextInput
                    onChangeText={(e: string) => {
                      console.log("e", e);
                      // Nếu xoá hết thì gán giá trị là 0
                      if (e === '' || parseInt(e) < 0) {
                        e = '0';
                      }
                      // Nếu giá trị nhập vào lớn hơn giá trị tổng thì gán giá trị là 0 và hiển thị cảnh báo
                      if (parseInt(e) > state.totalPrice) {
                        e = '0';
                        Alert.alert(messageApp, WarningDiscount);
                      }
                      setState({
                        ...state,
                        discount: e,
                      });
                    }}
                    style={[
                      ManagementWarehouseScreenCss.inputText,
                      {
                        color: blackcolor,
                      },
                    ]}
                    keyboardType={'numeric'}
                    value={
                      state.discount ? state.discount.toString() : undefined
                    }
                  />
                </View>
              </View>
              <View w_100 flDirectionRow mVertical4>
                <View w_35>
                  <Text blackColor fontSize16 fontWeight800>
                    {TotalPrice}
                  </Text>
                </View>
                <View
                  flDirectionRow
                  w_65
                  aItemsCenter
                  borderMainColor
                  style={{justifyContent: 'flex-end', paddingRight: scale(10)}}>
                  <Text fontSize20>
                    {state.totalPaymentPrice} {currencySymbol}
                  </Text>
                </View>
              </View>
              <View w_100 mVertical4>
                <View>
                  <Text blackColor fontSize16 fontWeight800>
                    {PaymentMethod}
                  </Text>
                </View>
                <View w_100 flDirectionRow>
                  {radioButtons.map((item: any) => (
                    <TouchableOpacity
                      key={item.id}
                      style={[styles.radioButton]}
                      onPress={() => handleSelectPayment(item)}>
                      <View
                        style={[
                          styles.radioButtonOuter,
                          selectedId === item.id ? styles.outerSelected : null,
                        ]}>
                        {selectedId === item.id && (
                          <View style={styles.radioButtonInner} />
                        )}
                      </View>
                      <Text>{item.label}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
              <View w_100 flDirectionRow style={{height: scale(80)}} mVertical4>
                <View w_35>
                  <Text blackColor fontSize16 fontWeight800>
                    {NoteExport}
                  </Text>
                </View>
                <View
                  flDirectionRow
                  w_65
                  borderWidth1
                  bRadius5
                  aItemsCenter
                  borderMainColor>
                  <TextInput
                    onChangeText={(e: string) => {
                      setState({
                        ...state,
                        note: e,
                      });
                    }}
                    style={[
                      {
                        color: blackcolor,
                        borderRadius: scale(5),
                        paddingLeft: scale(10),
                        fontSize: scale(14),
                        width: '100%',
                      },
                    ]}
                    value={state.note ? state.note.toString() : undefined}
                    multiline
                  />
                </View>
              </View>
            </View>

            <View w_100 flDirectionRow jContentAround mVertical4>
              <View w_45>
                <TouchableOpacity
                  style={[
                    ManagementWarehouseScreenCss.btnAddProduct,
                    {
                      backgroundColor: state.checkInput ? graycolor : maincolor,
                    },
                  ]}
                  disabled={state.checkInput}
                  onPress={createWarehouse}>
                  <Text whiteColor fontWeight800 fontSize18>
                    {CreateDeliveryImport}
                  </Text>
                </TouchableOpacity>
              </View>
              <View w_45>
                <TouchableOpacity
                  style={[
                    ManagementWarehouseScreenCss.btnAddProduct,
                    {
                      backgroundColor: false ? graycolor : maincolor,
                    },
                  ]}
                  onPress={handleCancer}>
                  <Text whiteColor fontWeight800 fontSize18>
                    {CancelDelivery}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAwareScrollView>
      {state.loadingData ? (
        <View
          style={[
            {
              position: 'absolute',
              justifyContent: 'center',
              alignContent: 'center',
              backgroundColor: modalbackground,
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
            },
          ]}>
          <ActivityIndicator size="large" color={maincolor} />
        </View>
      ) : null}
      {nameOpition === 'CreateAgency' ? (
        <View loadingScreen>
          <ModalCreateAcency />
        </View>
      ) : null}
    </SafeAreaView>
  );
};

export default React.memo(ManagementWarehouseScreen);
const styles = StyleSheet.create({
  radioButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: scale(2),
    borderColor: 'transparent',
    borderRadius: scale(12),
    marginHorizontal: scale(4),
  },

  radioButtonOuter: {
    height: scale(24),
    width: scale(24),
    borderRadius: scale(12),
    borderWidth: scale(2),
    borderColor: 'gray',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: scale(15),
  },
  outerSelected: {
    borderColor: 'black',
  },
  radioButtonInner: {
    height: scale(12),
    width: scale(12),
    borderRadius: scale(6),
    backgroundColor: 'black',
  },
});
