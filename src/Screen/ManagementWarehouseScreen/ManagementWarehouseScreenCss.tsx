import {Platform, StyleSheet} from 'react-native';
import {heightWindow, scale, setWidth} from '../../utils/style/Reponsive';

export const ManagementWarehouseScreenCss = StyleSheet.create({
  container: {
    flex: 1,
  },
  itemRow: {
    borderBottomColor: '#ccc',
    marginBottom: 10,
    borderBottomWidth: 1,
  },
  itemImage: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
  },
  itemText: {
    fontSize: 16,
    padding: 5,
  },
  loader: {
    marginTop: 10,
    alignItems: 'center',
  },
  btnBackScreen: {
    alignItems: 'flex-end',
    marginVertical: scale(8),
  },
  card: {
    height: setWidth(35),
    width: '100%',
    marginTop: Platform.OS === 'ios' ? 15 : 20,
    borderRadius: 15,
    backgroundColor: 'white',
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  loading: {
    position: 'absolute',
    justifyContent: 'center',
    alignContent: 'center',
    top: -scale(20),
    bottom: 0,
    right: -scale(20),
    left: -scale(20),
  },
  btnHandleProduct: {
    marginVertical: scale(10),
    height: scale(48),
    width: '40%',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scale(10),
  },
  btnAddProduct: {
    height: scale(40),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scale(12),
  },
  inputText: {
    borderRadius: scale(5),
    height: scale(44),
    paddingLeft: scale(10),
    fontSize: scale(14),
    width: '100%',
  },
});
