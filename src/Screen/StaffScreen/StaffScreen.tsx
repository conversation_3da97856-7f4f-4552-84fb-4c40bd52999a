import React, {useCallback, useEffect, useState} from 'react';
import {
  ActivityIndicator,
  KeyboardAvoidingView,
  Linking,
  Modal,
  Platform,
  Pressable,
  SafeAreaView,
  ScrollView,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import {Formik} from 'formik';
import {useDispatch} from 'react-redux';
import * as Yup from 'yup';

import SelectDropdown from 'react-native-select-dropdown';
import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {Text, View} from '../../Component/index';
import {StackNavigationProp} from '@react-navigation/stack';

import {setFlagStaff, setLoadingScreen} from '../../Redux/Slide';
import {StaffScreenCss} from './StaffScreenCss';
import {handleCreateStaff, handleGetAllStaffRedux} from '../../Redux/GetData';
import {heightWindow, scale} from '../../utils/style/Reponsive';
import {useAppSelector} from '../../hooks';
import {ThunkDispatch} from '@reduxjs/toolkit';
import {languages} from '../../constants';
type RootStackParamList = {
  DetailStaff: {};
};
interface IProps {
  navigation: StackNavigationProp<RootStackParamList, keyof RootStackParamList>;
}
interface IStaffScreen {
  [key: string]: string | undefined;
  staffScreenCreateStaffs?: string;
  staffScreenNameStaff?: string;
  staffScreenAddressStaff?: string;
  staffScreenPhoneStaff?: string;
  staffScreenPasswordStaff?: string;
  staffScreenPasswordConfirmStaff?: string;
  staffScreenStatusStaff?: string;
  staffScreenAddStaff?: string;
  staffScreenCancelStaff?: string;
  staffScreenStatusStaffWork?: string;
  confirmPasswordMessage?: string;
  minValidMessageName?: string;
  maxValidMessageName?: string;
  minValidMessageAddress?: string;
  maxValidMessageAddress?: string;
  agree?: string;
  cancel?: string;
  minValidMessageNumberPhone?: string;
  maxValidMessageNumberPhone?: string;
  matchNumberPhone?: string;
  fieldIsBlank?: string;
  matchesNumberPhone?: string;
  minValidMessagePassword?: string;
  maxValidMessagePassword?: string;
}
const StaffScreen = (props: IProps) => {
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {
    maincolor,
    blackcolor,
    modalbackground,
    graycolor,
    backgroundcolor,
    minValidNumber,
    maxValidNumberPhone,
    maxValidNumber,
    minValidPassword,
    maxValidPassword,
    useroffline,
    useronline,
    userstop,
  } = xmlData;
  const {navigation} = props;
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [eye, setEye] = useState<boolean>(true);
  const [eyeConfim, setEyeConfim] = useState<boolean>(true);
  const [status, setStatus] = useState(0);
  const [checkButton, setCheckButton] = useState<boolean>(true);
  const {access_token, isLoadingScreen, flagStaff} = useAppSelector(
    state => state.counter,
  );
  const listStaff = useAppSelector(state => state.getData.listStaff);
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): IStaffScreen | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: IStaffScreen = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    staffScreenCreateStaffs,
    staffScreenNameStaff,
    staffScreenAddressStaff,
    staffScreenPhoneStaff,
    staffScreenPasswordStaff,
    staffScreenPasswordConfirmStaff,
    staffScreenStatusStaff,
    staffScreenAddStaff,
    staffScreenCancelStaff,
    staffScreenStatusStaffWork,
    confirmPasswordMessage,
    minValidMessageName,
    maxValidMessageName,
    minValidMessageAddress,
    maxValidMessageAddress,
    agree,
    cancel,
    minValidMessageNumberPhone,
    maxValidMessageNumberPhone,
    matchNumberPhone,
    fieldIsBlank,
    matchesNumberPhone,
    minValidMessagePassword,
    maxValidMessagePassword,
    addStaffSuccess,
    staffExistButDeleted,
    infoStaffExist,
    notFullValidStaff,
    addStaffFaill,
    messageApp,
    RestoreStaffSuccess,
    onlineStatus,
    offlineStatus,
    stopStatus,
  }: any = langData;
  const countries: string[] = [onlineStatus, offlineStatus, stopStatus];
  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const regexPattern = matchNumberPhone;
  const regexPhone = new RegExp(regexPattern);
  const SignupSchema = Yup.object().shape({
    name: Yup.string()
      .min(minValidNumber, minValidMessageName)
      .max(maxValidNumber, maxValidMessageName)
      .required(fieldIsBlank),
    address: Yup.string()
      .min(minValidNumber, minValidMessageAddress)
      .max(maxValidNumber, maxValidMessageAddress)
      .required(fieldIsBlank),
    phone: Yup.string()
      .min(minValidNumber, minValidMessageNumberPhone)
      .max(maxValidNumberPhone, maxValidMessageNumberPhone)
      .required(fieldIsBlank)
      .matches(regexPhone, matchesNumberPhone),
    password: Yup.string()
      .min(minValidPassword, minValidMessagePassword)
      .max(maxValidPassword, maxValidMessagePassword)
      .required(fieldIsBlank),
    confirmPassWord: Yup.string()
      .min(minValidPassword, minValidMessagePassword)
      .max(maxValidPassword, maxValidMessagePassword)
      .required(fieldIsBlank)
      .oneOf([Yup.ref('password')], confirmPasswordMessage),
  });

  const handleSeteye = () => {
    setEye(!eye);
  };
  const handleConfimSeteye = () => {
    setEyeConfim(!eyeConfim);
  };
  // show modal registry staff
  const showModalRegistryStaff = () => {
    setModalVisible(true);
    setCheckButton(true);
  };
  //handle Add Staff
  const handleCreateUser = async (values: {
    name: string;
    address: string;
    phone: number;
    password: string;
  }) => {
    const {name, address, phone, password} = values;
    try {
      await dispatch(
        handleCreateStaff({
          access_token,
          name,
          address,
          phone,
          password,
          status,
          addStaffSuccess,
          staffExistButDeleted,
          infoStaffExist,
          notFullValidStaff,
          addStaffFaill,
          agree,
          cancel,
          messageApp,
          RestoreStaffSuccess,
        }),
      );
      dispatch(setFlagStaff());
    } catch (err: unknown) {
      console.log(err);
    }

    setModalVisible(false);
  };

  const handleStaffDetail = (index: number) => {
    navigation.navigate('DetailStaff', listStaff[index]);
  };

  const dialCall = (number: number) => {
    const prefix: string = Platform.OS === 'android' ? 'tel:' : 'telprompt:';
    const phoneNumber: string = `${prefix}${number}`;
    Linking.openURL(phoneNumber);
  };

  const refreshScreen = useCallback(() => {
    dispatch(setLoadingScreen({valid: true}));
    const timeoutID = setTimeout(() => {
      dispatch(setFlagStaff());
      dispatch(setLoadingScreen({valid: false}));
    }, 1000);
    return () => {
      clearTimeout(timeoutID);
    };
  }, [dispatch]);

  //Handle getList User
  useEffect(() => {
    dispatch(setLoadingScreen({valid: true}));
    dispatch(handleGetAllStaffRedux({access_token, messageApp}));
    dispatch(setLoadingScreen({valid: false}));
  }, [access_token, dispatch, flagStaff, messageApp]);

  return (
    <SafeAreaView style={StaffScreenCss.container}>
      <View >
        <View aItemsCenter flDirectionRow jContentBetween mVertical6>
          <View />
          <View>
            <Pressable
              style={[
                StaffScreenCss.button,
                {
                  backgroundColor: maincolor,
                },
              ]}
              onPress={showModalRegistryStaff}>
              <Text whiteColor textCenter fontWeightBold>
                {staffScreenCreateStaffs}
              </Text>
            </Pressable>
          </View>
          <View>
            <SimpleLineIcons
              name="refresh"
              size={40}
              color={maincolor}
              onPress={refreshScreen}
            />
          </View>
        </View>
        <ScrollView
          style={{
            height: '100%',
          }}
          showsVerticalScrollIndicator={false}>
          {listStaff &&
            listStaff.map(
              (
                item: {
                  name: string;
                  status: number;
                  phone: number;
                },
                index: number,
              ) => (
                <View
                  flDirectionRow
                  mVertical6
                  bRadius8
                  borderBlackColor
                  style={[
                    (StaffScreenCss.btnSettingMain,
                    {
                      backgroundColor: userstop,
                    }),
                    item.status === 1 && {
                      backgroundColor: useronline,
                    },
                    item.status === -1 && {
                      backgroundColor: useroffline,
                    },
                  ]}
                  key={index}>
                  <View
                    flDirectionRow
                    jContentBetween
                    aItemsCenter
                    borderWidth2
                    bRadius8>
                    <View w_40>
                      <TouchableOpacity
                        onPress={() => handleStaffDetail(index)}
                        style={StaffScreenCss.btnSettingTextName}>
                        <Text
                          style={StaffScreenCss.TextName}
                          blackColor
                          fontWeight600
                          fontSize16
                          numberOfLines={1}>
                          {item.name.length < 15
                            ? item.name
                            : `${item.name.substring(0, 15)}...`}
                        </Text>
                      </TouchableOpacity>
                    </View>
                    {item.status !== undefined && (
                      <TouchableOpacity
                        style={StaffScreenCss.view20Persent}
                        onPress={() => handleStaffDetail(index)}>
                        <Text paddingLeft6 blackColor fontWeight600>
                          {item.status === 0
                            ? 'Stop'
                            : item.status === 1
                            ? 'Online'
                            : 'Offline'}
                        </Text>
                      </TouchableOpacity>
                    )}
                    <View w_40 aItemsCenter flDirectionRow jContentAround>
                      <MaterialIcons
                        name="call"
                        size={scale(30)}
                        color="#000"
                        onPress={() => {
                          dialCall(item.phone);
                        }}
                      />
                      <Text
                        paddingLeft6
                        blackColor
                        fontWeight600
                        onPress={() => {
                          dialCall(item.phone);
                        }}>
                        {item.phone}
                      </Text>
                    </View>
                  </View>
                </View>
              ),
            )}
        </ScrollView>
        <Formik
          initialValues={{
            name: '',
            address: '',
            phone: 0,
            password: '',
            confirmPassWord: '',
          }}
          validationSchema={SignupSchema}
          onSubmit={handleCreateUser}>
          {({
            handleChange,
            values,
            errors,
            touched,
            isValid,
            setFieldTouched,
            dirty,
            resetForm,
          }) => (
            <Modal
              animationType="fade"
              transparent={true}
              visible={modalVisible}>
              <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
                <ScrollView
                  showsVerticalScrollIndicator={false}
                  style={{
                    height: heightWindow * 0.9,
                  }}>
                  <View
                    style={[
                      StaffScreenCss.viewRegisterStaff,
                      {
                        shadowColor: blackcolor,
                      },
                    ]}>
                    <Text
                      textCenter
                      fontSize22
                      fontWeight600
                      blackColor
                      style={StaffScreenCss.modalTextHeader}>
                      {staffScreenCreateStaffs}
                    </Text>
                    <View>
                      <View>
                        <Text
                          fontSize16
                          blackColor
                          fontWeight500
                          style={StaffScreenCss.modalText}>
                          {staffScreenNameStaff}:
                        </Text>
                        <TextInput
                          onChangeText={handleChange('name')}
                          onBlur={() => setFieldTouched('name')}
                          style={[
                            StaffScreenCss.modalTextInput,
                            {
                              borderColor: maincolor,
                              color: blackcolor,
                            },
                          ]}
                        />
                        <View mVertical2>
                          {errors.name && touched.name ? (
                            <Text redText>{errors.name}</Text>
                          ) : null}
                        </View>
                      </View>
                      <View>
                        <Text
                          fontSize16
                          blackColor
                          fontWeight500
                          style={StaffScreenCss.modalText}>
                          {staffScreenAddressStaff}:
                        </Text>
                        <TextInput
                          onChangeText={handleChange('address')}
                          onBlur={() => setFieldTouched('address')}
                          style={[
                            StaffScreenCss.modalTextInput,
                            {
                              borderColor: maincolor,
                              color: blackcolor,
                            },
                          ]}
                        />
                        <View mVertical2>
                          {errors.address && touched.address ? (
                            <Text redText>{errors.address}</Text>
                          ) : null}
                        </View>
                      </View>
                      <View>
                        <Text
                          fontSize16
                          blackColor
                          fontWeight500
                          style={StaffScreenCss.modalText}>
                          {staffScreenPhoneStaff}:
                        </Text>
                        <TextInput
                          onChangeText={handleChange('phone')}
                          onBlur={() => setFieldTouched('phone')}
                          style={[
                            StaffScreenCss.modalTextInput,
                            {
                              borderColor: maincolor,
                              color: blackcolor,
                            },
                          ]}
                          keyboardType={'numeric'}
                        />
                        <View mVertical2>
                          {errors.phone && touched.phone ? (
                            <Text redText>{errors.phone}</Text>
                          ) : null}
                        </View>
                      </View>
                      <View>
                        <Text
                          fontSize16
                          blackColor
                          fontWeight500
                          style={StaffScreenCss.modalText}>
                          {staffScreenPasswordStaff}:
                        </Text>
                        <TextInput
                          onChangeText={handleChange('password')}
                          onBlur={() => setFieldTouched('password')}
                          style={[
                            StaffScreenCss.modalTextInput,
                            {
                              borderColor: maincolor,
                              color: blackcolor,
                            },
                          ]}
                          secureTextEntry={eye}
                          keyboardType={'ascii-capable'}
                        />
                        <TouchableOpacity
                          onPress={handleSeteye}
                          style={StaffScreenCss.formikPassword}>
                          <Ionicons
                            name={eye ? 'eye-off' : 'eye'}
                            size={scale(30)}
                            style={[
                              StaffScreenCss.formikIconShowPassword,
                              {
                                color: maincolor,
                              },
                            ]}
                          />
                        </TouchableOpacity>
                        <View mVertical2>
                          {errors.password && touched.password ? (
                            <Text redText>{errors.password}</Text>
                          ) : null}
                        </View>
                      </View>
                      <View>
                        <Text
                          fontSize16
                          blackColor
                          fontWeight500
                          style={StaffScreenCss.modalText}>
                          {staffScreenPasswordConfirmStaff}:
                        </Text>
                        <TextInput
                          style={[
                            StaffScreenCss.modalTextInput,
                            {
                              borderColor: maincolor,
                              color: blackcolor,
                            },
                          ]}
                          onChangeText={handleChange('confirmPassWord')}
                          onBlur={() => setFieldTouched('confirmPassWord')}
                          secureTextEntry={eyeConfim}
                        />
                        <TouchableOpacity
                          onPress={handleConfimSeteye}
                          style={StaffScreenCss.formikPassword}>
                          <Ionicons
                            name={eyeConfim ? 'eye-off' : 'eye'}
                            size={scale(30)}
                            style={[
                              StaffScreenCss.formikIconShowPassword,
                              {
                                color: maincolor,
                              },
                            ]}
                          />
                        </TouchableOpacity>
                        <View mVertical2>
                          {errors.confirmPassWord && touched.confirmPassWord ? (
                            <Text redText>{errors.confirmPassWord}</Text>
                          ) : null}
                        </View>
                      </View>
                      <Text
                        fontSize16
                        blackColor
                        fontWeight500
                        style={StaffScreenCss.modalText}>
                        {staffScreenStatusStaff}:
                      </Text>
                      <SelectDropdown
                        buttonStyle={[
                          StaffScreenCss.viewSelectStatus,
                          {
                            borderColor: maincolor,
                            backgroundColor: backgroundcolor,
                          },
                        ]}
                        defaultButtonText={staffScreenStatusStaffWork}
                        data={countries}
                        onSelect={selectedItem => {
                          if (selectedItem === 'Offline') {
                            setStatus(-1);
                          }
                          if (selectedItem === 'Stop') {
                            setStatus(0);
                          }
                          if (selectedItem === 'Online') {
                            setStatus(1);
                          }
                          setCheckButton(false);
                        }}
                        buttonTextAfterSelection={selectedItem => {
                          return selectedItem;
                        }}
                        rowTextForSelection={item => {
                          return item;
                        }}
                      />
                    </View>
                    <View flDirectionRow mVertical6>
                      <TouchableOpacity
                        onPress={() => handleCreateUser(values)}
                        disabled={!isValid || !dirty || checkButton}
                        style={[
                          StaffScreenCss.btnConfirm,
                          {
                            backgroundColor:
                              !isValid || !dirty || checkButton
                                ? graycolor
                                : maincolor,
                          },
                        ]}>
                        <Text fontSize20 whiteColor fontWeight600>
                          {staffScreenAddStaff}
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        onPress={() => {
                          resetForm();
                          setModalVisible(!modalVisible);
                        }}
                        style={[
                          StaffScreenCss.btnConfirm,
                          {
                            backgroundColor: maincolor,
                          },
                        ]}>
                        <Text fontSize20 whiteColor fontWeight600>
                          {staffScreenCancelStaff}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </ScrollView>
                {isLoadingScreen ? (
                  <View
                    style={[
                      StaffScreenCss.viewLoading,
                      {
                        backgroundColor: modalbackground,
                      },
                    ]}>
                    <ActivityIndicator size="large" color={maincolor} />
                  </View>
                ) : null}
              </KeyboardAvoidingView>
            </Modal>
          )}
        </Formik>
      </View>

      {isLoadingScreen ? (
        <View
          style={[
            StaffScreenCss.viewLoading,
            {
              backgroundColor: modalbackground,
            },
          ]}>
          <ActivityIndicator size="large" color={maincolor} />
        </View>
      ) : null}
    </SafeAreaView>
  );
};

export default React.memo(StaffScreen);
