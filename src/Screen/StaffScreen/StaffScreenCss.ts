import { StyleSheet } from 'react-native';
import { scale, widthWindow } from '../../utils/style/Reponsive';
import { heightPercentageToDP as hp , widthPercentageToDP as wp} from 'react-native-responsive-screen'

export const StaffScreenCss = StyleSheet.create({
  container: {
    flex: 1,
    marginHorizontal: wp('1%'),
  },
  btnSettingMain: {
    height: scale(40),
  },

  btnSettingTextName: {
    width: '100%',
  },
  TextName: {
    paddingHorizontal: scale(6),
  },
  button: {
    height: scale(42),
    width: '100%',
    elevation: 2,
    justifyContent: 'center',
    marginHorizontal: scale(12),
    marginVertical: scale(4),
    borderRadius: scale(10),
  },

  modalTextHeader: {
    marginBottom: scale(15),
  },
  modalText: {
    marginBottom: 2,
  },
  modalTextInput: {
    borderWidth: scale(1),
    borderRadius: scale(5),
    height: scale(44),
    paddingLeft: scale(10),
    fontSize: scale(14),
  },

  view40Persent: {
    width: '40%',
  },
  view20Persent: {
    width: '18%',
  },
  viewRegisterStaff: {
    backgroundColor: 'white',
    borderRadius: scale(20),
    padding: scale(20),
    marginTop: widthWindow * 0.2,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    width: '100%',
  },
  formikPassword: {
    position: 'absolute',
    top: scale(28),
    right: 0,
    // backgroundColor: 'yellow',
    // height: '100%'
  },
  formikIconShowPassword: {
    marginHorizontal: scale(10),
    // backgroundColor: 'red'
  },
  viewSelectStatus: {
    width: '100%',
    borderWidth: 1,
    borderRadius: 8,
    height: scale(48),
  },

  btnConfirm: {
    width: '40%',
    height: scale(44),
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: scale(6),
    marginHorizontal: scale(20),
    borderRadius: scale(8),
  },

  viewLoading: {
    position: 'absolute',
    justifyContent: 'center',
    alignContent: 'center',
    top: -scale(20),
    bottom: 0,
    right: -scale(20),
    left: -scale(20),
    zIndex: 100,
  },
});
