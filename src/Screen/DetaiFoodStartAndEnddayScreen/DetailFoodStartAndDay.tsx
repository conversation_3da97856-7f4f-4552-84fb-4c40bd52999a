import React, {useState} from 'react';
import {
  Platform,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import Entypo from 'react-native-vector-icons/Entypo';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';

import {DetailFoodStartAndDayCSS} from './DetailFoodStartAndDayCss';
import {Text, View, Export, SendEmailOnIos} from '../../Component/index';
import {heightWindow} from '../../utils/style/Reponsive';
import {useAppSelector} from '../../hooks';
import {languages} from '../../constants';
interface IProps {
  navigation: {
    pop: (count?: number) => void;
  };
  route: any;
}
interface IStaffScreen {
  [key: string]: string | undefined;
  staffScreenCreateStaffs?: string;
  staffScreenNameStaff?: string;
  staffScreenAddressStaff?: string;
  staffScreenPhoneStaff?: string;
  staffScreenPasswordStaff?: string;
  staffScreenPasswordConfirmStaff?: string;
  staffScreenStatusStaff?: string;
  staffScreenAddStaff?: string;
  staffScreenCancelStaff?: string;
  staffScreenStatusStaffWork?: string;
  confirmPasswordMessage?: string;
  minValidMessageName?: string;
  maxValidMessageName?: string;
  minValidMessageAddress?: string;
  maxValidMessageAddress?: string;
  agree?: string;
  cancel?: string;
  minValidMessageNumberPhone?: string;
  maxValidMessageNumberPhone?: string;
  matchNumberPhone?: string;
  fieldIsBlank?: string;
  matchesNumberPhone?: string;
  minValidMessagePassword?: string;
  maxValidMessagePassword?: string;
}
const DetailFoodStartAndDay = (props: IProps) => {
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {maincolor, blackcolor} = xmlData;
  const {startDay, endDay, food, revenueDayByDay} = props.route.params;
  const {navigation} = props;
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const {totalDicount, totalSurcharge} = useAppSelector(state => state.counter);

  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): IStaffScreen | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: IStaffScreen = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    handleProductScreenName,
    tableScreenQuantityFood,
    homeScreenRevenue,
    detailStafScreenStaffTotalRevenue,
    detailFoodStartToEndFrom,
    detailFoodStartToEndTo,
    detailFoodStartToEndDetailRevenue,
    DiscountPrice,
    Surcharge,
  }: any = langData;
  const handleGoBack = () => {
    navigation.pop(1);
  };
  const currencySymbol = useAppSelector(state => state.counter?.infoAdmin?.admin?.store?.currency_symbol) || "đ";
  // handle sum total revenue
  const TotalPrice = food.reduce((cur: number, init: any) => {
    return (cur += init.total_price);
  }, 0);
  // handle sumTotalQuantity
  const TotalQuantity = food.reduce((cur: number, init: {quantity: number}) => {
    return (cur += init.quantity);
  }, 0);

  return (
    <SafeAreaView style={DetailFoodStartAndDayCSS.container}>
      <View w_100 mTop22>
        <View aItemsFlexEnd>
          <TouchableOpacity onPress={handleGoBack}>
            <Entypo name="log-out" size={40} color={maincolor} />
          </TouchableOpacity>
        </View>

        <View>
          <View>
            <Text fontSize16 textCenter fontWeight500 blackColor>
              {detailFoodStartToEndFrom} {startDay} {detailFoodStartToEndTo}{' '}
              {endDay}
            </Text>
            <Text fontSize28 textCenter fontWeight700 mainColor>
              {detailFoodStartToEndDetailRevenue}
            </Text>
          </View>
        </View>
      </View>

      {/*
      Content Detail Revenue
      */}

      <View w_100 mBottom10>
        <View>
          <View flDirectionRow jContentBetween>
            <View w_40>
              <Text marginVertical2 fontSize16 blackColor fontWeight600>
                {handleProductScreenName}
              </Text>
            </View>
            <View w_20 aItemsCenter>
              <Text marginVertical2 fontSize16 blackColor fontWeight600>
                {tableScreenQuantityFood}
              </Text>
            </View>
            <View w_40 aItemsFlexEnd>
              <Text marginVertical2 fontSize16 blackColor fontWeight600>
                {homeScreenRevenue}({currencySymbol})
              </Text>
            </View>
          </View>
        </View>
        <ScrollView
          style={{
            height: heightWindow * 0.5,
          }}
          showsVerticalScrollIndicator={false}>
          {food.map(
            (
              item: {
                title: string;
                quantity: string;
                totalPrice: number;
              },
              index: number,
            ) => {
              return (
                <View key={index} flDirectionRow jContentBetween w_100>
                  <View w_40 jContentCenter aItemsFlexStart>
                    <Text
                      marginVertical2
                      fontSize16
                      blackColor
                      fontWeight500
                      style={{paddingLeft: wp('1%')}}>
                      {item.title}
                    </Text>
                  </View>
                  <View w_20 aItemsCenter jContentCenter>
                    <Text marginVertical2 fontSize16 blackColor fontWeight500>
                      {item.quantity
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    </Text>
                  </View>
                  <View w_40 aItemsFlexEnd jContentCenter>
                    <Text
                      marginVertical2
                      fontSize16
                      blackColor
                      fontWeight500
                      style={{paddingRight: wp('1%')}}>
                      {Math.round(item.totalPrice)
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    </Text>
                  </View>
                </View>
              );
            },
          )}
        </ScrollView>

        <View w_100 flDirectionRow mTop8>
          <View
            w_40
            style={[
              DetailFoodStartAndDayCSS.quantityRevenue,
              {
                borderTopColor: blackcolor,
              },
            ]}>
            <Text
              fontSize18
              fontWeight600
              paddingTop8
              blackColor
              style={{paddingLeft: wp('1%')}}>
              {detailStafScreenStaffTotalRevenue}
            </Text>
          </View>
          <View
            w_20
            style={[
              DetailFoodStartAndDayCSS.quantityRevenue,
              {
                borderTopColor: blackcolor,
              },
            ]}>
            <View aItemsCenter>
              <Text fontSize18 fontWeight600 redText paddingTop8>
                {TotalQuantity.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              </Text>
            </View>
          </View>
          <View
            w_40
            style={[
              DetailFoodStartAndDayCSS.quantityRevenue,
              {
                borderTopColor: blackcolor,
              },
            ]}>
            <Text
              fontSize18
              fontWeight600
              mainColor
              textRight
              paddingTop8
              style={{paddingRight: wp('1%')}}>
              {revenueDayByDay.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}{' '}
              {/* {revenueDayByDay}{` `} */}
            </Text>
          </View>
        </View>
        {totalDicount ? (
          <View w_100 mTop6 flDirectionRow>
            <View w_40>
              <Text
                fontSize18
                fontWeight600
                blackColor
                style={{paddingLeft: wp('1%')}}>
                {Surcharge}
              </Text>
            </View>

            <View w_60>
              <Text
                fontSize18
                fontWeight600
                textRight
                style={{paddingRight: wp('1%')}}>
                {totalDicount}
              </Text>
            </View>
          </View>
        ) : <></>}
        {totalSurcharge ? (
          <View w_100 mTop6 flDirectionRow>
            <View w_40>
              <Text
                fontSize18
                fontWeight600
                blackColor
                style={{paddingLeft: wp('1%')}}>
                {DiscountPrice}
              </Text>
            </View>

            <View w_60>
              <Text
                fontSize18
                fontWeight600
                textRight
                style={{paddingRight: wp('1%')}}>
                {totalSurcharge}
              </Text>
            </View>
          </View>
        ) : <></>}
      </View>

      {/* {Platform.OS === 'android' ? (
        <Export
          dataFood={food}
          startDay={startDay}
          endDay={endDay}
          TotalPrice={TotalPrice}
        />
      ) : (
        <SendEmailOnIos
          dataFood={food}
          startDay={startDay}
          endDay={endDay}
          TotalPrice={TotalPrice}
        />
      )} */}
    </SafeAreaView>
  );
};

export default React.memo(DetailFoodStartAndDay);
