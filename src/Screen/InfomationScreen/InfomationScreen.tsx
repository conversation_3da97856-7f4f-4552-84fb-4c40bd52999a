import {
  Alert,
  Image,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  StatusBar,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {useDispatch} from 'react-redux';
import {
  Text,
  View,
  ModalAddImages,
  ModalFooterPrint,
} from '../../Component/index';

import {InfomationCss} from './InfomationScreenCss';
import {
  setFlagPrint,
  setShowListPrinter,
  showModalFooter,
} from '../../Redux/Slide';
import {updateFooterApp} from '../../api/handleApi';
import {heightWindow, scale} from '../../utils/style/Reponsive';
import {useAppSelector} from '../../hooks';
import {ThunkDispatch} from '@reduxjs/toolkit';
import {getFooterPrint} from '../../Redux/GetData';
import {languages} from '../../../src/constants';
import {scanLANForPrinters} from '../../utils/scanLANForPrinters';
import PrinterComponents from '../../../src/Component/PrinterComponents/PrinterComponents';
interface IPropsInfomationScreen {
  navigation: {
    pop: (count?: number) => void;
  };
}
interface HomeScreen {
  [key: string]: string | undefined;
  homeScreenCustomers?: string;
  homeScreenInfomation?: string;
  homeScreenStaff?: string;
  homeScreenTable?: string;
  homeScreenRevenue?: string;
  homeScreenProduct?: string;
  contact?: string;
  phoneContact?: string;
  homeScreenRevenueToday?: string;
  homeScreenOrdersNumbersToday?: string;
  homeScreenOrders?: string;
  homeScreenBestSalesToday?: string;
  homeScreenAllOrders?: string;
  homeScreenHighestSalesToday?: string;
  homeScreenDeleteAccount?: string;
  cancel?: string;
  agree?: string;
  homeScreenLogout?: string;
}
const InfomationScreen = (props: IPropsInfomationScreen) => {
  const {navigation} = props;
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {maincolor, blackcolor, graycolor, backgroundcolor} = xmlData;
  const {infoAdmin, modalFooter, access_token, flagFooter} = useAppSelector(
    state => state.counter,
  );
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const showListPrinter = useAppSelector(
    state => state.counter.showListPrinter,
  );
  console.log('showListPrinter', showListPrinter);
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): HomeScreen | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: HomeScreen = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    messageApp,
    componentCloseTitle,
    InfoScreenDetailStore,
    InfoScreenNameStore,
    InfoScreenNoName,
    InfoScreenNameBossStore,
    componentCustomersPhone,
    componentCustomersCreateDay,
    InfoScreenTraniner,
    InfoScreenExpired,
    InfoScreenQtiStaff,
    InfoScreenAddress,
    InfoScreenUpdateFooter,
    InfoScreenCreateFooter,
    InfoScreenUpdateImageFooter,
    InfoScreenContent,
    updateFooterSuccess,
    updateFooterFail,
  }: any = langData;
  const extractDateTime = (created_at: Date) => {
    const dateTime = new Date(created_at);

    const day = dateTime.getDate().toString().padStart(2, '0');
    const month = (dateTime.getMonth() + 1).toString().padStart(2, '0');
    const year = dateTime.getFullYear();
    const vietnameseDateTime = `${day}-${month}-${year}`;
    return vietnameseDateTime;
  };

  const DayRegistry: any = extractDateTime(infoAdmin.admin.created_at);
  const DayExpires: any = extractDateTime(infoAdmin.admin.expiry_date);
  const {listStaff, footerPrinter, idFooter, urlLogo} = useAppSelector(
    state => state.getData,
  );

  const [footerPrint, setFooterPrint] = useState<string | undefined>(
    footerPrinter,
  );
  const [buttonEditFooter, setButtonEditFooter] = useState<boolean>(true);
  const [initialLoad, setInitialLoad] = useState(true);
  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const handleShowInfomation = () => {
    dispatch(showModalFooter({valid: false, modalFooter: 'FooterPrint'}));
  };
  const handleShowAddImage = () => {
    dispatch(showModalFooter({valid: false, modalFooter: 'LogoStore'}));
  };
  const updateFooter = async () => {
    updateFooterApp(idFooter, footerPrint, access_token)
      .then(res => {
        if (res.data.status_code === 200) {
          setButtonEditFooter(true);
          dispatch(setFlagPrint());
          Alert.alert(messageApp, updateFooterSuccess);
        } else {
          Alert.alert(messageApp, updateFooterFail);
        }
      })
      .catch((err: string) => {
        console.log(err);
      });
  };

  const handleGoBack = () => {
    navigation.pop(1);
  };

  const checkLan = async () => {
      dispatch(setShowListPrinter(true));
  
  };

  useEffect(() => {
    const requestOptions = {access_token, messageApp};
    dispatch(getFooterPrint(requestOptions));
  }, []);
  useEffect(() => {
    const requestOptions = {access_token, messageApp};
    dispatch(getFooterPrint(requestOptions));
  }, [flagFooter]);

  return (
    <SafeAreaView
      style={{
        flex: 1,
      }}>
      <StatusBar />
      <KeyboardAvoidingView
        style={[InfomationCss.container]}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <View
          style={{
            height: '100%',
            marginHorizontal: scale(6),
          }}>
          <View
            flDirectionRow
            style={InfomationCss.titlePayment}
            accessible={false}
            jContentBetween>
            <View>
              <Text fontSize28 textCenter fontWeight700 mainColor>
                {InfoScreenDetailStore}
              </Text>
            </View>
            <View>
              {
              showListPrinter ? 
              <></> : 
                            <TouchableOpacity
                onPress={handleGoBack}
                style={[
                  InfomationCss.btnClose,
                  {
                    backgroundColor: maincolor,
                  },
                ]}>
                <Text
                  whiteColor
                  fontSize20
                  fontWeight600
                  textCenter
                  style={{
                    paddingHorizontal: scale(20),
                  }}>
                  {componentCloseTitle}
                </Text>
              </TouchableOpacity> 
              }

            </View>
          </View>

          <ScrollView
            showsVerticalScrollIndicator={false}
            style={{
              height: '100%',
            }}>
            <View flDirectionRow mVertical2>
              <View w_50>
                <Text fontSize18 blackColor>
                  {InfoScreenNameStore}:
                </Text>
              </View>
              <View aItemsFlexEnd w_45>
                <Text fontSize18 blackColor>
                  {infoAdmin?.admin.name === 'null'
                    ? InfoScreenNoName
                    : infoAdmin?.admin.name}
                </Text>
              </View>
            </View>

            <View flDirectionRow>
              <View w_50>
                <Text fontSize18 blackColor>
                  {InfoScreenNameBossStore}:
                </Text>
              </View>
              <View aItemsFlexEnd w_45>
                <Text fontSize18 blackColor>
                  {infoAdmin.admin.storename == null
                    ? InfoScreenNoName
                    : infoAdmin.admin.storename}
                </Text>
              </View>
            </View>

            <View flDirectionRow>
              <View w_55>
                <Text fontSize18 blackColor>
                  {componentCustomersPhone}:
                </Text>
              </View>
              <View aItemsFlexEnd w_40>
                <Text fontSize18 blackColor>
                  {infoAdmin?.admin.phone}
                </Text>
              </View>
            </View>

            <View flDirectionRow>
              <View w_40>
                <Text fontSize18 blackColor>
                  {componentCustomersCreateDay}:
                </Text>
              </View>
              <View w_55 aItemsFlexEnd>
                <Text fontSize18 blackColor>
                  {DayRegistry}
                </Text>
              </View>
            </View>

            <View flDirectionRow>
              <View w_55>
                <Text fontSize18 blackColor>
                  {InfoScreenExpired}:
                </Text>
              </View>
              <View aItemsFlexEnd w_40>
                <Text fontSize18 blackColor>
                  {DayExpires === `01-01-1970`
                    ? InfoScreenTraniner
                    : DayExpires}
                </Text>
              </View>
            </View>

            <View flDirectionRow>
              <View w_50>
                <Text fontSize18 blackColor>
                  {InfoScreenQtiStaff}:
                </Text>
              </View>
              <View aItemsFlexEnd w_45>
                <Text fontSize18 blackColor>
                  {listStaff.length > 0 ? listStaff.length : 0}
                </Text>
              </View>
            </View>

            <View flDirectionRow>
              <View w_40>
                <Text fontSize18 blackColor>
                  {InfoScreenAddress}:
                </Text>
              </View>
              <View aItemsFlexEnd w_55>
                <Text fontSize18 blackColor>
                  {infoAdmin.admin.address}
                </Text>
              </View>
            </View>

            <View flDirectionRow>
              <View w_55>
                <Text fontSize18 blackColor>
                  Kiểm tra kết nối máy in:
                </Text>
              </View>
              <View w_45>
                <View>
                  <TouchableOpacity
                    onPress={checkLan}
                    style={[
                      InfomationCss.btnClose,
                      {
                        backgroundColor: maincolor ,
                        borderRadius: scale(0),
                      },
                    ]}>
                    <Text whiteColor fontSize20 fontWeight600 textCenter>
                      Kiểm tra
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>

            <View>
              <View>
                <Text fontSize18 blackColor>
                  {InfoScreenContent}:
                </Text>
              </View>
              <View>
                {footerPrinter !== '' ? (
                  <View>
                    <View style={InfomationCss.imageView}>
                      <Image
                        source={{
                          uri: urlLogo,
                        }}
                        style={{
                          width: '100%',
                          height: urlLogo === null ? 0 : heightWindow * 0.22,
                        }}
                        resizeMode="contain"
                      />
                    </View>
                  </View>
                ) : null}

                {footerPrinter !== '' ? (
                  <TextInput
                    style={[
                      InfomationCss.inputContent,
                      {
                        color: blackcolor,
                      },
                    ]}
                    defaultValue={footerPrinter}
                    onChangeText={e => {
                      if (e !== footerPrint) {
                        setFooterPrint(e);
                        setButtonEditFooter(false);
                        setInitialLoad(false);
                        // initialLoad ? setButtonEditFooter(true) : '';
                      }
                    }}
                    multiline={true}
                    inputMode="text"
                    textAlignVertical="top"
                  />
                ) : null}
              </View>
            </View>
            <View
              flDirectionRow
              h_60
              jContentBetween
              style={{
                ...Platform.select({
                  ios: {
                    marginBottom: scale(0),
                  },
                  android: {
                    marginTop: scale(5),
                    marginBottom: scale(20),
                  },
                }),
              }}>
              {footerPrinter !== '' ? (
                <TouchableOpacity
                  style={[
                    InfomationCss.footerBtn,
                    {
                      backgroundColor: maincolor,
                    },
                    {
                      backgroundColor: buttonEditFooter ? graycolor : maincolor,
                    },
                  ]}
                  onPress={updateFooter}
                  disabled={buttonEditFooter}>
                  <Text fontSize18 fontWeight600 textCenter whiteColor>
                    {InfoScreenUpdateFooter}
                  </Text>
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  style={[
                    InfomationCss.footerBtn,
                    {
                      backgroundColor: maincolor,
                    },
                  ]}
                  onPress={handleShowInfomation}>
                  <Text fontSize18 fontWeight600 textCenter whiteColor>
                    {InfoScreenCreateFooter}
                  </Text>
                </TouchableOpacity>
              )}

              {footerPrinter !== '' ? (
                <TouchableOpacity
                  style={[
                    InfomationCss.footerBtn,
                    {
                      backgroundColor: maincolor,
                    },
                  ]}
                  onPress={handleShowAddImage}>
                  <Text fontSize18 fontWeight600 textCenter whiteColor>
                    {InfoScreenUpdateImageFooter}
                  </Text>
                </TouchableOpacity>
              ) : null}
            </View>
          </ScrollView>
        </View>
      </KeyboardAvoidingView>
      {modalFooter === 'FooterPrint' ? (
        <ModalFooterPrint navigation={navigation} />
      ) : null}
      {modalFooter === 'LogoStore' ? <ModalAddImages /> : null}
      {showListPrinter ? <PrinterComponents /> : null}
    </SafeAreaView>
  );
};

export default React.memo(InfomationScreen);
