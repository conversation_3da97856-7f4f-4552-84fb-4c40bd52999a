import { Platform, StyleSheet } from 'react-native';
import { heightWindow, scale } from '../../utils/style/Reponsive';

export const InfomationCss = StyleSheet.create({
  container: {
    flex: 1,
  },
  titlePayment: {
    marginTop: scale(10),

    // ...Platform.select({
    //     ios: {
    //     }, android: {
    //         marginTop: scale(10),
    //     }
    // }),
  },
  btnClose: {
    height: scale(40),
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: scale(5),
    width: '100%',
  },
  view50Percentage: {
    width: '100%',
  },
  imageView: {
    alignItems: 'center',
  },
  inputContent: {
    width: '100%',
    height: heightWindow * 0.25,
    fontSize: scale(18),
    borderWidth: 1,
    ...Platform.select({
      ios: {
        padding: scale(10),
      },
    }),
    borderRadius: scale(8),
    marginVertical: scale(6),
  },
  footerBtn: {
    borderRadius: scale(8),
    justifyContent: 'center',
    width: '48%',
  },
});
