import {ReactN<PERSON>, createContext, useEffect, useState} from 'react';
import {Alert} from 'react-native';

import AsyncStorage from '@react-native-async-storage/async-storage';

import {Login} from '../api/handleApi';
import {useAppSelector} from '../hooks';
import {handleGetXml, setResetPrint, setResetToken} from '../Redux/GetData';
import {useDispatch} from 'react-redux';
import {ThunkDispatch} from '@reduxjs/toolkit';
import {setAdminInfo, setResetInfoAdmin, setTokenAdmin} from '../Redux/Slide';
import {languages} from '../../src/constants';

export const AuthContext = createContext<any>(null);
interface IProps {
  children: ReactNode;
}
interface IAuthContext {
  [key: string]: string | undefined;
  tokenExpiresed?: string;
  messageApp?: string;
  fillInBlank?: string;
  loginFail?: string;
  networkError?: string;
}
export const AuthProvider = (props: IProps) => {
  const {children} = props;
  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [userInfo, setUserInfo] = useState<{}>({});
  const [splashLoading, setSplashLoading] = useState<boolean>(false);
  const checkTokenApp: number =
    useAppSelector(state => state.getData.checktoken) || 0;
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): IAuthContext | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: IAuthContext = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    tokenExpiresed,
    messageApp,
    fillInBlank,
    loginFail,
    networkError,messLoginFail401,messLoginFail204
  }: any = langData;

  //handle login
  const login = async (phone: string, password: string) => {
    setIsLoading(true);
    try {
      const res = await Login(phone, password);
      const result = res.data.status_code;
      let logoApp = res.data.admin?.image; // Add null check here
      switch (result) {
        case 200:
          let userInfo = res.data;
          let access_token = res.data.access_token;
          setUserInfo(userInfo);
          const storagePromises = [
            AsyncStorage.setItem('userInfo', JSON.stringify(userInfo)),
            AsyncStorage.setItem('access_token', JSON.stringify(access_token)),
            AsyncStorage.setItem('phone', JSON.stringify(phone)),
            AsyncStorage.setItem('password', JSON.stringify(password)),
            AsyncStorage.setItem('isCheckLang', JSON.stringify(isCheckLang)),
            AsyncStorage.setItem('LogoApp', JSON.stringify(logoApp)),
          ];
          await Promise.all(storagePromises);
          dispatch(setAdminInfo(userInfo));
          dispatch(setTokenAdmin(res.data.access_token));
          break;
        case 500:
          Alert.alert(messageApp, fillInBlank);
          break;
        case 400:
          Alert.alert(messageApp, loginFail);
          break;
        case 204:
          Alert.alert(messageApp, messLoginFail204);
          break;
        case 401:
          Alert.alert(messageApp, messLoginFail401);
          break;
        default:
          Alert.alert(messageApp, res.data.message);
          break;
      }
    } catch (err: unknown) {
      Alert.alert(messageApp, networkError);
    } finally {
      setIsLoading(false);
    }
  };

  //handle logout
  const logout = async () => {
    setIsLoading(true);
    try {
      await Promise.all([
        AsyncStorage.removeItem('userInfo'),
        AsyncStorage.removeItem('access_token'),
        AsyncStorage.removeItem('LogoApp'),
        AsyncStorage.removeItem('password'),
        AsyncStorage.removeItem('phone'),
        AsyncStorage.removeItem('isCheckLang'),
      ]);
      setUserInfo({});
    } catch (err: unknown) {
      console.log(err);
    } finally {
      setIsLoading(false);
    }
  };

  // handle check login
  useEffect(() => {
    const checkLoginStatus = async () => {
      try {
        setSplashLoading(true);
        const userInfo = await AsyncStorage.getItem('userInfo');
        const phone: string | null = await AsyncStorage.getItem('phone');
        const password: string | null = await AsyncStorage.getItem('password');
        const logoApp: string | null = await AsyncStorage.getItem('LogoApp');
        const access_token: string | null = await AsyncStorage.getItem(
          'access_token',
        );

        let newPhone = phone !== null ? phone.slice(1, -1) : '';
        let newPass = password !== null ? password.slice(1, -1) : '';
        Login(newPhone, newPass).then(res => {
          res.data.status_code === 200 ? '' : logout();
          if (userInfo) {
            dispatch(setTokenAdmin(access_token));
            setUserInfo(JSON.parse(userInfo));
          }
        });
        setSplashLoading(false);
      } catch (err: unknown) {
        setSplashLoading(false);
        console.log(err);
      }
    };
    dispatch(handleGetXml());
    checkLoginStatus();
  }, []);

  // handle check token expired
  const handleCheckToken = () => {
    if (checkTokenApp === 406 || checkTokenApp === 405) {
      logout();
      Alert.alert(messageApp, tokenExpiresed);
      dispatch(setResetToken());
      dispatch(setResetPrint());
      dispatch(setResetInfoAdmin());
    }
  };

  useEffect(() => {
    if (checkTokenApp === 406 || checkTokenApp === 405) {
      handleCheckToken();
    }
  }, [checkTokenApp, handleCheckToken]);

  return (
    <AuthContext.Provider
      value={{
        isLoading,
        userInfo,
        login,
        logout,
        splashLoading,
      }}>
      {children}
    </AuthContext.Provider>
  );
};
