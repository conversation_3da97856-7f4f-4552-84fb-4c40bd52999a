import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { BlogState, rootInterface } from '../interface';
import { set } from 'lodash';

const initialState: BlogState['counter'] = {
  editItem: true,
  isLoading: true,
  isError: true,
  showModalWeek: false,
  isLoadingScreen: false,
  flag: true,
  select: false,
  showOpition: false,
  loading: false,
  nameSelectOpition: '',
  access_token: '',
  infoAdmin: [
    {
      store: {
        id: 0,
        name: '',
        address: '',
        phone: '',
        email: '',
        currency: 'đ',
        is_tax_included: 0,
        tax_rate: 0,
      },
    }
  ],
  showModalFooter: false,
  modalFooter: '',
  checkCreate: false,
  urlLogo: '',
  flagTable: true,
  flagStaff: true,
  flagProduct: true,
  flagChart: false,
  flagWeek: false,
  flagMonth: false,
  flagPrint: false,
  flagCustomers: false,
  flagListCustomers: false,
  nameCustomer: '',
  splashLoading: false,
  flagFooter: false,
  totalDicount: 0,
  totalSurcharge : 0,
  showListPrinter:false,
};

export const counterSlice = createSlice({
  name: 'counter',
  initialState,
  reducers: {
    showModalFooter: (
      state,
      action: PayloadAction<rootInterface['modalFooter']>,
    ) => {
      state.showModalFooter = action.payload.valid;
      state.modalFooter = action.payload.modalFooter;
    },
    checkCreate: (
      state,
      action: PayloadAction<rootInterface['checkCreate']>,
    ) => {
      state.checkCreate = action.payload.valid;
    },
    setStateEdit: (
      state,
      action: PayloadAction<rootInterface['stateEdit']>,
    ) => {
      state.editItem = action.payload.valid;
      state.nameSelectOpition = action.payload.nameOpiton;
    },
    setStateShowOption: (
      state,
      action: PayloadAction<rootInterface['stateShowOption']>,
    ) => {
      state.showOpition = action.payload.valid;
      state.nameSelectOpition = action.payload.nameOpiton;
    },
    setShowRevenueWeeks: (
      state,
      action: PayloadAction<rootInterface['showRevenueWeeks']>,
    ) => {
      state.showModalWeek = action.payload.valid;
      state.nameSelectOpition = action.payload.nameOpiton;
    },
    setShowRevenueMonth: (
      state,
      action: PayloadAction<rootInterface['validRevenueMonth']>,
    ) => {
      state.showOpition = action.payload.valid;
      state.nameSelectOpition = action.payload.nameOpiton;
    },
    setShowRevenueDayByDay: (
      state,
      action: PayloadAction<rootInterface['RevenueDayByDay']>,
    ) => {
      state.showOpition = action.payload.valid;
      state.nameSelectOpition = action.payload.nameOpiton;
    },
    setAdminInfo: (state, action) => {
      state.infoAdmin = action.payload;
    },
    setTokenAdmin: (state, action) => {
      state.access_token = action.payload
    },
    setFlag: state => {
      state.flag = !state.flag;
    },
    setLoading: (state, action) => {
      state.loading = action.payload.valid;
    },
    setLoadingScreen: (
      state,
      action,
    ) => {
      state.isLoadingScreen = action.payload.valid;
    },
    setUpdateInfoAdmin: (
      state,
      action
    ) => {
      state.infoAdmin = action.payload.valid;
    },
    setUpdateLogo: (state, action: PayloadAction<string>) => {
      state.urlLogo = action.payload;
    },
    setFlagTable: state => {
      state.flagTable = !state.flagTable;
    },
    setFlagStaff: state => {
      state.flagStaff = !state.flagStaff;
    },
    setFlagProduct: state => {
      state.flagProduct = !state.flagProduct;
    },
    setFlagChart: state => {
      state.flagChart = !state.flagChart;
    },
    setFlagWeek: state => {
      state.flagWeek = !state.flagWeek;
    },
    setFlagMonth: state => {
      state.flagMonth = !state.flagMonth;
    },
    setFlagPrint: state => {
      state.flagPrint = !state.flagPrint;
    },
    setFlagCustomers: state => {
      state.flagCustomers = !state.flagCustomers;
    },
    setShowCreateCustomers: (
      state,
      action: PayloadAction<rootInterface['showCreateCustomers']>,
    ) => {
      state.showOpition = action.payload.valid;
      state.nameCustomer = action.payload.nameOpiton;
    },
    getCustomers: state => {
      state.flagListCustomers = !state.flagListCustomers;
    },
    setResetInfoAdmin: state => {
      state.infoAdmin = [];
    },
    setFlagFooter: state => {
      state.flagFooter = !state.flagFooter;
    },
    setTotalDiscount: (state, action: PayloadAction<number>) => {
      state.totalDicount = action.payload;
    },
    setTotalSurcharge: (state, action: PayloadAction<number>) => {
      state.totalSurcharge = action.payload;
    },
    resetState: () => {
      return initialState;
    },
    setShowListPrinter: (state, action: PayloadAction<boolean>) => {
      state.showListPrinter = action.payload;
    },
  },
});

export const {
  setStateEdit,
  setStateShowOption,
  setAdminInfo,
  setFlag,
  setShowRevenueWeeks,
  setShowRevenueMonth,
  setShowRevenueDayByDay,
  setTokenAdmin,
  setLoading,
  setLoadingScreen,
  showModalFooter,
  checkCreate,
  setUpdateInfoAdmin,
  setUpdateLogo,
  setFlagTable,
  setFlagStaff,
  setFlagProduct,
  setFlagChart,
  setFlagWeek,
  setFlagMonth,
  setFlagPrint,
  setShowCreateCustomers,
  setFlagCustomers,
  getCustomers,
  setResetInfoAdmin,
  setFlagFooter,
  resetState,
  setTotalDiscount,
  setTotalSurcharge,
  setShowListPrinter
} = counterSlice.actions;

export default counterSlice.reducer;
