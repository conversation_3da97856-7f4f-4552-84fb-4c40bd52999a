import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { Alert, LogBox } from 'react-native';
import axios from 'axios';
import { BASE_URL } from '../api/ApiManager';
import { createContact } from '../api/handleApi';
import { DefaultLangue } from '../constants/index'
import { parse } from 'fast-xml-parser';
import { BlogState } from '../interface';
import { toNonAccentVietnamese } from '../utils/style/Reponsive';

LogBox.ignoreLogs(['Warning: ...']); // Ignore log notification by message
LogBox.ignoreAllLogs(); //Ignore all log notifications
const initialState: BlogState['getData'] = {
  isCheckLang: 0,
  isLoadingLogin: false,
  isErrorLogin: false,
  revenueToday: 0,
  revenueOptionalDate: 0,
  revenueStartAndEndDay: 0,
  revenueLastday: 0,
  revenueLast2day: 0,
  revenueLast3day: 0,
  revenueLast4day: 0,
  revenueLast5day: 0,
  revenueLast6day: 0,
  revenueOnWeek: 0,
  revenueFirstWeek: 0,
  revenueSecondWeek: 0,
  revenueThirdWeek: 0,
  revenueFourthWeek: 0,
  revenueFifthWeek: 0,
  revenueOnMouth: 0,
  revenueLastMouth: 0,
  isLoading: false,
  isError: false,
  isLoadingTable: false,
  isErrorTable: false,
  categoryName: '',
  paymentToday: [],
  listCategory: [],
  listProduct: [],
  listTable: [],
  listStaff: [],
  userInfo: {},
  listInventory: [],
  showOpition: false,
  footerPrinter: '',
  idFooter: 0,
  // idFooterconfirm: 0,
  urlLogo: '',
  access_token: '',
  date_end: '',
  date_start: '',
  id: 0,
  user_id: 0,
  name: '',
  address: '',
  phone: 0,
  password: '',
  status: 0,
  table: '',
  idCategory: 0,
  changeValue: '',
  date: '',
  dateLast6Day: '',
  dateLast5Day: '',
  dateLast4Day: '',
  dateLast3Day: '',
  dateLast2Day: '',
  dateLastDay: '',
  numberphone: 0,
  infoContact: '',
  checktoken: 0,
  listSubProduct: [],
  messageApp: '',
  pageCurrent: 1, hasMoreData: true, checkLoading: true, isFirstLoad: true,
  hasFetchedAllData: undefined,
  isLoadingInventory: false,
  resetProductName: false,
  listAgencies: [],
  listRecept: [],
  isLoadingRecept: false,
  pageCurrentRecept: 1,
  checkTokenAccess: 0,
  
  xmlData: {
    screen: {
      backgroundcolor: '#FFFFFF',
      blackcolor: '#000000',
      diamondmember: '#66CDAA',
      goldmember: '#EEAD0E',
      graycolor: '#808080',
      maincolor: '#FF6D22',
      modalbackground: 'RGBA(0,0,0,0.5)',
      platiummember: '#DA70D6',
      sivermember: '#A0A0A0',
      useroffline: '#FF6347',
      useronline: '#00FA9A',
      userstop: '#DCDCDC',
      matchNumberPhone: `/^(84|0[1|2|3|4|5|6|7|8|9])+([0-9]{8})\b/g`,
      fieldIsBlank: 'Không được bỏ trống',
      minValidNumber: 4,
      maxValidNumberPhone: 11,
      maxValidNumber: 40,
      minValidPassword: 4,
      maxValidPassword: 20,
      maxValidSendInfo: 200,
      minValidMessageName: 'Họ và tên lớn hơn 4 kí tự',
      createCustomersFail: 'Gửi thông tin liên hệ thất bại',
      maxValidMessageName: 'Họ và tên lớn hơn 40 kí tự',
      minValidMessageAddress: 'Địa chỉ lớn hơn 4 kí tự',
      maxValidMessageAddress: 'Địa chỉ bé hơn 40 kí tự',
      minValidMessageNumberPhone: 'Số điện thoại quá ngắn',
      maxValidMessageNumberPhone: 'Số điện thoại quá dài',
      matchesNumberPhone: 'Nhập đúng định dạng số điện thoại',
      minValidMessagePassword: 'Mật khẩu quá ngắn',
      maxValidMessagePassword: 'Mật khẩu quá dài',
      confirmPasswordMessage: 'Mật khẩu xác thực phải giống với mật khẩu',
      minValidMessagePSendInfo: 'Nội dung liên lạc quá ngắn',
      maxValidMessagePSendInfo: 'Nội dung liên lạc quá dài',
      createCustomersSuccess: 'Thêm khách hàng thành công',
      createCustomersNumberPhoneExist: 'Số điện thoại khách hàng đã tồn tại',
      createCustomerNameFillInBlank: 'Vui lòng điền tên khách hàng',
      vietVangPhoneNumber: '02862651411',
      messageApp: 'Thông báo',
      cancel: 'Huỷ bỏ',
      agree: 'Đồng ý',
      networkIsError: 'Có lỗi xảy ra ',
      loginFail: 'Sai mật khẩu hoặc tài khoản',
      fillInBlank: 'Không đc bỏ trống',
      networkError: 'Không có mạng vui lòng đăng nhập lại sau',
      messageDisable: 'Gửi yêu cầu xoá tài khoản admin thành công . Bạn đã bị đăng xuất',
      addCategorySuccess: 'Thêm danh mục thành công',
      fillInBlankCategory: 'Chưa điền tên danh mục',
      categoryNameExist: 'Tên danh mục đã tồn tại',
      addCategoryFail: 'Lỗi thêm danh mục',
      editCategoryFail: 'Cập nhật danh mục không thành công',
      addCategoryExistButDelete: 'Danh mục này đã tồn tại và hiện tại đang bị xóa bạn có muốn khôi phục',
      deleteCategorySuccess: 'Xoá danh mục thành công',
      categoryNameNotValid: 'Danh mục không hợp lệ',
      deleteCategoryFail: 'Xoá danh mục không thành công',
      editCategorySuccess: 'Cập nhật danh mục thành công',
      editCategoryBlank: 'Không được bỏ trống tên danh mục',
      addProductSuccess: 'Thêm sản phẩm thành công',
      productNameExist: 'Tên sản phẩm đã tồn tại',
      notFullValid: 'Chưa điền đầy đủ thông tin',
      findNotCategory: 'Không tìm thấy danh mục',
      notSelectedCategory: 'Vui lòng thêm danh mục',
      addProductFail: 'Tạo sản phẩm thất bại',
      alertVat: 'Nhập VAT từ 0 đến 100',
      addProductExistButDeleted: 'Sản phẩm này đã tồn tại và hiện tại đang bị xóa bạn có muốn khôi phục',
      deleteProductSuccess: 'Xoá sản phẩm thành công',
      deleteProductFail: 'Có lỗi xảy ra vui lòng thử lại sau',
      editProductSuccess: 'Cập nhật thông tin sản phẩm thành công',
      editNotFullValid: 'Chưa điền đầy đủ hoặc sai thông tin',
      editProductFail: 'Cập nhật thông tin sản phẩm thất bại',
      sendInfoSuccess: 'Cảm ơn bạn đã gửi thông tin. Chúng tôi sẽ liên lạc lại sau',
      sendInfoFail: 'Gửi thông tin thất bại, không được bỏ trống',
      addStaffSuccess: 'Thêm nhân viên thành công',
      addStaffFaill: 'Thêm nhân viên thất bại',
      infoStaffExist: 'Số điện thoại này đã được đăng ký vui lòng đăng ký số điện thoại khác',
      staffExistButDeleted: 'Thông tin nhân viên đã tồn tại nhưng hiện tại đã bị xoá bạn có muốn khôi phục',
      notFullValidStaff: 'Không được bỏ trống thông tin',
      notFullValidAddress: 'Không được bỏ trống địa chỉ',
      notFullValidNameStaff: 'Không được bỏ trống tên nhân viên',
      changeInfoStaffSuccess: 'Cập nhật thông tin nhân viên thành công',
      phoneNumberExist: 'Số điện thoại đã tồn tại',
      phoneNumberNotValid: 'Số điện thoại phải là 10 số',
      changeInfoStaffFail: 'Có lỗi xảy ra vui lòng thử lại sau',
      deleteStaffSuccess: 'Xoá nhân viên thành công',
      deleteStaffFail: 'Nhân viên đang trực tuyến không thể xoá. Vui lòng đổi thành offline để xoá',
      changePasswordSuccess: 'Thay đổi mật khẩu nhân viên thành công',
      changePasswordFail: 'Thay đổi mật khẩu nhân viên thất bại',
      addTableSuccess: 'Thêm bàn thành công',
      addTableExist: 'Bàn đã tồn tại',
      addTableExistButDeleted: 'Bàn này đã tồn tại và hiện tại đang bị xóa bạn có muốn khôi phục lại',
      addTableFail: 'Thêm bàn thất bại',
      cancelOrderSuccess: 'Huỷ bàn thành công',
      cancelOrderFail: 'Huỷ bàn thất bại',
      deleteTableSuccess: 'Xoá bàn thành công',
      deleteTableFail: 'Xoá bàn thất bại',
      deleteMutilTableSuccess: 'Xoá các bàn thành công',
      doNotChooseTableDelete: 'Chưa chọn bàn để xoá',
      deleteMutilTableFail: 'Xoá các bàn thất bại',
    },
    loginscreen: {
      nameApp: 'Delta POS',
      phoneContact: '02862651411',
      email: '<EMAIL>',
    }, Viet: {
      titleApp: ''
    }
  },

};

const createAxiosConfig = (access_token: string | undefined) => {
  return {
    headers: {
      Authorization: `Bearer ${access_token}`,
    },
    timeout: 10000,
  };
};
export const handleGetXml = createAsyncThunk('admin/handleGetXml', async () => {
  try {
    let obj: any = {};
    const response = await axios.get(
      `${BASE_URL}/storage/admin_public_vi.xml`
      // `http://localhost:8000/admin_public_vi.xml`
    );
    const xmlData = response.data;
    obj = parse(xmlData);
    return obj.lang;
  } catch (error) {
    return DefaultLangue
  }
});

export const handleSendInfoContact = createAsyncThunk(
  'admin/handleSendInfoContact',
  async (action: BlogState['getData']) => {
    const {
      name,
      phone,
      address,
      infoContact,
      messageApp,
      sendInfoSuccess,
      sendInfoFail,
      createCustomersFail,
    } = action;
    try {
      if (
        typeof name !== 'undefined' &&
        typeof phone !== 'undefined' &&
        typeof address !== 'undefined' &&
        typeof infoContact !== 'undefined'
      ) {
        createContact(name, phone, address, infoContact).then(res => {
          if (res.data.status_code === 200) {
            Alert.alert(messageApp, sendInfoSuccess);
          } else {
            Alert.alert(messageApp, createCustomersFail);
          }
        });
      }
    } catch (error) {
      Alert.alert(messageApp, sendInfoFail);
    }
  },
);

export const fetchRevenueToDay = createAsyncThunk(
  'admin/getRevenue',
  async (action: BlogState['getData']) => {
    const { access_token } = action;
    try {
      const res = await axios.post(
        `${BASE_URL}/api/admin/revenue/get_revenue`,
        {},
        createAxiosConfig(access_token),
      );
      if (res.data.status_code === 200) {
        return Math.floor(res?.data?.revenue)
        // } else {
        //   return res?.data?.status_code;
      }
    } catch (err: unknown) {
      console.log(err);
    }
  },
);


export const checkToken = createAsyncThunk(
  'admin/checkToken',
  async (action: BlogState['getData']) => {
    const { access_token } = action;

    try {
      const res = await axios.post(
        `${BASE_URL}/api/admin/revenue/get_revenue`,
        {},
        createAxiosConfig(access_token)
      );
      if (res.data.status_code !== 200) {
        return res?.data?.status_code;
      }
    } catch (err: unknown) {
      console.log(err);
    }
  },
);

export const fetchRevenueLastDay = createAsyncThunk(
  'admin/fetchRevenueLastDay',
  async (action: BlogState['getData']) => {
    const { access_token, dateLastDay } = action;
    try {

      const res = await axios.post(
        `${BASE_URL}/api/admin/revenue/get_revenue_by_date`,
        {
          date: dateLastDay,
        },
        createAxiosConfig(access_token)
      );
      if (res.data.status_code === 200) {
        return Math.floor(res?.data?.revenue)
      }
    } catch (err: unknown) {
      console.log(err);
    }
  },
);


export const fetchRevenueLast2Day = createAsyncThunk(
  'admin/fetchRevenueLast2Day',
  async (action: BlogState['getData']) => {
    const { access_token, dateLast2Day } = action;
    try {

      const res = await axios.post(
        `${BASE_URL}/api/admin/revenue/get_revenue_by_date`,
        {
          date: dateLast2Day,
        },
        createAxiosConfig(access_token),
      );
      if (res.data.status_code === 200) {
        return Math.floor(res?.data?.revenue)
      }
    } catch (err: unknown) {
      console.log(err);
    }
  },
);


export const fetchRevenueLast3Day = createAsyncThunk(
  'admin/fetchRevenueLast3Day',
  async (action: BlogState['getData']) => {
    const { access_token, dateLast3Day } = action;
    try {

      const res = await axios.post(
        `${BASE_URL}/api/admin/revenue/get_revenue_by_date`,
        {
          date: dateLast3Day,
        },
        createAxiosConfig(access_token),
      );
      if (res.data.status_code === 200) {
        return Math.floor(res?.data?.revenue)
      }
    } catch (err: unknown) {
      console.log(err);
    }
  },
);


export const fetchRevenueLast4Day = createAsyncThunk(
  'admin/fetchRevenueLast4Day',
  async (action: BlogState['getData']) => {
    const { access_token, dateLast4Day } = action;
    try {

      const res = await axios.post(
        `${BASE_URL}/api/admin/revenue/get_revenue_by_date`,
        {
          date: dateLast4Day,
        },
        createAxiosConfig(access_token),
      );
      if (res.data.status_code === 200) {
        return Math.floor(res?.data?.revenue)
      }
    } catch (err: unknown) {
      console.log(err);
    }
  },
);

export const fetchRevenueLast5Day = createAsyncThunk(
  'admin/fetchRevenueLast5Day',
  async (action: BlogState['getData']) => {
    const { access_token, dateLast5Day } = action;
    try {

      const res = await axios.post(
        `${BASE_URL}/api/admin/revenue/get_revenue_by_date`,
        {
          date: dateLast5Day,
        },
        createAxiosConfig(access_token),
      );
      if (res.data.status_code === 200) {
        return Math.floor(res?.data?.revenue)
      }
    } catch (err: unknown) {
      console.log(err);
    }
  },
);

export const fetchRevenueLast6Day = createAsyncThunk(
  'admin/fetchRevenueLast6Day',
  async (action: BlogState['getData']) => {
    const { access_token, dateLast6Day } = action;
    try {

      const res = await axios.post(
        `${BASE_URL}/api/admin/revenue/get_revenue_by_date`,
        {
          date: dateLast6Day,
        },
        createAxiosConfig(access_token),
      );
      if (res.data.status_code === 200) {
        return Math.floor(res?.data?.revenue)
      }
    } catch (err: unknown) {
      console.log(err);
    }
  },
);

export const fetchRevenueOpionalDate = createAsyncThunk(
  'admin/fetchRevenueOpionalDate',
  async (action: BlogState['getData']) => {
    const { access_token, date } = action;
    try {

      const res = await axios.post(
        `${BASE_URL}/api/admin/revenue/get_revenue_by_date`,
        {
          date: date,
        },
        createAxiosConfig(access_token),
      );
      if (res.data.status_code === 200) {
        return Math.floor(res?.data?.revenue)
      }
    } catch (err: unknown) {
      console.log(err);
    }
  },
);

export const fetchRevenueStartAndEndDay = createAsyncThunk(
  'admin/fetchRevenueStartAndEndDay',
  async (action: BlogState['getData']) => {
    const { access_token, date_start, date_end } = action;
    try {

      const res = await axios.post(
        `${BASE_URL}/api/admin/revenue/get_revenue_by_date_to_date`,
        {
          date_start,
          date_end,
        },
        createAxiosConfig(access_token),
      );
      if (res.data.status_code === 200) {
        return Math.floor(res?.data?.revenue)
      }
    } catch (err: unknown) {
      console.log(err);
    }
  },
);

export const fetchPaymentToDay = createAsyncThunk(
  'admin/fetchPayment',
  async (action: BlogState['getData']) => {
    const { access_token } = action;
    const res = await axios.post(
      `${BASE_URL}/api/admin/payment/get_all_payment_to_day`,
      {
        access_token,
      },
      createAxiosConfig(access_token),
    );
    if (res.data.status_code === 200) {
      return res.data.payment
        .filter((item: any) => item.status !== -1)
        .map((item: { items: string }) => JSON.parse(item.items));
    } else {
      // Alert.alert(messageApp, networkIsError);
    }
  },
);

export const fetchListProduct = createAsyncThunk(
  'admin/fetchListProduct',
  async (action: BlogState['getData']) => {
    const { access_token } = action;
    try {

      const res = await axios.post(
        `${BASE_URL}/api/admin/product/get_product_list_for_admin`,
        {},
        createAxiosConfig(access_token),
      );
      if (res.data.status_code === 200) {
        res.data?.product
          .filter((item: { status: number }) => item.status !== -1)
          .map((item: { searchTitle: string; title: string }) => {
            return (item.searchTitle = toNonAccentVietnamese(item.title));
          });
        return res?.data?.product.filter(
          (item: { status: number }) => item.status !== -1,
        ).sort((item: { sort_rank: any; }) => item.sort_rank);
      }
    } catch (err: unknown) {
      console.log(err);
    }
  },
);

export const handleCreateCategoryRedux = createAsyncThunk(
  'admin/handleCreateCategory',
  async (action: BlogState['getData']) => {
    const {
      access_token,
      categoryName,
      addCategorySuccess,
      fillInBlankCategory,
      addCategoryExistButDelete,
      categoryNameExist,
      addCategoryFail,
      messageApp,
      agree,
      cancel,
      RestoreCategories
    } = action;
    const res = await axios.post(
      `${BASE_URL}/api/admin/category/create_category`,
      {
        category_name: categoryName,
      },
      createAxiosConfig(access_token),
    );
    const result = res.data.status_code;
    switch (result) {
      case 200:
        Alert.alert(messageApp, addCategorySuccess);
        break;
      case 400:
        Alert.alert(messageApp, fillInBlankCategory);
        break;
      case 398:
        Alert.alert(
          messageApp,
          addCategoryExistButDelete,
          [
            {
              text: agree,
              onPress: async () => {
                await axios
                  .post(
                    `${BASE_URL}/api/admin/category/update_status_active`,
                    {
                      id: res.data.id,
                    },
                    createAxiosConfig(access_token),
                  )
                  .then(res => {
                    res.data.status_code === 200;
                    Alert.alert(messageApp, RestoreCategories);
                  })
                  .catch(err => {
                    console.log(err);
                  });
              },
            },
            {
              text: cancel,
              onPress: () => {
                console.log('Cancel Pressed');
              },
              style: 'cancel',
            },
          ],
          { cancelable: false },
        );
        break;
      case 399:
        Alert.alert(messageApp, categoryNameExist);
        break;
      default:
        Alert.alert(messageApp, addCategoryFail);
        break;
    }
  },
);

export const fetchListCategory = createAsyncThunk(
  'admin/fetchListCategory',
  async (action: BlogState['getData']) => {
    const { access_token } = action;

    try {
      const res = await axios.post(
        `${BASE_URL}/api/admin/category/get_category`,
        {},
        createAxiosConfig(access_token),
      );
      if (res.data.status_code === 200) {
        return res.data?.category_list
          .filter((item: { status: number }) => item.status !== -1)
          .map((item: { id: string; category_name: string; sort_rank: number }) => {
            return { key: item.id, value: item.category_name , sort_rank: item.sort_rank };
          }).sort((item: { sort_rank: number }) => item.sort_rank);
      }
      //  else if (res.data.status_code === 404) {
      // Alert.alert(messageApp, 'Hãy tạo danh mục và sản phẩm để xử dụng app ');
      // } else {
      // Alert.alert(messageApp, networkIsError);
      // }
    } catch (err: unknown) {
      console.error(err);
    }
  },
);

export const handleEditCategoryRedux = createAsyncThunk(
  'admin/handleEditCategory',
  async (action: BlogState['getData']) => {
    const {
      access_token,
      idCategory,
      valueCategory,
      messageApp,
      editCategoryBlank,
      editCategorySuccess,
      categoryNameExist,
      addCategoryExistButDelete,
      editCategoryFail,
      agree,
      cancel, CategoryExist,
      RestoreCategories
    } = action;
    const res = await axios.post(
      `${BASE_URL}/api/admin/category/update_category`,
      {
        id: idCategory,
        category_name: valueCategory,
      },
      createAxiosConfig(access_token),
    );
    if (
      res.data.message.category_name == 'Trường category name là bắt buộc nhập.'
    ) {
      Alert.alert(messageApp, editCategoryBlank);
    } else if (res.data.message == 'Chỉnh sửa danh mục thành công') {
      Alert.alert(messageApp, editCategorySuccess);
    } else if (
      res.data.message.category_name == 'category name đã được sử dụng.'
    ) {
      Alert.alert(messageApp, categoryNameExist);
    } else if (res.data.status_code === 399) {
      Alert.alert(messageApp, CategoryExist);
    } else if (res.data.status_code === 398) {
      Alert.alert(
        messageApp,
        addCategoryExistButDelete,
        [
          {
            text: agree,
            onPress: async () => {
              await axios
                .post(
                  `${BASE_URL}/api/admin/category/update_status_active`,
                  {
                    id: res.data.id,
                  },
                  createAxiosConfig(access_token)
                )
                .then(res => {
                  res.data.status_code === 200;
                  Alert.alert(messageApp, RestoreCategories);
                })
                .catch((err: string) => {
                  console.log(err);
                });
            },
          },
          {
            text: cancel,
            onPress: () => {
              console.log('Cancel Pressed')
            },
            style: 'cancel',
          },
        ],
        { cancelable: false },
      );
    } else {
      Alert.alert(messageApp, editCategoryFail);
    }
  },
);

export const handleDeleteCategoryRedux = createAsyncThunk(
  'admin/handleDeleteCategory',
  async (action: BlogState['getData']) => {
    const {
      idCategory,
      access_token,
      deleteCategorySuccess,
      messageApp,
      categoryNameNotValid,
      deleteCategoryFail,
    } = action;

    const res = await axios.post(
      `${BASE_URL}/api/admin/category/delete_category_remove`,
      {
        id: idCategory,
      },
      createAxiosConfig(access_token),
    );
    const result = res.data.status_code;
    switch (result) {
      case 200:
        Alert.alert(messageApp, deleteCategorySuccess);
        break;
      case 404:
        Alert.alert(messageApp, categoryNameNotValid);
        break;
      default:
        Alert.alert(messageApp, deleteCategoryFail);
        break;
    }
  },
);

export const handleCreateTableRedux = createAsyncThunk(
  'admin/handleCreateTable',
  async (action: BlogState['getData']) => {
    const {
      access_token,
      table,
      addTableSuccess,
      addTableExistButDeleted,
      messageApp,
      addTableExist,
      addTableFail,
      agree,
      cancel, RestoreTableSuccess
    } = action;

    const res = await axios.post(
      `${BASE_URL}/api/admin/table/create_table`,
      {
        tablename: table?.toUpperCase(),
      },
      createAxiosConfig(access_token),
    );
    const result = res.data.status_code;
    if (result === 200) {
      Alert.alert(messageApp, addTableSuccess);
    } else if (result === 398) {
      Alert.alert(
        messageApp,
        addTableExistButDeleted,
        [
          {
            text: agree,
            onPress: async () => {
              await axios
                .post(
                  `${BASE_URL}/api/admin/table/update_status_active`,
                  {
                    id: res.data.id,
                  },
                  createAxiosConfig(access_token),
                )
                .then(res => {
                  res.data.status_code === 200;
                  Alert.alert(messageApp, RestoreTableSuccess);
                })
                .catch((err: string) => {
                  console.error(err);
                });
            },
          },
          {
            text: cancel,
            onPress: () => {
              console.log('Cancel Pressed');
            },
            style: 'cancel',
          },
        ],
        { cancelable: false },
      );
    } else if (result === 399) {
      Alert.alert(messageApp, addTableExist);
    } else {
      Alert.alert(messageApp, addTableFail);
    }
  },
);

export const handleCreateStaff = createAsyncThunk(
  'admin/handleCreateStaff',
  async (action: BlogState['getData']) => {
    const {
      name,
      address,
      phone,
      password,
      status,
      addStaffSuccess,
      staffExistButDeleted,
      infoStaffExist,
      notFullValidStaff,
      addStaffFaill,
      agree,
      cancel,
      access_token,
      messageApp, RestoreStaffSuccess
    } = action;
    const res = await axios.post(
      `${BASE_URL}/api/admin/user-maneger/user_register`,
      {
        name,
        address,
        phone,
        password,
        status, RestoreStaffSuccess
      },
      createAxiosConfig(access_token),
    );
    const result = res.data.status_code;
    switch (result) {
      case 200:
        Alert.alert(messageApp, addStaffSuccess);
        break;
      case 398:
        Alert.alert(
          messageApp,
          staffExistButDeleted,
          [
            {
              text: agree,
              onPress: async () => {
                await axios
                  .post(
                    `${BASE_URL}/api/admin/user-maneger/update_status_active`,
                    {
                      id: res.data.id,
                    },
                    createAxiosConfig(access_token),
                  )
                  .then(res => {
                    res.data.status_code === 200;
                    Alert.alert(messageApp, RestoreStaffSuccess);
                  })
                  .catch((err: string) => {
                    console.log(err);
                  });
              },
            },
            {
              text: cancel,
              onPress: () => {
                console.log('Cancel Pressed');
              },
              style: 'cancel',
            },
          ],
          { cancelable: false },
        );
        break;

      case 399:
        Alert.alert(messageApp, infoStaffExist);
        break;
      case 400:
        Alert.alert(messageApp, notFullValidStaff);
        break;
      default:
        Alert.alert(messageApp, addStaffFaill);
        break;
    }
  },
);

// export const handleGetListTableRedux = createAsyncThunk(
//   'admin/handleGetListTable',
//   async (action: BlogState['getData']) => {
//     const config = {
//       headers: {
//         'Authorization': `Bearer ${access_token}`
//       }
//     };
//     const { access_token } = action;
//     const res = await axios.post(`${BASE_URL}/api/admin/table/get_all_table`, {
//       access_token,
//     });
//     if (res.data.status_code === 200) {
//       return res?.data?.listtable
//         .filter((item: { status: number }) => item.status !== -1)
//         .sort((min: { tablename: number }, max: { tablename: number }) => {
//           return min.tablename - max.tablename;
//         });
//     }
//   },
// );
//handle Delete Table
// export const handleDeleteTableRedux = createAsyncThunk(
//   'admin/handleDeleteTable',
//   async (action: BlogState['getData']) => {
//     const { access_token, id, messageApp, deleteTableSuccess, deleteTableFail } = action;
//     const res = await axios.post(`${BASE_URL}/api/admin/table/delete_table`, {
//       access_token,
//       id: id,
//     });
//     if (res.data.status_code === 200) {
//       Alert.alert(messageApp, deleteTableSuccess);
//     } else {
//       Alert.alert(messageApp, deleteTableFail);
//     }
//   },
// );
//handle cancer Order Table
// export const handleCancerOrderRedux = createAsyncThunk(
//   'admin/handleCancerOrder',
//   async (action: BlogState['getData']) => {
//     const { access_token, id, user_id } = action;
//     const res = await axios.post(`${BASE_URL}/api/user/table/check_out_table`, {
//       access_token,
//       id,
//       user_id,
//     });
//     if (res.data.status_code === 200) {
//       Alert.alert(messageApp, cancelOrderSuccess);
//     } else {
//       Alert.alert(messageApp, cancelOrderFail);
//     }
//   },
// );
//handle Get All Staff

export const handleGetAllStaffRedux = createAsyncThunk(
  'admin/handleGetListStaff',
  async (action: BlogState['getData']) => {
    const { access_token } = action;

    const res = await axios.post(
      `${BASE_URL}/api/admin/user-maneger/list_user_for_admin`,
      {},
      createAxiosConfig(access_token),
    );
    if (res.data.status_code === 200) {
      return res?.data?.users
        .filter((item: { status: number }) => item.status !== -2)
        .sort((a: { status: number }, b: { status: number }) => {
          if (a.status < b.status) {
            return 1;
          }
          if (a.status > b.status) {
            return -1;
          }
          return 0;
        });
    }
  },
);

export const handleGetRevenueOnMouth = createAsyncThunk(
  'admin/handleGetRevenueOnMouth',
  async (action: BlogState['getData']) => {
    const { date_end, date_start, access_token } = action;

    const res = await axios.post(
      `${BASE_URL}/api/admin/revenue/get_revenue_by_date_to_date`,
      {
        date_start,
        date_end,
      },
      createAxiosConfig(access_token),
    );
    if (res.data.status_code === 200) {
      return Math.floor(res?.data?.revenue)
    }
  },
);

export const handleGetRevenueLastMouth = createAsyncThunk(
  'admin/handleGetRevenueLastMouth',
  async (action: BlogState['getData']) => {
    const { date_end, date_start, access_token } = action;
    try {

      const res = await axios.post(
        `${BASE_URL}/api/admin/revenue/get_revenue_by_date_to_date`,
        {
          date_start,
          date_end,
        },
        createAxiosConfig(access_token),
      );
      if (res.data.status_code === 200) {
        return Math.floor(res?.data?.revenue)
      }
    } catch (err: unknown) {
      console.log(err);
    }
  },
);

export const handleGetRevenueOnWeek = createAsyncThunk(
  'admin/handleGetRevenueOnWeek',
  async (action: BlogState['getData']) => {
    const { date_end, date_start, access_token } = action;
    try {

      const res = await axios.post(
        `${BASE_URL}/api/admin/revenue/get_revenue_by_date_to_date`,
        {
          date_start,
          date_end,
        },
        createAxiosConfig(access_token),
      );
      if (res.data.status_code === 200) {
        return Math.floor(res?.data?.revenue)
      }
    } catch (err: unknown) {
      console.log(err);
    }
  },
);

export const handleGetRevenueFirstWeek = createAsyncThunk(
  'admin/handleGetRevenueFirstWeek',
  async (action: BlogState['getData']) => {
    const { date_end, date_start, access_token } = action;

    try {

      const res = await axios.post(
        `${BASE_URL}/api/admin/revenue/get_revenue_by_date_to_date`,
        {
          date_start,
          date_end,
        },
        createAxiosConfig(access_token),
      );
      if (res.data.status_code === 200) {
        return Math.floor(res?.data?.revenue)
      }
    } catch (err: unknown) {
      console.log(err);
    }
  },
);

export const handleGetRevenueSecondWeek = createAsyncThunk(
  'admin/handleGetRevenueSecondWeek',
  async (action: BlogState['getData']) => {
    const { date_end, date_start, access_token } = action;
    try {

      const res = await axios.post(
        `${BASE_URL}/api/admin/revenue/get_revenue_by_date_to_date`,
        {
          date_start,
          date_end,
        },
        createAxiosConfig(access_token),
      );
      if (res.data.status_code === 200) {
        return Math.floor(res?.data?.revenue)
      }
    } catch (err: unknown) {
      console.log(err);
    }
  },
);

export const handleGetRevenueThirdWeek = createAsyncThunk(
  'admin/handleGetRevenueThirdWeek',
  async (action: BlogState['getData']) => {
    const { date_end, date_start, access_token } = action;
    try {

      const res = await axios.post(
        `${BASE_URL}/api/admin/revenue/get_revenue_by_date_to_date`,
        {
          date_start,
          date_end,
        },
        createAxiosConfig(access_token),
      );
      if (res.data.status_code === 200) {
        return Math.floor(res?.data?.revenue)
      }
    } catch (err: unknown) {
      console.log(err);
    }
  },
);

export const handleGetRevenueFourtWeek = createAsyncThunk(
  'admin/handleGetRevenueFourtWeek',
  async (action: BlogState['getData']) => {
    const { date_end, date_start, access_token } = action;
    try {

      const res = await axios.post(
        `${BASE_URL}/api/admin/revenue/get_revenue_by_date_to_date`,
        {
          date_start,
          date_end,
        },
        createAxiosConfig(access_token),
      );
      if (res.data.status_code === 200) {
        return Math.floor(res?.data?.revenue)
      }
    } catch (err: unknown) {
      console.log(err);
    }
  },
);

export const handleGetRevenueFifthWeek = createAsyncThunk(
  'admin/handleGetRevenueFifthWeek',
  async (action: BlogState['getData']) => {
    const { date_end, date_start, access_token } = action;
    try {
      const res = await axios.post(
        `${BASE_URL}/api/admin/revenue/get_revenue_by_date_to_date`,
        {
          date_start,
          date_end,
        },
        createAxiosConfig(access_token),
      );
      if (res.data.status_code === 200) {
        return Math.floor(res?.data?.revenue)
      }
    } catch (err: unknown) {
      console.log(err);
    }
  },
);

export const handleDisableAdmin = createAsyncThunk(
  'admin/handleDisableAdmin',
  async (action: BlogState['getData']) => {
    const { access_token, messageApp, messageDisable } = action;
    try {

      const res = await axios.post(`${BASE_URL}/api/admin/delete`, {}, createAxiosConfig(access_token));
      if (res.data.status_code === 200) {
        Alert.alert(messageApp, messageDisable);
      }
    } catch (err: unknown) {
      console.log(err);
    }
  },
);

export const getFooterPrint = createAsyncThunk(
  'admin/getFooterPrint',
  async (action: BlogState['getData']) => {
    const { access_token } = action;
    try {

      const res = await axios.post(
        `${BASE_URL}/api/admin/footer/get_footer`,
        {},
        createAxiosConfig(access_token),
      );
      if (res.data.status_code === 200) {
        return res?.data;
      } else {
        return res?.data?.message;
      }
    } catch (err: unknown) {
      console.log(err);
    }
  },
);

export const getInventoryList = createAsyncThunk(
  'admin/getInventoryList',
  async (action: BlogState['getData']) => {
    const { access_token, pageCurrent } = action;
    try {
      const res = await axios.get(`${BASE_URL}/api/admin/inventory/get_inventory_list`, {
        params: {
          pageSize: 10,
          currentPage: pageCurrent,
        },
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${access_token}`,
        },
      })
      const inventoryList = res.data.data.inventory_list;
      if (inventoryList.length === 0) {
        return 'NO_MORE_DATA';
      }
      return inventoryList;
    } catch (err: unknown) {
      console.log(err);
    }
  },
);

export const getInventoryListAll = createAsyncThunk(
  'admin/getInventoryListAll',
  async (action: BlogState['getData']) => {
    const { access_token } = action;
    try {
      const res = await axios(`${BASE_URL}/api/admin/inventory/get_all_inventory_list`, {
        method: 'get',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${access_token}`,
        }
      })
      const inventoryList = res.data.data;
      return inventoryList;
    } catch (err: unknown) {
      console.log(err);
    }
  },
);

export const fetchListSubProduct = createAsyncThunk(
  'admin/fetchListSubProduct',
  async (action: BlogState['getData']) => {
    const { access_token } = action;
    try {
      const request = await axios({
        method: 'post',
        url: `${BASE_URL}/api/posWeb/filter`,
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${access_token}`,
        },
        data: {
          "products": {
            "query": {
              "is_extra": "1"
            }
          }
        },
      });
      return {
        data: request.data,
        status: request.status,
      }
    } catch (err: unknown) {
      console.error(err);
    }
  },
);

export const fetchListRecept = createAsyncThunk(
  'admin/fetchListRecept',
  async (action: BlogState['getData']) => {
    const { access_token, pageCurrentRecept } = action;
    try {
      const request = await axios({
        method: 'post',
        url: `${BASE_URL}/api/posWeb/filter`,
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${access_token}`,
        },
        data: {
          "payments": {
            "clauses": {
              "pagination": {
                "pageSize": 10,
                "currentPage": pageCurrentRecept
              }
            }
          }
        }
      });
      const receptList = request.data.data.data_payment.data_list
      if (receptList.length === 0) {
        return;
      }
      return {
        data: request.data,
        status: request.status,
      }
    } catch (err: unknown) {
      console.error(err);
    }
  },
);

export const fetchTaxCode = createAsyncThunk(
  'admin/fetchTaxCode',
  async (action: BlogState['getData']) => {
    const { access_token, searchText,messageApp } = action;
    try {
      const request = await axios({
        method: 'post',
        url: `${BASE_URL}/api/posWeb/filter`,
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${access_token}`,
        },
        data: {
            "payments": {
                "query": {
                    "payment_code": {
                        "operator" : "like",
                        "value" : searchText
                    }
                }
            }
        }
      });
      const receptList = request.data.data.data_payment
      if (receptList.length === 0) {
        Alert.alert(messageApp,'Không tìm thấy mã hóa đơn')
        return;
      }
      return {
        data: request.data,
        status: request.status,
      }
    } catch (err: unknown) {
      console.error(err);
    }
  },
);

export const getListAgencies = createAsyncThunk(
  'admin/getListAgencies',
  async (action: BlogState['getData']) => {
    const { access_token } = action;
    try {
      const res = await axios(`${BASE_URL}/api/posWeb/filter`, {
        method: 'post',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${access_token}`,
        }, params: {
          "agencies": {
            "select": [
              "id",
              "code",
              "name"
            ]
          }
        }
      })
      const listAgencies = res.data.data.data_agencies.map(
        (item: { id: string; name: string }) => {
          return { key: item.id, value: item.name };
        },
      );
      return listAgencies;
    } catch (err: unknown) {
      console.log(err);
    }
  },
);

export const GetData = createSlice({
  name: 'counter',
  initialState,
  reducers: {
    setError: state => {
      state.isError = false;
    },
    setResetPrint: state => {
      state.footerPrinter = '';
    },
    setResetToken: state => {
      state.checktoken = 0;
    },
    setIsValue: (state, action) => {
      state.isCheckLang = action.payload;
    },
    updateInventoryItem: (state, action) => {
      const { id, newQuantity } = action.payload;
      const item = state.listInventory.find((item: { product_id: any; }) => item.product_id === id);
      if (item) {
        Object.assign(item, newQuantity);
      }
    },
    createInventoryItem: (state, action) => {
      state.listInventory.push(action.payload);
    },
    resetStateData: (state) => {
      state.listCategory = [];
      state.listProduct = [];
      state.listTable = [];
      state.listStaff = [];
      state.userInfo = {};
      state.listInventory = [];
    },
    handleResetProductName: (state) => {
      state.resetProductName = true;
    },
    reCheckProductName: (state) => {
      state.resetProductName = false;
    },
    handlePageCurrentRecept: (state) => {
      state.pageCurrentRecept = state.pageCurrentRecept + 1;
    },
    handleLoadingRecept: (state) => {
      state.isLoadingRecept = !state.isLoadingRecept
    }
  },
  extraReducers: builder => {
    builder
      .addCase(handleGetXml.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(handleGetXml.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.xmlData = action.payload;
      })
      .addCase(handleGetXml.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })
      // get revenue to day
      .addCase(fetchRevenueToDay.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(fetchRevenueToDay.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.revenueToday = action.payload;
      })
      .addCase(fetchRevenueToDay.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })
      //check token
      .addCase(checkToken.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(checkToken.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.checktoken = action.payload;
      })
      .addCase(checkToken.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })
      // handle get revenue second week
      .addCase(handleSendInfoContact.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(handleSendInfoContact.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
      })
      .addCase(handleSendInfoContact.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })
      // get revenue last day
      .addCase(fetchRevenueLastDay.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(fetchRevenueLastDay.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.revenueLastday = action.payload;
      })
      .addCase(fetchRevenueLastDay.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })
      // get revenue last 2 day
      .addCase(fetchRevenueLast2Day.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(fetchRevenueLast2Day.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.revenueLast2day = action.payload;
      })
      .addCase(fetchRevenueLast2Day.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })
      // get revenue last 3 day
      .addCase(fetchRevenueLast3Day.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(fetchRevenueLast3Day.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.revenueLast3day = action.payload;
      })
      .addCase(fetchRevenueLast3Day.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })
      // get revenue last 4 day
      .addCase(fetchRevenueLast4Day.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(fetchRevenueLast4Day.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.revenueLast4day = action.payload;
      })
      .addCase(fetchRevenueLast4Day.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })
      // get revenue last 5 day
      .addCase(fetchRevenueLast5Day.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(fetchRevenueLast5Day.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.revenueLast5day = action.payload;
      })
      .addCase(fetchRevenueLast5Day.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })
      // get revenue last 6 day
      .addCase(fetchRevenueLast6Day.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(fetchRevenueLast6Day.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.revenueLast6day = action.payload;
      })
      .addCase(fetchRevenueLast6Day.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })
      // get revenue last Opional day
      .addCase(fetchRevenueOpionalDate.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(fetchRevenueOpionalDate.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.revenueOptionalDate = action.payload;
      })
      .addCase(fetchRevenueOpionalDate.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })
      // get revenue last start and end day
      .addCase(fetchRevenueStartAndEndDay.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(fetchRevenueStartAndEndDay.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.revenueStartAndEndDay = action.payload;
      })
      .addCase(fetchRevenueStartAndEndDay.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })
      //get payment to day
      .addCase(fetchPaymentToDay.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(fetchPaymentToDay.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.paymentToday = action.payload;
      })
      .addCase(fetchPaymentToDay.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })
      //get list category
      .addCase(fetchListCategory.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(fetchListCategory.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.listCategory = action.payload || [];
      })
      .addCase(fetchListCategory.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })
      //get list sub product
      .addCase(fetchListSubProduct.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(fetchListSubProduct.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.checkTokenAccess = action.payload?.status
        state.listSubProduct = action.payload?.data.data.data_product.map((item: { id: any; title: any; }) => ({
          key: item.id,
          value: item.title,
        })) || [];
      })
      .addCase(fetchListSubProduct.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })
      .addCase(fetchListRecept.pending, state => {
        state.isLoadingRecept = true;
        state.isError = false;
      })
      .addCase(fetchListRecept.fulfilled, (state, action) => {
        let newProduct;
        if (typeof action.payload === 'object' && action.payload !== null && 'data' in action.payload) {
          newProduct = action.payload.data.data.data_payment.data_list;
        } else {
          console.error('Unexpected action.payload structure:', action.payload);
        }
        if (newProduct) {
          newProduct.forEach((product: { id: any; }) => {
            const exists = state.listRecept.some((item: { id: any; }) => item.id === product.id);
            if (!exists) {
              state.listRecept.push(product);
            }
          });
        }
        state.isLoadingRecept = false;
        state.isError = false;

      })
      .addCase(fetchListRecept.rejected, state => {
        state.isLoadingRecept = false;
        state.isError = true;
      })
      .addCase(fetchTaxCode.pending, state => {
        state.isLoadingRecept = true;
        state.isError = false;
      })
      .addCase(fetchTaxCode.fulfilled, (state, action) => {
        let newProduct;
        if (typeof action.payload === 'object' && action.payload !== null && 'data' in action.payload) {
          newProduct = action.payload.data.data.data_payment;
        } else {
          console.error('Unexpected action.payload structure:', action.payload);
        }
        if (newProduct) {
          newProduct.forEach((product: { id: any; }) => {
            const exists = state.listRecept.some((item: { id: any; }) => item.id === product.id);
            if (!exists) {
              state.listRecept.push(product);
            }
          });
        }
        state.isLoadingRecept = false;
        state.isError = false;

      })
      .addCase(fetchTaxCode.rejected, state => {
        state.isLoadingRecept = false;
        state.isError = true;
      })
      //get list product
      .addCase(fetchListProduct.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(fetchListProduct.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.listProduct = action.payload;
      })
      .addCase(fetchListProduct.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })
      // handle create category
      .addCase(handleCreateCategoryRedux.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(handleCreateCategoryRedux.fulfilled, state => {
        state.isLoading = false;
        state.isError = false;
      })
      .addCase(handleCreateCategoryRedux.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })
      // handle delete category
      .addCase(handleDeleteCategoryRedux.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(handleDeleteCategoryRedux.fulfilled, state => {
        state.isLoading = false;
        state.isError = false;
      })
      .addCase(handleDeleteCategoryRedux.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })
      //handle edit category
      .addCase(handleEditCategoryRedux.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(handleEditCategoryRedux.fulfilled, state => {
        state.isLoading = false;
        state.isError = false;
      })
      .addCase(handleEditCategoryRedux.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })
      //handle create table
      .addCase(handleCreateTableRedux.pending, state => {
        state.isLoadingTable = true;
        state.isErrorTable = false;
      })
      .addCase(handleCreateTableRedux.fulfilled, state => {
        state.isLoadingTable = false;
        state.isErrorTable = false;
      })
      .addCase(handleCreateTableRedux.rejected, state => {
        state.isLoadingTable = false;
        state.isErrorTable = true;
      })
      //handle get list table
      // .addCase(handleGetListTableRedux.pending, state => {
      //   state.isLoading = true;
      //   state.isError = false;
      // })
      // .addCase(handleGetListTableRedux.fulfilled, (state, action) => {
      //   state.isLoading = false;
      //   state.isError = false;
      //   state.listTable = action.payload;
      // })
      // .addCase(handleGetListTableRedux.rejected, state => {
      //   state.isLoading = false;
      //   state.isError = true;
      // })

      // handle delete table
      // .addCase(handleDeleteTableRedux.pending, state => {
      //   state.isLoading = true;
      //   state.isError = false;
      // })
      // .addCase(handleDeleteTableRedux.fulfilled, state => {
      //   state.isLoading = false;
      //   state.isError = false;
      // })
      // .addCase(handleDeleteTableRedux.rejected, state => {
      //   state.isLoading = false;
      //   state.isError = true;
      // })

      // handle get revenue on mouth
      .addCase(handleGetRevenueOnMouth.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(handleGetRevenueOnMouth.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.revenueOnMouth = action.payload;
      })
      .addCase(handleGetRevenueOnMouth.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })

      // handle get revenue last mouth
      .addCase(handleGetRevenueLastMouth.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(handleGetRevenueLastMouth.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.revenueLastMouth = action.payload;
      })
      .addCase(handleGetRevenueLastMouth.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })

      // handle get revenue on week
      .addCase(handleGetRevenueOnWeek.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(handleGetRevenueOnWeek.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.revenueOnWeek = action.payload;
      })
      .addCase(handleGetRevenueOnWeek.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })

      // handle get revenue first week
      .addCase(handleGetRevenueFirstWeek.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(handleGetRevenueFirstWeek.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.revenueFirstWeek = action.payload;
      })
      .addCase(handleGetRevenueFirstWeek.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })

      // handle get revenue second week
      .addCase(handleGetRevenueSecondWeek.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(handleGetRevenueSecondWeek.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.revenueSecondWeek = action.payload;
      })
      .addCase(handleGetRevenueSecondWeek.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })

      // handle get revenue third week
      .addCase(handleGetRevenueThirdWeek.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(handleGetRevenueThirdWeek.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.revenueThirdWeek = action.payload;
      })
      .addCase(handleGetRevenueThirdWeek.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })

      // handle get revenue fourt week
      .addCase(handleGetRevenueFourtWeek.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(handleGetRevenueFourtWeek.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.revenueFourthWeek = action.payload;
      })
      .addCase(handleGetRevenueFourtWeek.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })

      // handle get revenue fifth week
      .addCase(handleGetRevenueFifthWeek.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(handleGetRevenueFifthWeek.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.revenueFifthWeek = action.payload;
      })
      .addCase(handleGetRevenueFifthWeek.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })

      //handle get list staff
      .addCase(handleGetAllStaffRedux.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(handleGetAllStaffRedux.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.listStaff = action.payload;
      })
      .addCase(handleGetAllStaffRedux.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })

      //handle get create staff
      .addCase(handleCreateStaff.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(handleCreateStaff.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
      })
      .addCase(handleCreateStaff.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })

      //handle disable account admin
      .addCase(handleDisableAdmin.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(handleDisableAdmin.fulfilled, state => {
        state.isLoading = false;
        state.isError = false;
      })
      .addCase(handleDisableAdmin.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })

      // get footer print
      .addCase(getFooterPrint.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(getFooterPrint.fulfilled, (state, action) => {
        const { payload } = action;
        state.isLoading = false;
        state.isError = false;
        if (payload === 'Cửa hàng chưa có footer') {
          state.footerPrinter = '';
          state.idFooter = '';
          state.urlLogo = '';
          // state.idFooterconfirm = "";
        } else {
          const { footer, status_code } = payload;
          state.footerPrinter = footer.content;
          state.idFooter = footer.id;
          state.urlLogo = footer.image;
          // state.idFooterconfirm = status_code;
        }
      })
      .addCase(getFooterPrint.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })

      // get inventory list
      .addCase(getInventoryList.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(getInventoryList.fulfilled, (state, action) => {
        if (action.payload === 'NO_MORE_DATA') {
          state.hasMoreData = false;
          state.hasFetchedAllData = true;
          return;
        }
        if (!state.hasFetchedAllData) {
          const newProducts = action.payload.filter((product: { product_id: any; }) => {
            return !state.listInventory.some((existingProduct: { product_id: any; }) => existingProduct.product_id === product.product_id);
          });
          state.listInventory.push(...newProducts);
          state.pageCurrent += 1;
        }

        state.isLoading = false;
        state.isError = false;


      })
      .addCase(getInventoryList.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })
      // get inventory list
      .addCase(getInventoryListAll.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(getInventoryListAll.fulfilled, (state, action) => {
        // state.listInventory = action.payload;
        state.isLoading = false;
        state.isError = false;
        const newProducts = action.payload.filter((product: { product_id: any; }) => {
          return product.product_id;
        });
        state.listInventory = action.payload;
      })
      .addCase(getInventoryListAll.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })
      //get list sub product
      .addCase(getListAgencies.pending, state => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(getListAgencies.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.listAgencies = action.payload;
      })
      .addCase(getListAgencies.rejected, state => {
        state.isLoading = false;
        state.isError = true;
      })
  },
});

export const { reCheckProductName, setError, setResetPrint, setResetToken, setIsValue, updateInventoryItem, resetStateData, createInventoryItem, handleResetProductName, handlePageCurrentRecept, handleLoadingRecept } = GetData.actions;
export default GetData.reducer;
