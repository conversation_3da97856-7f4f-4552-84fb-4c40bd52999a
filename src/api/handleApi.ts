import axios from 'axios';
import { BASE_URL } from './ApiManager';

export const Login = async (phone: string, password: string) => {
  const request = await axios({
    method: 'post',
    url: `${BASE_URL}/api/admin/login`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      phone: phone,
      password: password,
    },
  });
  return request;
};

export const updateFooterApp = async (
  idFooter: string | number | undefined,
  footerPrint: string | undefined,
  access_token: string,
) => {
  const request = await axios({
    method: 'post',
    url: `${BASE_URL}/api/admin/footer/update_footer`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${access_token}`,
    },
    data: {
      id: idFooter,
      content: footerPrint,
    },
  });
  return request;
};

export const createCustomers = async (
  name: string,
  phone: string,
  token: string,
) => {
  const request = await axios({
    method: 'post',
    url: `${BASE_URL}/api/admin/customer/create_customer`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    data: {
      name: name,
      phone: phone,
    },
  });
  return request;
};

export const createFooters = async (contentFooter: string, token: string) => {
  const res = await axios({
    method: 'post',
    url: `${BASE_URL}/api/admin/footer/create_footer`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    data: {
      content: contentFooter,
    },
  });
  return res;
};

export const getListTables = async (token: string) => {
  const request = await axios({
    method: 'post',
    url: `${BASE_URL}/api/admin/table/get_all_table`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });
  return request;
};

export const deleteTable = async (
  idTableDelete: number | undefined,
  token: string,
) => {
  const request = await axios({
    method: 'post',
    url: `${BASE_URL}/api/admin/table/delete_table`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    data: {
      id: idTableDelete,
    },
  });
  return request;
};

export const deleteTables = async (listtable: number[], token: string) => {
  const request = await axios({
    method: 'post',
    url: `${BASE_URL}/api/admin/table/delete_tables`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    data: {
      id: listtable.toString(),
    },
  });
  return request;
};

export const checkOutTable = async (
  tableIdOrder?: number,
  userId?: string,
  token?: string,
) => {
  // console.log("userId", userId);
  const request = await axios({
    method: 'post',
    url: `${BASE_URL}/api/user/table/check_out_table`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    data: {
      id: tableIdOrder,
      user_id: userId,
    },
  });
  // console.log("resquest", request.data.message);
  return request;
};

export const getDetailPaymentStaff = async (id: number, token: string) => {
  const request = await axios({
    method: 'post',
    url: `${BASE_URL}/api/admin/payment/get_all_payment`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    data: {
      user_id: id,
    },
  });

  return request;
};

export const handleUpdateStaff = async (
  id: number,
  name?: string,
  address?: string,
  phone?: string,
  status?: number,
  token?: string,
) => {
  const request = await axios({
    method: 'post',
    url: `${BASE_URL}/api/admin/user-maneger/edit_user`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    data: {
      id,
      name,
      address,
      phone,
      status,
    },
  });
  return request;
};

export const handleUpdatePassword = async (
  id: number | string,
  password: string,
  token: string,
) => {
  const request = await axios({
    method: 'post',
    url: `${BASE_URL}/api/admin/user-maneger/change_password`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    data: {
      id,
      password,
      password_confirmation: password,
    },
  });
  return request;
};

export const handleDeleleStaff = async (id: string | number, token: string) => {
  const request = await axios({
    method: 'post',
    url: `${BASE_URL}/api/admin/user-maneger/delete_user`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    data: {
      id,
    },
  });
  return request;
};

export const deleteBillPayment = async (id: string | number, token: string) => {
  const request = await axios({
    method: 'post',
    url: `${BASE_URL}/api/admin/payment/delete_payment`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    data: {
      id,
    },
  });
  return request;
};

export const getPaymentByDateToDate = async (
  date_start: string,
  date_end: string,
  token: string,
) => {
  const request = await axios({
    method: 'get',
    url: `${BASE_URL}/api/admin/revenue/get_revenue_by_condition`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    params: {
      "created_at": [date_start, date_end],
    },
  },
  );
  return request;
};

export const handleGetListCustomers = async (token: string) => {
  const request = await axios({
    method: 'post',
    url: `${BASE_URL}/api/admin/customer/get_all_customer`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });
  return request;
};

export const handleDeleteProduct = async (id: string, token: string) => {
  const request = await axios({
    method: 'post',
    url: `${BASE_URL}/api/admin/product/delete_product`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    data: {
      id,
    },
  });
  return request;
};

export const createContact = async (
  name: string,
  numberphone: string | number,
  address: string,
  info_contact: string,
) => {
  const request = await axios({
    method: 'post',
    url: `${BASE_URL}/api/create_contact`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      name,
      numberphone,
      address,
      info_contact,
    },
  });
  return request;
};

export const revertProduct = async (id: string, token: string) => {
  const request = await axios({
    method: 'post',
    url: `${BASE_URL}/api/admin/product/update_status_active`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    data: {
      id,
    },
  });
  return request;
};

export const updateLogoApp = async (
  access_token: string,
  imageUri: string | null,
  fromData: FormData,
  fromDataNoImage: FormData
) => {
  const response = await axios.post(
    `${BASE_URL}/api/admin/update`,
    imageUri === null ? fromDataNoImage : fromData,
    {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'multipart/form-data',
        Authorization: `Bearer ${access_token}`,
      },
    }
  );
  return response;
};

export const updateFooter = async (
  access_token: string,
  imageUri: string | null | undefined,
  fromData: FormData,
  fromDataNoImage: FormData
) => {
  const response = await axios.post(
    `${BASE_URL}/api/admin/footer/update_footer`,
    imageUri === `` ? fromDataNoImage : fromData,
    {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'multipart/form-data',
        Authorization: `Bearer ${access_token}`,
      },
    }
  );
  return response;
};

export const sendDiviceToKen = async (
  access_token: string,
  diviceToken: string
) => {
  const request = await axios({
    method: 'post',
    url: `${BASE_URL}/api/user/add_device_token`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${access_token}`,
    },
    data: {
      device_token: diviceToken
    },
  });
  return request;
};

export const getInventory = async (
  access_token: string,
  pageCurrent: string
) => {
  const request = await axios({
    method: 'get',
    url: `${BASE_URL}/api/admin/inventory/get_inventory_list?pageSize=10&currentPage=${pageCurrent}`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${access_token}`,
    },
  });
  return request;
};

export const createWarehouseReceipt = async (token: string, state: {
  agencyId: string,
  billCode: string,
  totalPrice: string,
  discount: string,
  totalPaymentPrice: string,
  paymentMethod: string,
  note: string,
  quantity: string,
  productName: string,
  productId: string,
  price: string

}) => {
  const { agencyId, billCode, totalPrice, discount, totalPaymentPrice, paymentMethod, note, quantity, productName, productId, price } = state

  const request = await axios({
    method: 'post',
    url: `${BASE_URL}/api/admin/input/addInvoiceInput`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    params: {
      "quantity": [
        quantity
      ],
      "product_title": [
        productName
      ],
      "product_id": [
        productId
      ],
      "price": [
        price
      ],
      "agency_id": agencyId,
      "input_code": billCode,
      "total_price": totalPrice,
      "discount": discount,
      "total_payment_price": totalPaymentPrice,
      "payment_method": paymentMethod,
      "note": note,
      "input_status": "success"
    },
  });
  return request;
};

export const exportWarehouseReceipt = async (token: string, state: {
  totalPrice: string,
  discount: string,
  totalPaymentPrice: string,
  paymentMethod: string,
  note: string,
  quantity: string,
  productName: string,
  productId: string,
  price: string | number,
  phone: string,
  representName: string,
  address: string
  , outputTypesValue: string
  firstPrice: string
}) => {
  const { totalPrice, discount, totalPaymentPrice, paymentMethod, note, quantity, productName, productId, price, phone, representName, address, outputTypesValue, firstPrice } = state;
  const request = await axios({
    method: 'post',
    url: `${BASE_URL}/api/admin/output/create_export_invoice`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    params: {
      "quantity": [
        quantity
      ],
      "product_title": [
        productName
      ],
      "product_id": [
        productId
      ],
      "price": [
        price != 0 ? price : firstPrice
      ],
      "output_receiver": representName,
      "output_receiver_phone": phone,
      "output_address": address,
      "output_type": outputTypesValue,
      "total_price": totalPrice,
      "discount": discount,
      "total_payment_price": totalPaymentPrice,
      "payment_method": paymentMethod,
      "note": note,
      "output_status": "success"
    }
  });
  return request;
};

export const createAcency = async (
  values: {
    acencyName: string,
    companyName: string,
    tax: string,
    address: string,
    contactName: string,
    email: string,
    phone: string,
    note: string

  },
  access_token: string) => {
  const { acencyName, companyName, tax, address, contactName, email, phone, note } = values;
  const request = await axios({
    method: 'post',
    url: `${BASE_URL}/api/admin/agency/create`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${access_token}`,
    },
    params: {
      "name": acencyName,
      "company_name": companyName,
      "company_tax": tax,
      "address": address,
      "contact_person": contactName,
      "contact_email": email,
      "contact_number": phone,
      "note": note
    }
  });
  return request;
};

export const sortRankProduct = async (
  access_token: string,
  sortedData: any,
) => {
  const request = await axios({
    method: 'post',
    url: `${BASE_URL}/api/admin/product/edit-menu`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${access_token}`,
    },
    data:sortedData
  });
  return request;
};

export const sortRankTable = async (
  access_token: string,
  sortedData: any,
) => {
  const request = await axios({
    method: 'post',
    url: `${BASE_URL}/api/admin/table/edit-menu`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${access_token}`,
    },
    data:sortedData
  });
  console.log("sortRankTable",request)
  return request;
};

export const sortRankListCategories = async (
  access_token: string,
  sortedData: any,
) => {
  const request = await axios({
    method: 'post',
    url: `${BASE_URL}/api/admin/category/edit-menu`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${access_token}`,
    },
    data: sortedData
  });
  return request;
};