import { ViewProps } from "react-native";
import { MediaType, PhotoQuality } from "react-native-image-picker";

interface sliceState {
  editItem: boolean;
  isLoading: boolean;
  isError: boolean;
  showModalWeek: boolean;
  isLoadingScreen: boolean;
  flag: boolean;
  select: boolean;
  showOpition: boolean;
  loading: boolean;
  nameSelectOpition: string;
  access_token: string;
  infoAdmin: {
    admin: {
      storename: string,
      address: string,
      name: string,
      id?: string,
      phone?: string,
      created_at: Date,
      expiry_date: Date
      store?:{
        is_tax_included?: number;
        currency?: string;
      }
    }
  } | any
  ;
  showModalFooter: boolean;
  modalFooter: string;
  checkCreate: boolean;
  urlLogo: string | boolean;
  flagTable: boolean;
  flagStaff: boolean;
  flagProduct: boolean;
  flagChart: boolean;
  flagWeek: boolean;
  flagMonth: boolean;
  flagPrint: boolean;
  flagCustomers: boolean;
  flagListCustomers: boolean;
  nameCustomer: string;
  splashLoading?: boolean;
  flagFooter?: boolean ;
  totalDicount?: number;
  totalSurcharge?: number;
  showListPrinter?: boolean;
}
interface getData {
  isCheckLang?: string | number,
  access_token?: string;
  revenueToday?: number;
  revenueOptionalDate?: number;
  revenueStartAndEndDay?: number;
  revenueLastday?: number;
  revenueLast2day?: number;
  revenueLast3day?: number;
  revenueLast4day?: number;
  revenueLast5day?: number;
  revenueLast6day?: number;
  revenueOnWeek?: number;
  revenueFirstWeek?: number;
  revenueSecondWeek?: number;
  revenueThirdWeek?: number;
  revenueFourthWeek?: number;
  revenueFifthWeek?: number;
  revenueOnMouth?: number;
  revenueLastMouth?: number;
  isLoading?: boolean;
  isError?: boolean;
  isLoadingTable?: boolean;
  isErrorTable?: boolean;
  categoryName?: string;
  paymentToday?: any;
  listCategory?: string[] | any;
  listProduct?: string[] | any;
  listTable?: string[];
  listStaff?: any;
  showOpition?: false;
  footerPrinter?: string;
  idFooter?: number | string;
  // idFooterconfirm?: number | string;
  urlLogo?: string;
  date_end?: string | number | Date;
  date_start?: string | number | Date;
  id?: number;
  user_id?: number;
  name?: string;
  address?: string;
  phone?: number | string;
  password?: string;
  status?: number;
  table?: string;
  idCategory?: number;
  changeValue?: string;
  date?: string;
  dateLast6Day?: string;
  dateLast5Day?: string;
  dateLast4Day?: string;
  dateLast3Day?: string;
  dateLast2Day?: string;
  dateLastDay?: string;
  numberphone?: number;
  infoContact?: any;
  dateToDay?: string;
  // phone?: string;
  isLoadingLogin?: boolean;
  isErrorLogin?: boolean;
  messageApp: string;
  sendInfoSuccess?: string;
  sendInfoFail?: string;
  createCustomersFail?: string;
  backgroundcolor?: string;
  blackcolor?: string;
  diamondmember?: string;
  goldmember?: string;
  graycolor?: string;
  maincolor?: string;
  modalbackground?: string;
  platiummember?: string;
  sivermember?: string;
  useroffline?: string;
  useronline?: string;
  userstop?: string;
  matchNumberPhone?: string | RegExp;
  fieldIsBlank?: string;
  minValidNumber?: number;
  maxValidNumberPhone?: string | number;
  maxValidNumber?: string | number;
  minValidPassword?: string | number;
  maxValidPassword?: string | number;
  maxValidSendInfo?: string | number;
  minValidMessageName?: string | number;
  maxValidMessageName?: string | number;
  minValidMessageAddress?: string | number;
  maxValidMessageAddress?: string | number;
  minValidMessageNumberPhone?: string | number;
  maxValidMessageNumberPhone?: string | number;
  matchesNumberPhone?: string;
  minValidMessagePassword?: string;
  maxValidMessagePassword?: string;
  confirmPasswordMessage?: string;
  minValidMessagePSendInfo?: string;
  maxValidMessagePSendInfo?: string;
  createCustomersSuccess?: string;
  createCustomersNumberPhoneExist?: string;
  createCustomerNameFillInBlank?: string;
  vietVangPhoneNumber?: number | string;
  cancel?: string;
  agree?: string;
  networkIsError?: string;
  loginFail?: string;
  fillInBlank?: string;
  networkError?: string;
  messageDisable?: string;
  addCategorySuccess?: string;
  fillInBlankCategory?: string;
  CategoryExist?: string
  RestoreTableSuccess?: string;
  RestoreStaffSuccess?: string
  categoryNameExist?: string;
  addCategoryFail?: string;
  editCategoryFail?: string;
  addCategoryExistButDelete?: string;
  deleteCategorySuccess?: string;
  categoryNameNotValid?: string;
  deleteCategoryFail?: string;
  editCategorySuccess?: string;
  editCategoryBlank?: string;
  addProductSuccess?: string;
  productNameExist?: string;
  notFullValid?: string;
  findNotCategory?: string;
  notSelectedCategory?: string;
  addProductFail?: string;
  alertVat?: string;
  addProductExistButDeleted?: string;
  deleteProductSuccess?: string;
  deleteProductFail?: string;
  editProductSuccess?: string;
  editNotFullValid?: string;
  editProductFail?: string;

  addStaffSuccess?: string;
  addStaffFaill?: string;
  infoStaffExist?: string;
  staffExistButDeleted?: string;
  notFullValidStaff?: string;
  notFullValidAddress?: string;
  notFullValidNameStaff?: string;
  changeInfoStaffSuccess?: string;
  phoneNumberExist?: string;
  phoneNumberNotValid?: string;
  changeInfoStaffFail?: string;
  deleteStaffSuccess?: string;
  deleteStaffFail?: string;
  changePasswordSuccess?: string;
  changePasswordFail?: string;
  addTableSuccess?: string;
  addTableExist?: string;
  addTableExistButDeleted?: string;
  addTableFail?: string;
  cancelOrderSuccess?: string;
  cancelOrderFail?: string;
  deleteTableSuccess?: string;
  deleteTableFail?: string;
  deleteMutilTableSuccess?: string;
  doNotChooseTableDelete?: string;
  deleteMutilTableFail?: string;
  RestoreCategories?: string;
  userInfo?: any;
  checktoken?: number;
  xmlData?:
  | {
    screen?: {
      backgroundcolor?: string;
      blackcolor?: string;
      diamondmember?: string;
      goldmember?: string;
      graycolor?: string;
      maincolor?: string;
      modalbackground?: string;
      platiummember?: string;
      sivermember?: string;
      useroffline?: string;
      useronline?: string;
      userstop?: string;
      matchNumberPhone?: string | RegExp;
      fieldIsBlank?: string;
      minValidNumber?: number;
      createCustomersFail?: string;
      maxValidNumberPhone?: string | number;
      maxValidNumber?: string | number;
      minValidPassword?: string | number;
      maxValidPassword?: string | number;
      maxValidSendInfo?: string | number;
      minValidMessageName?: string | number;
      maxValidMessageName?: string | number;
      minValidMessageAddress?: string | number;
      maxValidMessageAddress?: string | number;
      minValidMessageNumberPhone?: string | number;
      maxValidMessageNumberPhone?: string | number;
      matchesNumberPhone?: string;
      minValidMessagePassword?: string;
      maxValidMessagePassword?: string;
      confirmPasswordMessage?: string;
      minValidMessagePSendInfo?: string;
      maxValidMessagePSendInfo?: string;
      createCustomersSuccess?: string;
      createCustomersNumberPhoneExist?: string;
      createCustomerNameFillInBlank?: string;
      vietVangPhoneNumber?: number | string;
      messageApp: string;
      cancel?: string;
      agree?: string;
      networkIsError?: string;
      loginFail?: string;
      fillInBlank?: string;
      networkError?: string;
      messageDisable?: string;
      addCategorySuccess?: string;
      fillInBlankCategory?: string;
      CategoryExist?: string;
      RestoreTableSuccess?: string;
      RestoreStaffSuccess?: string
      categoryNameExist?: string;
      RestoreCategories?: string;
      addCategoryFail?: string;
      editCategoryFail?: string;
      addCategoryExistButDelete?: string;
      deleteCategorySuccess?: string;
      categoryNameNotValid?: string;
      deleteCategoryFail?: string;
      editCategorySuccess?: string;
      editCategoryBlank?: string;
      addProductSuccess?: string;
      productNameExist?: string;
      notFullValid?: string;
      findNotCategory?: string;
      notSelectedCategory?: string;
      addProductFail?: string;
      alertVat?: string;
      addProductExistButDeleted?: string;
      deleteProductSuccess?: string;
      deleteProductFail?: string;
      editProductSuccess?: string;
      editNotFullValid?: string;
      editProductFail?: string;
      sendInfoSuccess?: string;
      sendInfoFail?: string;
      addStaffSuccess?: string;
      addStaffFaill?: string;
      infoStaffExist?: string;
      staffExistButDeleted?: string;
      notFullValidStaff?: string;
      notFullValidNameStaff?: string;
      notFullValidAddress?: string;
      changeInfoStaffSuccess?: string;
      phoneNumberExist?: string;
      phoneNumberNotValid?: string;
      changeInfoStaffFail?: string;
      deleteStaffSuccess?: string;
      deleteStaffFail?: string;
      changePasswordSuccess?: string;
      changePasswordFail?: string;
      addTableSuccess?: string;
      addTableExist?: string;
      addTableExistButDeleted?: string;
      addTableFail?: string;
      cancelOrderSuccess?: string;
      cancelOrderFail?: string;
      deleteTableSuccess?: string;
      deleteTableFail?: string;
      deleteMutilTableSuccess?: string;
      doNotChooseTableDelete?: string;
      deleteMutilTableFail?: string;
      access_token?: string;
    };
    loginscreen?: {
      nameApp?: string;
      email?: string;
      phoneContact: string;
    }; Viet?: {
      titleApp?: string;
      qlbh?: string;
      nameApp?: string;
      phone?: string;
      pass?: string;
      login?: string;
      registry?: string;
      contact?: string;
      phoneContact?: string;
      email?: string;
    }
  }
  | any;
  listInventory?: any;
  pageCurrent?: any;
  hasMoreData?: boolean;
  isFirstLoad?: boolean;
  checkLoading?: boolean;
  hasFetchedAllData?: any;
  isLoadingInventory?: boolean;
  listSubProduct?: any;
  resetProductName?: boolean;
  listAgencies?: any;
  checkTokenAccess?: number;
  listRecept?: any;
  pageCurrentRecept?: any;
  isLoadingRecept?: boolean;
  searchText?:string;valueCategory?:string;
}
interface detailCustomers {
  detailCustomer: {
    admin_id: number;
    create_date: string;
    created_at: string;
    id: number;
    name: string;
    phone: number;
    point: string;
    searchTitle: string;
    total_money: number;
    updated_at: string;
  };
  item?: any;
  // TableNumber?: any;
}

interface ImageOptions {
  title: string;
  type: string;
  maxWidth: number;
  maxHeight: number;
  quality: PhotoQuality;
  mediaType: MediaType;
  includeBase64: boolean;
  selectionLimit: number;
}
interface exportFileData {
  startDay: string;
  endDay: string;
  dataFood?: any;
  TotalPrice: number;
}

interface detailPayment {
  item: any;
}
interface modalFooter {
  valid: boolean;
  modalFooter: string;
}

interface checkCreate {
  valid: boolean;
}
interface setLoading {
  valid: boolean;
}
interface setToken {
  valid: string;
}
interface validRevenueMonth {
  valid: boolean;
  nameOpiton: string;
}

interface showCreateCustomers {
  valid: boolean;
  nameOpiton: string;
}

interface RevenueDayByDay {
  valid: boolean;
  nameOpiton: string;
}
interface stateEdit {
  valid: boolean;
  nameOpiton: string;
}
interface showRevenueWeeks {
  valid: boolean;
  nameOpiton: string;
}
interface stateShowOption {
  valid: boolean;
  nameOpiton: string;
}
interface propsHandleProduct {
  navigation: {
    pop: (count?: number) => void;
  };
  route: {
    params: {
      title: string;
      vat: string;
      price: string;
      id: string;
      category_name: string;
      image: string;
      category_id: string;
    };
  };
}

interface BlogState {
  counter: sliceState;
  getData: getData;
}
interface rootInterface {
  detailCustomers: detailCustomers;
  exportFileData: exportFileData;
  detailPayment: detailPayment;
  modalFooter: modalFooter;
  propsHandleProduct: propsHandleProduct;
  validRevenueMonth: validRevenueMonth;
  RevenueDayByDay: RevenueDayByDay;
  checkCreate: checkCreate;
  stateEdit: stateEdit;
  stateShowOption: stateShowOption;
  showRevenueWeeks: showRevenueWeeks;
  setLoading: setLoading;
  showCreateCustomers: showCreateCustomers;
  ImageOptions: ImageOptions;
  setToken: setToken
}

interface IViewProps extends ViewProps {
  flex1?: boolean;
  fWrap?: boolean;
  flDirectionRow?: boolean;
  flDirectionCol?: boolean;

  mainBGColor?: boolean;
  whiteBGColor?: boolean;
  redBGColor?: boolean;

  aItemsFlexEnd?: boolean;
  aItemsFlexStart?: boolean;
  aItemsCenter?: boolean;

  jContentBetween?: boolean;
  jContentCenter?: boolean;
  jContentAround?: boolean;

  mHorizontal10?: boolean;
  mHorizontal12?: boolean;
  mHorizontal18?: boolean;
  mHorizontal22?: boolean;
  mTop6?: boolean;
  mTop8?: boolean;
  mTop10?: boolean;
  mTop32?: boolean;
  mTop80?: boolean;
  mBottom10?: boolean;
  mVertical2?: boolean;
  mTop22?: boolean;
  mVertical6?: boolean;
  mVertical4?: boolean;
  mLeft46?: boolean;
  mRight8?: boolean;
  mHorizontal6?: boolean;

  borderMainColor?: boolean;
  borderBlackColor?: boolean;
  borderWidth1?: boolean;
  bRadius5?: boolean;
  bRadius8?: boolean;
  bRadius20?: boolean;
  borderWidth2?: boolean;

  pHorizontal40?: boolean;
  p_2?: boolean;
  p_10?: boolean;
  p_20?: boolean;
  pHorizontal6?: boolean;
  pRight20?: boolean;
  p_Right10?: boolean;

  w_100?: boolean;
  w_90?: boolean;
  w_80?: boolean;
  w_88?: boolean;
  w_70?: boolean;
  w_60?: boolean;
  w_65?: boolean;
  w_67?: boolean;
  w_55?: boolean;
  w_45?: boolean;
  w_50?: boolean;
  w_40?: boolean;
  w_35?: boolean;

  w_33?: boolean;
  w_30?: boolean;
  w_25?: boolean;
  w_20?: boolean;
  w_15?: boolean;
  w_10?: boolean;
w_5?: boolean;
  h_20?: boolean;
  h_34?: boolean;
  h_40?: boolean;
  h_60?: boolean;

  loadingScreen?: boolean;
  modalView?: boolean;
  searchBar?: boolean;
  listCustomers?: boolean;
}
export type { IViewProps, BlogState, rootInterface };