import {useContext} from 'react';
import {NavigationContainer} from '@react-navigation/native';

import {createStackNavigator} from '@react-navigation/stack';

import {AuthContext} from '../context/AuthContext';
import {
  ChartScreen,
  ProductScreen,
  HandleProduct,
  DetailStaff,
  DetailFoods,
  SplashScreen,
  CustommersScreen,
  InfomationScreen,
  LoginScreen,
  InventoryManagementScreen,
  InventoryDetailScreen,
  ManagementWarehouseScreen,
  ExportWarehouseScreen,
  ElectronicInvoiceScreen,
  ElectroicInvoiceDetail,
} from '../Screen/index';

import BottomHomeTab from '../../src/Screen/bottomTab/BottomTab';

const Stack = createStackNavigator();

const StackNavigationScreen = () => {
  const {userInfo, splashLoading} = useContext(AuthContext);

  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
        }}>
        {splashLoading ? (
          <Stack.Screen name="SplashScreen" component={SplashScreen} />
        ) : userInfo.admin ? (
          <>
            <Stack.Screen name="Home" component={BottomHomeTab} />
            <Stack.Screen name="Product" component={ProductScreen} />
            <Stack.Screen name="HandleProduct" component={HandleProduct} />
            <Stack.Screen name="Chart" component={ChartScreen} />
            <Stack.Screen
              name="InventoryManagement"
              component={InventoryManagementScreen}
              options={{gestureEnabled: false}}
            />
            <Stack.Screen
              name="InventoryDetailScreen"
              component={InventoryDetailScreen}
              options={{gestureEnabled: false}}
            />

            <Stack.Screen
              name="DetailStaff"
              component={DetailStaff}
              options={{gestureEnabled: false}}
            />
            <Stack.Screen name="DetailFood" component={DetailFoods} />
            <Stack.Screen
              name="CustommersScreen"
              component={CustommersScreen}
              options={{gestureEnabled: false}}
            />
            <Stack.Screen
              name="InfomationScreen"
              component={InfomationScreen}
              options={{gestureEnabled: false}}
            />
            <Stack.Screen
              name="ManagementWarehouseScreen"
              component={ManagementWarehouseScreen}
              options={{gestureEnabled: false}}
            />
            <Stack.Screen
              name="ExportWarehouseScreen"
              component={ExportWarehouseScreen}
              options={{gestureEnabled: false}}
            />
            <Stack.Screen
              name="ElectronicInvoiceScreen"
              component={ElectronicInvoiceScreen}
              options={{gestureEnabled: false}}
            />
            <Stack.Screen
              name="ElectroicInvoiceDetail"
              component={ElectroicInvoiceDetail}
              options={{gestureEnabled: false}}
            />
          </>
        ) : (
          <Stack.Screen name="Login" component={LoginScreen} />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default StackNavigationScreen;
