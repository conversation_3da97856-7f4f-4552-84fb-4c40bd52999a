import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '../../../store';
import { setShowListPrinter } from '../store/settingsSlice';
import { scanLANForPrinters } from '../../utils/scanLANForPrinters';

// Định nghĩa type cho kết quả quét máy in
interface ScanPrintersResult {
  printers: string[];
  isScanning: boolean;
  error: string | null;
}

export const useScanPrinters = () => {
  const dispatch = useDispatch<AppDispatch>();
  const [scanResult, setScanResult] = useState<ScanPrintersResult>({
    printers: [],
    isScanning: false,
    error: null,
  });

  const scanForPrinters = async () => {
    setScanResult({ printers: [], isScanning: true, error: null });
    try {
      const printers = await scanLANForPrinters();
      setScanResult({ printers, isScanning: false, error: null });

      // Nếu tìm thấy ít nhất 1 máy in, dispatch action setShowListPrinter(true)
      if (printers.length > 0) {
        dispatch(setShowListPrinter(true));
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Lỗi không xác định khi quét máy in';
      setScanResult({ printers: [], isScanning: false, error: errorMessage });
    }
  };

  // Chạy quét khi hook được gọi (có thể tùy chỉnh điều kiện chạy)
  useEffect(() => {
    scanForPrinters();
  }, []);

  return {
    printers: scanResult.printers,
    isScanning: scanResult.isScanning,
    error: scanResult.error,
    scanForPrinters, // Hàm để gọi lại quét thủ công
  };
};