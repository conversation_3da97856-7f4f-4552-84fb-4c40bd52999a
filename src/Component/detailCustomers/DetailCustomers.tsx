import {
  <PERSON><PERSON>,
  <PERSON>H<PERSON>ler,
  Platform,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import React, {useCallback, useEffect} from 'react';
import {useDispatch} from 'react-redux';
import {Text, View} from '../index';

import {setShowCreateCustomers} from '../../Redux/Slide';
import {rootInterface} from '../../interface';
import {ThunkDispatch} from '@reduxjs/toolkit';
import {heightWindow, scale} from '../../utils/style/Reponsive';
import {useAppSelector} from '../../hooks';
import {languages} from '../../../src/constants';
interface IDetailCustomers {
  [key: string]: string | undefined;
  backToPage?: string;
  componentCustomersPhone?: string;
  componentCustomersCreateDay?: string;
  componentCustomersTotalPrice?: string;
  componentCustomersRemainingPoints?: string;
  componentCustomersPoints?: string;
  componentCustomersPointsUsed?: string;
  componentCustomersPointsListPointUsed?: string;
  componentCustomersInfoCustomer?: string;
  componentCustomersFullName?: string;
  close?: string;
  cancel?: string;
  agree?: string;
  messageApp?: string;
}
const DetailCustomers = (props: rootInterface['detailCustomers']) => {
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {maincolor} = xmlData;
  const {name, phone, create_date, total_money, point} = props.detailCustomer;
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): IDetailCustomers | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: IDetailCustomers = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    backToPage,
    componentCustomersPhone,
    componentCustomersCreateDay,
    componentCustomersTotalPrice,
    componentCustomersRemainingPoints,
    componentCustomersPoints,
    componentCustomersPointsUsed,
    componentCustomersPointsListPointUsed,
    componentCustomersInfoCustomer,
    componentCustomersFullName,
    close,
    cancel,
    agree,
    messageApp,
  }: any = langData;
 const currencySymbol = useAppSelector(state => state.counter?.infoAdmin?.admin?.store?.currency_symbol) || "đ";

  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const handleCloseDetailCustomerModal = useCallback(() => {
    dispatch(
      setShowCreateCustomers({
        valid: true,
        nameOpiton: '',
      }),
    );
  }, [dispatch, setShowCreateCustomers]);
  useEffect(() => {
    const backAction = () => {
      Alert.alert(messageApp, JSON.stringify({backToPage}), [
        // Fix: Convert {backToPage} to a string
        {
          text: cancel,
          onPress: () => null,
          style: 'cancel',
        },
        {
          text: agree,
          onPress: () =>
            dispatch(
              setShowCreateCustomers({
                valid: true,
                nameOpiton: '',
              }),
            ),
        },
      ]);
      return true;
    };
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );
    return () => backHandler.remove();
  }, [dispatch]);

  return (
    <View
      flex1
      mHorizontal10
      mTop8
      style={{
        ...Platform.select({
          ios: {
            marginTop: heightWindow * 0.07,
          },
          android: {
            padding: 0,
          },
        }),
      }}>
      <View  flDirectionRow jContentBetween>
        <Text fontSize25 textCenter mainColor fontWeight700>
          {componentCustomersInfoCustomer}
        </Text>
        <TouchableOpacity
          onPress={handleCloseDetailCustomerModal}
          style={{
            borderRadius: scale(8),
            justifyContent: 'center',
            width: '20%',
            height: scale(40),
            backgroundColor: maincolor,
          }}>
          <Text whiteColor fontSize20 fontWeight600 textCenter>
            {close}
          </Text>
        </TouchableOpacity>
      </View>
      <View flDirectionRow w_100 mVertical4 mTop10>
        <View w_40>
          <Text blackColor fontSize18>
            {componentCustomersFullName}:
          </Text>
        </View>
        <View aItemsFlexEnd w_55>
          <Text blackColor fontSize18>
            {name}
          </Text>
        </View>
      </View>

      <View w_100 flDirectionRow mVertical4>
        <View w_40>
          <Text blackColor fontSize18>
            {componentCustomersPhone}:
          </Text>
        </View>
        <View aItemsFlexEnd w_55>
          <Text blackColor fontSize18>
            {phone}
          </Text>
        </View>
      </View>

      <View w_100 flDirectionRow mVertical4>
        <View w_40>
          <Text blackColor fontSize18>
            {componentCustomersCreateDay}:
          </Text>
        </View>
        <View aItemsFlexEnd w_55>
          <Text blackColor fontSize18>
            {create_date}
          </Text>
        </View>
      </View>

      <View w_100 flDirectionRow mVertical4>
        <View w_60>
          <Text blackColor fontSize18>
            {componentCustomersTotalPrice}:
          </Text>
        </View>
        <View aItemsFlexEnd w_35>
          <Text blackColor fontSize18>
            {total_money == null
              ? 0
              : total_money.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            {currencySymbol}
          </Text>
        </View>
      </View>

      <View w_100 flDirectionRow mVertical4>
        <View w_50>
          <Text blackColor fontSize18>
            {componentCustomersRemainingPoints}:
          </Text>
        </View>
        <View aItemsFlexEnd w_45>
          <Text blackColor fontSize18>
            {point == null
              ? 0
              : point.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            {componentCustomersPoints}
          </Text>
        </View>
      </View>

      <View w_100 flDirectionRow mVertical4>
        <View w_50>
          <Text blackColor fontSize18>
            {componentCustomersPointsUsed}:
          </Text>
        </View>
      </View>
      <View>
        <ScrollView showsVerticalScrollIndicator={false}>
          <Text>{componentCustomersPointsListPointUsed}</Text>
        </ScrollView>
      </View>
    </View>
  );
};

export default React.memo(DetailCustomers);
