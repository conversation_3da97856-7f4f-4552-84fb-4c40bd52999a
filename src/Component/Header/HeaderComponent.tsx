import {StyleSheet, Text, TouchableOpacity} from 'react-native';
import { useState } from 'react';
import RowComponent from '../Row/RowComponent';
import TitleComponent from '../Title/TitleComponent';
import {colors} from '../../constants/color';
import TextComponent from '../Text/TextComponent';
import {scale} from '../../utils/style/Reponsive';
import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';
import Entypo from 'react-native-vector-icons/Entypo';
import {useNavigation} from '@react-navigation/native';
import { View } from 'react-native';
import {useAppSelector} from '../../hooks';
import {languages} from '../../constants';

interface Props {
  text: string;
  size?: number;
  color?: string;
  flex?: number;
  onPress?: () => void;
  refreshScreen?: () => void;
  refreshBtn? :boolean,
  btnOption? : boolean,
  onCustomerTypeChange? :(text : string )=>void
}
interface IHandleProduct {
  [key: string]: string | undefined;
}
const HeaderComponent = (props: Props) => {
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);

  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): IHandleProduct | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: IHandleProduct = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {electronicInvoiceIndividual,
    electronicInvoiceCompany}: any = langData;
  const navigation: any = useNavigation();

  const {text, size, color, flex, refreshScreen,refreshBtn,btnOption,onCustomerTypeChange} = props;
  const [isVisible, setIsVisible] = useState(false);
  const [customerType, setCustomerType] = useState(electronicInvoiceIndividual);
  
  const selectCustomerType = (type:string) => {
    setCustomerType(type);
    setIsVisible(false); 
    onCustomerTypeChange?.(type);
  };
  const showLogout = () => {
    navigation.pop(1);
  };

  return (
    <RowComponent
      styles={{
        flex: 1,
      }}>
      <TitleComponent
        text={text}
        size={size ?? 20}
        color={color ?? colors.blue}
        flex={4}
      />
      {
      refreshBtn && 
      <TouchableOpacity
      style={{
        flex: 1,
      }}>
        <SimpleLineIcons
          name="refresh"
          size={scale(40)}
          color={color}
          onPress={refreshScreen}
        />
      </TouchableOpacity>
      }
      {
      btnOption && 
        <View style={styles.container}>
          <TouchableOpacity style={styles.button} onPress={() => setIsVisible(!isVisible)}>
            <RowComponent
            style={{
              flex:1,
              alignItems: 'center',
              justifyContent:'center'
            }}
            >
            <Text style={{color:'black'}}
            >{customerType}</Text>
            </RowComponent>
          </TouchableOpacity>
        {isVisible && (
          <View style={styles.dropdown}>
            <TouchableOpacity onPress={() => selectCustomerType(electronicInvoiceIndividual)}>
              <Text style={{color:'black'}} >{electronicInvoiceIndividual}</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => selectCustomerType(electronicInvoiceCompany)}>
              <Text style={{color:'black'}} >{electronicInvoiceCompany}</Text>
            </TouchableOpacity>
          </View>
        )}
        </View>
      }
      <TouchableOpacity
        style={{
          flex: 1,
          alignItems: 'flex-end',
        }}>
        <Entypo
          name="log-out"
          size={scale(40)}
          color={color}
          onPress={showLogout}
        />
      </TouchableOpacity>
    </RowComponent>
  );
};

export default HeaderComponent;

const styles = StyleSheet.create({
  container: {
    flex:2,
    position: 'relative',
  },
  button: {
    padding: 8,
    borderRadius: 5,
    borderWidth: 2,
    borderColor: colors.gray,
    
  },
  dropdown: {
    marginTop: 5,
    backgroundColor: '#f8f9fa',
    borderColor: '#ddd',
    borderWidth: 1,
    borderRadius: 5,
    position: 'absolute',
    top: scale(22), 
    left: 0,
    right: 0,
    flex:1,
    alignItems: 'center',
    justifyContent:'center'
  },
});

