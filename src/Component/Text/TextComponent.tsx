import {View, Text, StyleProp, TextStyle} from 'react-native';
import React from 'react';
import {globalStyles} from '../../styles/globalStyles';
import {colors} from '../../constants/color';
import {scale} from '../../utils/style/Reponsive';

interface Props {
  text: string;
  size?: number;
  color?: string;
  flex?: number;
  styles?: StyleProp<TextStyle>;
  weight?:
    | 'bold'
    | 'normal'
    | '100'
    | '200'
    | '300'
    | '400'
    | '500'
    | '600'
    | '700'
    | '800'
    | '900';
  align?: 'center' | 'left' | 'right';
  justify?: 'center' | 'flex-start' | 'flex-end';
  aItem?: 'center' | 'flex-start' | 'flex-end';
}

const TextComponent = (props: Props) => {
  const {text, size, color, flex, styles, weight, align, justify, aItem} =
    props;

  return (
    <Text
      style={[
        globalStyles.text,
        {
          flex: flex ?? 1,
          fontSize: size ?? scale(14),
          color: color ?? colors.black,
          fontWeight: weight ?? 'normal',
          textAlign: align ?? 'left',
          justifyContent: justify ?? 'center',
          alignItems: aItem ?? 'center',
        },
        styles,
      ]}>
      {text}
    </Text>
  );
};

export default TextComponent;
