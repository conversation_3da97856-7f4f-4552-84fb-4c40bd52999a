import {StyleSheet, Text as RNText, TextProps} from 'react-native';
import {scale} from '../../utils/style/Reponsive';
import React from 'react';
import {useAppSelector} from '../../hooks';

interface ITextProps extends TextProps {
  contactText?: boolean;
  blackColor?: boolean;
  mainColor?: boolean;
  grayColor?: boolean;
  redText?: boolean;
  whiteColor?: boolean;

  fontSize16?: boolean;
  fontSize18?: boolean;
  fontSize20?: boolean;
  fontSize22?: boolean;
  fontSize25?: boolean;
  fontSize28?: boolean;
  fontSize32?: boolean;

  fontWeight500?: boolean;
  fontWeight600?: boolean;
  fontWeight700?: boolean;
  fontWeight800?: boolean;
  fontWeight900?: boolean;
  fontWeightBold?: boolean;

  flex1?: boolean;

  marginRight16?: boolean;
  marginBottom4?: boolean;
  marginBottom15?: boolean;
  marginVertical2?: boolean;

  paddingTop8?: boolean;
  paddingTop2?: boolean;
  paddingLeft6?: boolean;
  paddingBottom5?: boolean;
  paddingBottom8?: boolean;
  textCenter?: boolean;
  textRight?: boolean;
  textLeft?: boolean;
}
const Text = ({
  style,
  flex1,

  contactText,
  blackColor,
  mainColor,
  whiteColor,
  grayColor,
  redText,

  fontWeight500,
  fontWeight600,
  fontWeight800,
  fontWeight700,
  fontWeight900,
  fontWeightBold,
  textCenter,
  textRight,
  textLeft,

  fontSize16,
  fontSize18,
  fontSize20,
  fontSize22,
  fontSize25,
  fontSize28,
  fontSize32,

  marginBottom15,
  marginRight16,
  marginBottom4,
  marginVertical2,
  paddingBottom5,
  paddingBottom8,
  paddingTop8,
  paddingTop2,
  paddingLeft6,
  ...props
}: ITextProps) => {
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {maincolor, blackcolor, backgroundcolor, graycolor} = xmlData;
  const styles = StyleSheet.create({
    contactText: {
      fontSize: scale(20),
      fontWeight: '600',
      color: maincolor,
    },
    fontSize16: {
      fontSize: scale(16),
    },
    fontSize18: {
      fontSize: scale(18),
    },
    fontSize22: {
      fontSize: scale(22),
    },
    fontSize20: {
      fontSize: scale(20),
    },
    fontSize25: {
      fontSize: scale(25),
    },
    fontSize28: {
      fontSize: scale(28),
    },
    fontSize32: {
      fontSize: scale(32),
    },
    mainColor: {
      color: maincolor,
    },
    whiteColor: {
      color: backgroundcolor,
    },
    blackColor: {
      color: blackcolor,
    },
    grayColor: {
      color: graycolor,
    },
    fontWeight500: {
      fontWeight: '500',
    },
    fontWeight600: {
      fontWeight: '600',
    },
    fontWeight700: {
      fontWeight: '700',
    },
    fontWeight800: {
      fontWeight: '800',
    },
    fontWeight900: {
      fontWeight: '900',
    },
    paddingBottom5: {
      paddingBottom: scale(5),
    },
    paddingTop8: {
      paddingTop: scale(8),
    },
    paddingTop2: {
      paddingTop: scale(2),
    },
    paddingLeft6: {
      paddingLeft: 6,
    },
    marginBottom15: {
      marginBottom: scale(15),
    },
    marginVertical2: {
      marginVertical: scale(2),
    },
    textCenter: {
      textAlign: 'center',
    },
    textRight: {
      textAlign: 'right',
    },
    textLeft: {
      textAlign: 'left',
    },
    fontWeightBold: {
      fontWeight: 'bold',
    },
    redText: {
      color: 'red',
    },
    flex1: {
      flex: 1,
    },
    marginRight16: {
      marginRight: scale(16),
    },
    marginBottom4: {
      marginBottom: scale(4),
    },
    paddingBottom8: {
      paddingBottom: scale(8),
    },
  });
  const _style = StyleSheet.flatten([
    contactText && styles.contactText,
    flex1 && styles.flex1,

    redText && styles.redText,
    blackColor && styles.blackColor,
    mainColor && styles.mainColor,
    whiteColor && styles.whiteColor,
    grayColor && styles.grayColor,

    fontSize16 && styles.fontSize16,
    fontSize18 && styles.fontSize18,
    fontSize20 && styles.fontSize20,
    fontSize22 && styles.fontSize22,
    fontSize25 && styles.fontSize25,
    fontSize28 && styles.fontSize28,
    fontSize32 && styles.fontSize32,

    textCenter && styles.textCenter,
    textRight && styles.textRight,
    textLeft && styles.textLeft,

    fontWeight500 && styles.fontWeight500,
    fontWeight600 && styles.fontWeight600,
    fontWeight700 && styles.fontWeight700,
    fontWeight800 && styles.fontWeight800,
    fontWeight900 && styles.fontWeight900,
    fontWeightBold && styles.fontWeightBold,

    paddingTop8 && styles.paddingTop8,
    paddingTop2 && styles.paddingTop2,
    paddingLeft6 && styles.paddingLeft6,
    paddingBottom8 && styles.paddingBottom8,
    paddingBottom5 && styles.paddingBottom5,
    marginBottom15 && styles.marginBottom15,
    marginRight16 && styles.marginRight16,
    marginBottom4 && styles.marginBottom4,
    marginVertical2 && styles.marginVertical2,
    style,
  ]);

  return <RNText {...props} style={_style} />;
};

export default React.memo(Text);
