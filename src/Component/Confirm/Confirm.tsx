import {useCallback, useState} from 'react';
import {Modal, Pressable, TextInput, TouchableOpacity} from 'react-native';
import {useDispatch} from 'react-redux';

import {setFlagTable, setStateEdit} from '../../Redux/Slide';
import {handleCreateTableRedux} from '../../Redux/GetData';
import {Text, View} from '../index';

import {ConfirmCss} from './ConfirmCss';
import {ThunkDispatch} from '@reduxjs/toolkit';
import {useAppSelector} from '../../hooks';
import {scale} from '../../utils/style/Reponsive';
import {languages} from '../../../src/constants';
interface IConfirmComponent {
  [key: string]: string | undefined;
  tableScreenAddTable?: string;
  tableScreenNameTable?: string;
  staffScreenCancelStaff?: string;
  addTableSuccess?: string;
  addTableExistButDeleted?: string;
  messageApp?: string;
  addTableExist?: string;
  addTableFail?: string;
  agree?: string;
  cancel?: string;
}
const Confirm = () => {
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {maincolor, blackcolor, graycolor} = xmlData;
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): IConfirmComponent | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: IConfirmComponent = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    tableScreenAddTable,
    tableScreenNameTable,
    staffScreenCancelStaff,
    addTableSuccess,
    addTableExistButDeleted,
    messageApp,
    addTableExist,
    addTableFail,
    agree,
    cancel,
    RestoreTableSuccess,
  }: any = langData;
  const [buttonAddTable, setButtonAddTable] = useState<boolean>(true);
  const modalVisible: boolean = true;

  const [table, setTable] = useState<string>();
  const access_token = useAppSelector(state => state.counter.access_token);
  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const closeButton = () => {
    dispatch(setStateEdit({valid: true, nameOpiton: ''}));
  };
  const handleCreateTable = useCallback(async () => {
    try {
      await dispatch(
        handleCreateTableRedux({
          table,
          addTableSuccess,
          addTableExistButDeleted,
          messageApp,
          addTableExist,
          addTableFail,
          agree,
          cancel,
          access_token,
          RestoreTableSuccess,
        }),
      );
      dispatch(setFlagTable());
      closeButton();
    } catch (err: unknown) {
      console.log(err);
    }
  }, [table, access_token]);
  const createTable = useCallback((item: string) => {
    setTable(item);
    setButtonAddTable(!item);
  }, []);

  return (
    <View flex1>
      <Modal animationType="fade" transparent visible={modalVisible}>
        <View flex1 jContentCenter aItemsCenter mHorizontal10
        >
          <View
            style={ConfirmCss.viewCreateTable}
            w_100
            aItemsCenter
            whiteBGColor
            bRadius20
            p_20>
            <Text textCenter fontSize22 fontWeight600 blackColor>
              {tableScreenAddTable}
            </Text>
            <View >
              <View>
                <Text fontSize18 fontWeight500 blackColor>
                  {tableScreenNameTable}
                </Text>
                <TextInput
                  onChangeText={item => {
                    createTable(item);
                  }}
                  style={[
                    ConfirmCss.inputCreateTable,
                    {
                      borderColor: maincolor,
                      color: blackcolor,
                    },
                  ]}
                />
              </View>
            </View>

            <View flDirectionRow w_100 jContentAround mTop6>
              <View w_40 h_40>
                <TouchableOpacity
                  style={[
                    ConfirmCss.btnTextTable,
                    {
                      backgroundColor: buttonAddTable ? graycolor : maincolor,
                    },
                  ]}
                  onPress={handleCreateTable}
                  disabled={buttonAddTable}>
                  <Text whiteColor fontWeightBold textCenter fontSize18>
                    {tableScreenAddTable}
                  </Text>
                </TouchableOpacity>
              </View>
              <View w_40>
                <Pressable
                  style={{
                    width: '100%',
                    backgroundColor: maincolor,
                    height: scale(36),
                    borderRadius: 5,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                  onPress={closeButton}>
                  <Text whiteColor fontWeightBold textCenter fontSize18>
                    {staffScreenCancelStaff}
                  </Text>
                </Pressable>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default Confirm;
