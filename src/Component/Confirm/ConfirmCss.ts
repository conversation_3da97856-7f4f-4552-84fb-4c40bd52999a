import { Platform, StyleSheet } from 'react-native';
import { scale } from '../../utils/style/Reponsive';
import { heightPercentageToDP as hp , widthPercentageToDP as wp} from 'react-native-responsive-screen'

export const ConfirmCss = StyleSheet.create({
  viewCreateTable: {
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    // backgroundColor:'red',
    width: wp('96%'),
  },
  inputCreateTable: {
    borderWidth: 0.7,
    borderRadius: 5,
    height: scale(40),
    marginVertical: 8,
    ...Platform.select({
      ios: {
        padding: scale(10),
      },
      android: {
        paddingLeft: scale(10),
      },
    }),
    width: wp('90%'),

  },
  btnTextTable: {
    width: '100%',
    height: scale(36),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 4,
  },
});
