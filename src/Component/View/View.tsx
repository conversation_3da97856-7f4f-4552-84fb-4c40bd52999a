import {StyleSheet, View as RNView, ViewProps} from 'react-native';
import {scale} from '../../utils/style/Reponsive';
import React from 'react';
import {useAppSelector} from '../../hooks';
import {IViewProps} from 'src/interface';
import { widthPercentageToDP as wp, heightPercentageToDP as hp } from 'react-native-responsive-screen';

const View = ({
  flex1,
  fWrap,
  flDirectionRow,

  mainBGColor,
  whiteBGColor,
  redBGColor,

  jContentAround,
  jContentCenter,
  jContentBetween,
  aItemsCenter,
  aItemsFlexEnd,
  aItemsFlexStart,

  borderMainColor,
  borderBlackColor,
  borderWidth1,
  bRadius5,
  bRadius8,
  bRadius20,
  borderWidth2,

  w_100,
  w_90,
  w_88,
  w_80,
  w_70,
  w_60,
  w_65,
  w_67,
  w_55,
  w_50,
  w_45,
  w_35,
  w_33,
  w_30,
  w_40,
  w_20,
  w_25,
  w_15,
  w_10,
  w_5,
  h_20,
  h_34,
  h_40,
  h_60,

  mVertical2,
  mTop32,
  mTop22,
  mLeft46,
  mHorizontal22,
  mHorizontal6,
  mVertical6,
  mVertical4,
  mHorizontal10,
  mHorizontal12,
  mBottom10,
  mHorizontal18,
  mTop6,
  mTop8,
  mTop10,
  mTop80,
  mRight8,
  p_Right10,
  pHorizontal40,
  p_2,
  p_10,
  p_20,
  pHorizontal6,
  pRight20,
  flDirectionCol,
  searchBar,
  listCustomers,
  loadingScreen,
  modalView,
  style,
  ...props
}: IViewProps) => {
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {maincolor, blackcolor, backgroundcolor, modalbackground} = xmlData;
  const styles = StyleSheet.create({
    flex1: {
      flex: 1,
    },
    mainBGColor: {
      backgroundColor: maincolor,
    },
    whiteBGColor: {
      backgroundColor: backgroundcolor,
    },
    aItemsCenter: {
      alignItems: 'center',
    },
    aItemsFlexEnd: {
      alignItems: 'flex-end',
    },
    aItemsFlexStart: {
      alignItems: 'flex-start',
    },
    mHorizontal10: {
      marginHorizontal: scale(10),
    },
    mHorizontal12: {
      marginHorizontal: scale(12),
    },
    mHorizontal22: {
      marginHorizontal: scale(22),
    },
    p_2: {
      padding: scale(2),
    },
    p_10: {padding: scale(10)},
    p_20: {padding: scale(20)},
    pHorizontal6: {
      paddingHorizontal: scale(6),
    },
    pHorizontal40: {
      paddingHorizontal: hp('40%'),
    },
    mLeft46: {
      marginLeft: scale(46),
    },
    mRight8: {
      marginRight: scale(8),
    },
    pRight20: {
      paddingRight: scale(20),
    },
    p_Right10: {
      paddingRight: scale(10),
    },
    borderMainColor: {
      borderColor: maincolor,
    },
    borderBlackColor: {
      borderColor: blackcolor,
    },
    mBottom10: {
      marginBottom: scale(10),
    },
    mHorizontal18: {
      marginHorizontal: scale(18),
    },
    mHorizontal6: {
      marginHorizontal: scale(6),
    },
    mTop6: {marginTop: scale(6)},
    mTop8: {
      marginTop: scale(8),
    },
    mTop10: {
      marginTop: scale(10),
    },
    borderWidth1: {
      borderWidth: scale(1),
    },
    borderWidth2: {
      borderWidth: scale(2),
    },
    flDirectionRow: {
      flexDirection: 'row',
    },
    flDirectionCol: {
      flexDirection: 'column',
    },
    fWrap: {
      flexWrap: 'wrap',
    },
    jContentBetween: {
      justifyContent: 'space-between',
    },
    jContentAround: {
      justifyContent: 'space-around',
    },
    bRadius5: {
      borderRadius: scale(5),
    },
    bRadius20: {
      borderRadius: scale(20),
    },
    mTop32: {
      marginTop: scale(32),
    },
    mTop22: {
      marginTop: scale(22),
    },
    bRadius8: {
      borderRadius: scale(8),
    },
    jContentCenter: {
      justifyContent: 'center',
    },
    w_100: {
      width: wp('100%'),
    },
    w_90: {
      width: wp('90%'),
    },
    w_80: {
      width: wp('80%'),
    },
    w_88: {
      width: wp('88%'),
    },
    w_70: {
      width: wp('70%'),
    },
    w_65: {width: wp('65%')},
    w_67: {width: wp('67%')},
    w_60: {
      width: wp('60%'),
    },
    w_55: {
      width: wp('55%'),
    },
    w_50: {
      width: wp('50%'),
    },
    w_45: {
      width: wp('45%'),
    },
    w_40: {
      width: wp('40%'),
    },
    w_35: {
      width: wp('35%'),
    },
    w_30: {
      width: wp('30%'),
    },
    w_33: {
      width: wp('33%'),
    },
    w_25: {
      width: wp('25%'),
    },
    w_20: {
      width: wp('20%'),
    },
    w_10: {
      width: wp('10%'),
    },
    w_15: {
      width: wp('15%'),
    },
    w_5:{
      width: wp('5%'),
    },
    mTop80: {
      marginTop: scale(80),
    },
    mVertical6: {
      marginVertical: scale(6),
    },
    mVertical4: {
      marginVertical: scale(4),
    },
    mVertical2: {
      marginVertical: scale(2),
    },
    h_20: {
      height: hp('2.5%'),
    },
    h_34: {
      height: scale(20),
    },
    h_40: {
      height:hp('5%'),
    },
    h_60: {
      height: scale(60),
    },
    loadingScreen: {
      position: 'absolute',
      backgroundColor: modalbackground,
      top: 0,
      bottom: 0,
      right: 0,
      left: 0,
      justifyContent: 'center',
      flex: 1,
    },
    modalView: {
      margin: scale(20),
      backgroundColor: backgroundcolor,
      borderRadius: scale(20),
      padding: scale(32),
      shadowColor: blackcolor,
      shadowOffset: {
        width: 0,
        height: scale(2),
      },
      shadowOpacity: 0.25,
      shadowRadius: scale(4),
      elevation: scale(5),
    },
    searchBar: {
      flexDirection: 'row',
      alignItems: 'center',
      borderColor: blackcolor,
      borderWidth: scale(1),
      borderRadius: scale(5),
      marginVertical: scale(6),
      height: scale(44),
    },
    listCustomers: {
      height: hp('5%'),
      flexDirection: 'row',
      marginVertical: scale(6),
      borderRadius: scale(8),
      borderColor: blackcolor,
      borderWidth: scale(1.5),
    },
    redBGColor:{
      backgroundColor:'red'
    }
  });
  const _style = StyleSheet.flatten([
    flex1 && styles.flex1,
    mainBGColor && styles.mainBGColor,
    whiteBGColor && styles.whiteBGColor,
    aItemsCenter && styles.aItemsCenter,
    aItemsFlexStart && styles.aItemsFlexStart,
    mHorizontal10 && styles.mHorizontal10,
    mHorizontal12 && styles.mHorizontal12,
    mBottom10 && styles.mBottom10,
    borderMainColor && styles.borderMainColor,
    borderWidth1 && styles.borderWidth1,
    flDirectionRow && styles.flDirectionRow,
    jContentBetween && styles.jContentBetween,
    bRadius5 && styles.bRadius5,
    bRadius20 && styles.bRadius20,
    jContentCenter && styles.jContentCenter,
    loadingScreen && styles.loadingScreen,
    fWrap && styles.fWrap,
    jContentAround && styles.jContentAround,
    mTop6 && styles.mTop6,
    mTop8 && styles.mTop8,
    mTop10 && styles.mTop10,
    mTop80 && styles.mTop80,
    modalView && styles.modalView,
    mHorizontal18 && styles.mHorizontal18,
    borderBlackColor && styles.borderBlackColor,
    w_100 && styles.w_100,
    w_90 && styles.w_90,
    w_88 && styles.w_88,
    w_80 && styles.w_80,
    w_70 && styles.w_70,
    w_67 && styles.w_67,
    w_65 && styles.w_65,
    w_60 && styles.w_60,
    w_50 && styles.w_50,
    w_55 && styles.w_55,
    w_45 && styles.w_45,
    w_25 && styles.w_25,
    w_40 && styles.w_40,
    w_35 && styles.w_35,

    w_33 && styles.w_33,
    w_30 && styles.w_30,
    w_20 && styles.w_20,
    w_15 && styles.w_15,
    w_5 && styles.w_5,
    w_10 && styles.w_10,
    mVertical6 && styles.mVertical6,
    mVertical4 && styles.mVertical4,
    bRadius8 && styles.bRadius8,
    borderWidth2 && styles.borderWidth2,
    h_20 && styles.h_20,
    h_34 && styles.h_34,
    h_40 && styles.h_40,
    h_60 && styles.h_60,
    mVertical2 && styles.mVertical2,
    mTop32 && styles.mTop32,
    mTop22 && styles.mTop22,
    pHorizontal40 && styles.pHorizontal40,
    pHorizontal6 && styles.pHorizontal6,
    p_2 && styles.p_2,
    p_10 && styles.p_10,
    p_20 && styles.p_20,
    p_Right10 && styles.p_Right10,
    aItemsFlexEnd && styles.aItemsFlexEnd,
    mLeft46 && styles.mLeft46,
    mHorizontal22 && styles.mHorizontal22,
    mHorizontal6 && styles.mHorizontal6,
    pRight20 && styles.pRight20,
    searchBar && styles.searchBar,
    listCustomers && styles.listCustomers,
    mRight8 && styles.mRight8,
    flDirectionCol && styles.flDirectionCol,
    redBGColor && styles.redBGColor,
    style,
  ]);
  return <RNView {...props} style={_style} />;
};

export default React.memo(View);
