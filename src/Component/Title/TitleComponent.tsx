import {View, Text} from 'react-native';
import React from 'react';
import TextComponent from '../Text/TextComponent';

interface Props {
  text: string;
  size?: number;
  color?: string;
  flex?: number;
  weight?: string;
}

const TitleComponent = (props: Props) => {
  const {text, size, color, flex, weight} = props;

  return (
    <TextComponent
      size={size ?? 20}
      color={color}
      text={text}
      flex={flex ?? 1}
      weight={
        weight
          ? (weight.toString() as
              | 'bold'
              | '700'
              | '600'
              | 'normal'
              | '100'
              | '200'
              | '300'
              | '400'
              | '500'
              | '800'
              | '900')
          : '700'
      }
    />
  );
};

export default TitleComponent;
