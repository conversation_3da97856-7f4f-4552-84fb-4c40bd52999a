import React, {
  ReactElement,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import {FlatList, TouchableOpacity, Modal, DimensionValue} from 'react-native';
import {dropDownComponentsCss} from './DropDownComponentsCss';
import AntDesign from 'react-native-vector-icons/AntDesign';

import {Text, View} from '../index';

import {useAppSelector} from '../../hooks';
import {scale} from '../../utils/style/Reponsive';
import {languages} from '../../../src/constants';
import {reCheckProductName} from '../../Redux/GetData';
import {useDispatch} from 'react-redux';
import {ThunkDispatch} from '@reduxjs/toolkit';

interface IProps {
  value: React.ReactNode;
  data: Array<{value: string; key: number}>;
  onSelect: (item: {value: string; key: number} | any) => void;
  widthComponent: string | number;
}
interface ISelected {
  value: string;
  key: number;
}

const Dropdown = (props: IProps) => {
  const {value, data, onSelect, widthComponent} = props;
  const heightScroll =
    data.length === 0
      ? 0
      : data.length === 1
      ? scale(39)
      : data.length === 2
      ? scale(76)
      : data.length === 3
      ? scale(115)
      : data.length === 4
      ? scale(155)
      : scale(194);
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);

  const resetProductName = useAppSelector(
    state => state.getData.resetProductName,
  );
  useEffect(() => {
    if (resetProductName) {
      setSelected(undefined);
    }
  }, [resetProductName]);
  const {maincolor, blackcolor, backgroundcolor} = xmlData;
  const DropdownButton = useRef<TouchableOpacity>(null);
  const [visible, setVisible] = useState<boolean>(false);
  const [selected, setSelected] = useState<ISelected>();
  const [dropdownTop, setDropdownTop] = useState<number>(0);
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const checkLangue = (): IProps | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: any = {};
      langData.fields.forEach((field: string) => {
        result[field as keyof IProps] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {productsScreenCategoryFind}: any = langData;
  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();

  const openDropdown = useCallback(() => {
    DropdownButton.current?.measure(
      (
        _fx: number | string,
        _fy: number,
        _w: string | number,
        h: number,
        _px: string | number,
        py: number,
      ) => {
        const fx: string = _fx.toString();
        setDropdownTop(py + h);
      },
    );
    setVisible(true);
    dispatch(reCheckProductName());
  }, [DropdownButton, setDropdownTop, setVisible]);

  const onItemPress = useCallback(
    (item: ISelected) => {
      setSelected(item);
      onSelect(item);
      setVisible(false);
    },
    [onSelect, setSelected, setVisible],
  );
  const toggleDropdown = useCallback(() => {
    visible ? setVisible(false) : openDropdown();
  }, [visible, openDropdown]);
  const renderItem = ({item}: any): ReactElement<any, any> => (
    <View whiteBGColor>
      <TouchableOpacity
        style={[
          dropDownComponentsCss.item,
          {
            backgroundColor: backgroundcolor,
          },
        ]}
        onPress={() => onItemPress(item)}>
        <Text textCenter blackColor>
          {item.value}
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderDropdown = (): ReactElement<any, any> => {
    return (
      <Modal visible={visible} transparent animationType="fade">
        <TouchableOpacity
          onPress={() => setVisible(false)}
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <View
            style={[
              dropDownComponentsCss.dropdown,
              {top: dropdownTop},
              {
                shadowColor: blackcolor,
                backgroundColor: backgroundcolor,
                height: heightScroll,
                width: widthComponent as DimensionValue,
              },
            ]}>
            <FlatList
              data={data}
              renderItem={renderItem}
              keyExtractor={(item, index) => index.toString()}
              showsVerticalScrollIndicator={false}
              style={{
                backgroundColor: backgroundcolor,
              }}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    );
  };

  return (
    <TouchableOpacity
      ref={DropdownButton}
      style={[
        dropDownComponentsCss.button,
        {
          backgroundColor: maincolor,
        },
      ]}
      onPress={toggleDropdown}>
      {renderDropdown()}
      <Text flex1 textCenter blackColor>
        {resetProductName
          ? value
          : (selected && selected?.value) ||
            value ||
            productsScreenCategoryFind}
      </Text>
      <AntDesign
        style={dropDownComponentsCss.icon}
        name="caretdown"
        size={18}
      />
    </TouchableOpacity>
  );
};

export default React.memo(Dropdown);
