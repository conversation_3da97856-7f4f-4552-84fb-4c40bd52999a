import { Platform, StyleSheet } from 'react-native';
import { heightWindow, scale } from '../../utils/style/Reponsive';

export const dropDownComponentsCss = StyleSheet.create({
  container: {
    flex: 1,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    height: scale(38),
    zIndex: 1,
  },
  icon: {
    marginRight: scale(10),
  },
  dropdown: {
    position: 'absolute',
    shadowRadius: 4,
    shadowOffset: { height: 4, width: 0 },
    shadowOpacity: 0.5,
    ...Platform.select({
      ios: {
        width: '98%',
        marginLeft: 4,
      },
      android: {
        width: '98%',
        marginLeft: 4,
      },
    }),
  },
  item: {
    paddingVertical: 10,
    ...Platform.select({
      ios: {
        borderBottomWidth: 0.7,
        borderTopWidth: 1,
      },
      android: {
        borderBottomWidth: 1,
      },
    }),
    borderColor: 'black',
  },
});
