import React, {ReactNode} from 'react';
import {
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleProp,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {colors} from '../../constants/color';
import {globalStyles} from '../../styles/globalStyles';
import RowComponent from '../Row/RowComponent';
import TextComponent from '../Text/TextComponent';
import {useNavigation} from '@react-navigation/native';
import {useColorScheme} from 'react-native';

interface Props {
  title?: string;
  back?: boolean;
  right?: ReactNode;
  children: ReactNode;
  isScroll?: boolean;
  styles?: StyleProp<ViewStyle>;
}

const Container = (props: Props) => {
  const {title, back, right, children, isScroll, styles} = props;
  const navigation: any = useNavigation();
  const colorScheme = useColorScheme();

  return (
    <SafeAreaView style={[globalStyles.container, {flex: 1}, styles]}>
      <StatusBar
        barStyle={colorScheme === 'dark' ? 'light-content' : 'dark-content'}
      />
      {/* Header container */}
      {isScroll ? (
        <ScrollView style={{flex: 1, flexGrow: 1}}>{children}</ScrollView>
      ) : (
        <View style={{flex: 1}}>{children}</View>
      )}
    </SafeAreaView>
  );
};

export default Container;
