import {ReactNode,} from 'react';
import {KeyboardTypeOptions, StyleProp, TextInput, ViewStyle} from 'react-native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import {globalStyles} from '../../styles/globalStyles';
import RowComponent from '../Row/RowComponent';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useAppSelector} from '../../hooks';
import {
  View,
} from '../../Component/index';
import {scale} from '../../utils/style/Reponsive';
interface Props {
  onChange: (val: string) => void;
  placeholder?: string;
  title?: string;
  prefix?: ReactNode;
  affix?: ReactNode;
  allowClear?: boolean;
  multiple?: boolean;
  numberOfLine?: number;
  type?: KeyboardTypeOptions;
  isPassword?: boolean;
  setValue?: (val: string) => void;
  clearText?: () => void;
  searchText: string;
  isIcon ?: boolean;
  searchIcon?: boolean;
  onSearch?: () => void;
  size?: number;
  style?: StyleProp<ViewStyle>;
  editable?: boolean;
  cleanButton?  : boolean;
}

const InputComponent = (props: Props) => {
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);

  const {maincolor, blackcolor} = xmlData;

  const {
    onChange,
    placeholder,
    multiple: multiple,
    clearText,
    searchText,
    isIcon,
    searchIcon,
    onSearch,
    size,
    style,
    editable,
    cleanButton,
  } = props;
  return (
    <RowComponent
      styles={[globalStyles.inputContainer, {justifyContent: 'space-evenly',flex:1},style]}>
        {
          isIcon &&  <View
          style={[
            {
              flex: 1,
              paddingHorizontal: scale(2),
            },
          ]}>
          <Ionicons name="search" size={ size ?? scale(34)} color={blackcolor} />
        </View>
        }
      <TextInput
        placeholder={placeholder}
        value={searchText}
        onChangeText={onChange}
        placeholderTextColor={blackcolor}
        style={[
          {color: blackcolor},
          {
            flex: 9,
          },
        ]}
        editable={editable}
      />
      {
      searchIcon && 
      <View
          style={[
            {
              flex: 1,
              paddingHorizontal: scale(2),
              alignItems:'flex-end',
            },
          ]}>
          {
          editable ? <Ionicons name="search" size={ size ?? scale(34)} color={blackcolor} onPress={onSearch}/> :''
          } 
      </View>
      }
        <View
          style={[
            {
              flex: 1,
              paddingHorizontal: scale(2),
              alignItems:'flex-end'
            },
          ]}>
            {
            cleanButton &&  <AntDesign
            name="close"
            size={ size ?? scale(34)}
            color={blackcolor}
            onPress={clearText}
          />
            }
      </View>
    </RowComponent>
  );
};

export default InputComponent;
