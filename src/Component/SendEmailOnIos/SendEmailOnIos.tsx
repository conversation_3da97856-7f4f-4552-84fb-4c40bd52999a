import React, {useState} from 'react';
import {<PERSON>, Button, Alert} from 'react-native';

import {utils, WorkBook, write} from 'xlsx';
import RNFetchBlob from 'react-native-blob-util';
import Mailer from 'react-native-mail';
import {rootInterface} from '../../interface';
import {useAppSelector} from '../../hooks';

const SendEmailOnIos = (props: rootInterface['exportFileData']) => {
  const {startDay, endDay, dataFood, TotalPrice} = props;
  const {storename, address, name} = useAppSelector(
    state => state.counter.infoAdmin.admin,
  );
  const date = new Date();
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const [data] = useState({
    data: [
      ['Báo cáo doanh thu'],
      [`Chi tiết doanh thu từ ngày  ${startDay} đến ngày ${endDay}`],
      dataFood.map((item: {title: string}) => {
        return [item.title];
      }),
      dataFood.map((item: {quantity: string}) => {
        return [item.quantity];
      }),
      dataFood.map((item: {totalPrice: number}) => {
        return [Math.round(item.totalPrice) + 'đ'];
      }),
      ['Tổng cộng :', Math.round(TotalPrice) + 'đ'],
    ],
  });
  const [dataTax] = useState({
    data: [
      [
        `HỘ, CÁ NHÂN KINH DOANH:${storename ? storename : ''}`,
        '',
        '',
        '',
        'Mẫu số S4-HKD',
      ],
      [
        `Địa chỉ:${address ? address : ''}`,
        '',
        '',
        '(Ban hành kèm theo Thông tư số 88/2021/TT-BTC ngày 11 tháng 10 năm 2021 của Bộ trưởng Bộ Tài chính)',
      ],
      [''],
      ['', '', 'SỔ CHI TIẾT DOANH THU BÁN HÀNG HÓA, DỊCH VỤ'],
      ['', '', '', `Tên địa điểm kinh doanh: ${name ? name : ''}  `],
      ['', '', '', '', `Năm: ${year}`],
      [],
      [
        'Ngày,tháng ghi sổ',
        'Chứng từ',
        '',
        'Diễn giải',
        'Doanh thu bán hàng hóa, dịch vụ',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
      ],
      [
        `Ngày ${day} tháng ${month}`,
        'Số hiệu',
        'Ngày,tháng',
        '',
        'Hoạt động kinh doanh khác',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
      ],
      ['A', 'B', 'C', 'D', '10', '', '', '', '', '', '', '', '', '', '', ''],
      [],
      dataFood.map((item: {title: string}) => {
        return [item.title];
      }),
      dataFood.map((item: {quantity: string}) => {
        return [item.quantity];
      }),
      dataFood.map((item: {totalPrice: number}) => {
        return [Math.round(item.totalPrice) + 'đ'];
      }),
      [
        'Tổng cộng',
        `${Math.round(TotalPrice)}đ`,
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
      ],
      ['- Sổ này có 01 trang, đánh số từ trang 01 đến trang 01'],
      [`- Ngày mở sổ: ${day} tháng ${month}  năm ${year} `],
      [
        '',
        '',
        '',
        '',
        '',
        `Ngày ${day} tháng ${month}  năm ${year} `,
        '',
        '',
        '',
        '',
        '',
        '',
      ],
      [
        '',
        'NGƯỜI LẬP BIỂU',
        '',
        '',
        'NGƯỜI ĐẠI DIỆN HỘ KINH DOANH/CÁ NHÂN KINH DOANH',
      ],
      ['', '(Ký, họ tên)', '', '', '', '(Ký, họ tên, đóng dấu)'],
      ['', '', '', '', '', ''],
      ['', '', '', '', '', ''],
      ['', '', '', '', '', ''],
      ['', '', '', '', '', ''],
      [
        '',
        `${storename ? storename : ''}`,
        '',
        '',
        '',
        `${storename ? storename : ''}`,
      ],
    ],
  });

  const writeWorkbook = async (wb: WorkBook) => {
    const wbout = write(wb, {type: 'buffer', bookType: 'xlsx'});
    const file =
      RNFetchBlob.fs.dirs.DocumentDir +
      `/Chi tiết doanh thu từ ngày  ${startDay} đến ngày ${endDay} .xlsx`;
    await RNFetchBlob.fs.writeFile(file, Array.from(wbout), 'ascii');
    return file;
  };
  const writeWorkbookTax = async (wb: WorkBook) => {
    const wbout = write(wb, {type: 'buffer', bookType: 'xlsx'});
    const file =
      RNFetchBlob.fs.dirs.DocumentDir +
      `/Bản hỗ trợ khai báo thuế ${startDay} đến ngày ${endDay} .xlsx`;
    await RNFetchBlob.fs.writeFile(file, Array.from(wbout), 'ascii');
    return file;
  };

  const exportFile = async () => {
    /* convert AOA back to worksheet */
    const ws = utils.aoa_to_sheet(data.data);
    /* build new workbook */
    const wb = utils.book_new();
    utils.book_append_sheet(wb, ws, 'SheetJS', true);
    /* write file */
    const res = await writeWorkbook(wb);
    Mailer.mail(
      {
        subject: 'Delta POS Việt Vang',
        recipients: [],
        ccRecipients: [],
        bccRecipients: [],
        body: `Chi tiết doanh thu từ ngày  ${startDay} đến ngày ${endDay}`,
        attachments: [
          {
            path: res,
            type: 'xlsx',
          },
        ],
      },
      (error, event) => {
        if (error == 'not_available') {
          Alert.alert('Vui lòng đăng nhập email trên máy bạn');
          return;
        }
        if (event == 'cancelled') {
          Alert.alert('Đã huỷ bỏ');
          return;
        }
        if (event == 'sent') {
          Alert.alert('Gửi thành công');
          return;
        }
      },
    );
  };
  const exportFileTax = async () => {
    /* convert AOA back to worksheet */
    const ws = utils.aoa_to_sheet(dataTax.data);
    /* build new workbook */
    const wb = utils.book_new();
    utils.book_append_sheet(wb, ws, 'SheetJS', true);
    /* write file */
    const res = await writeWorkbookTax(wb);
    Mailer.mail(
      {
        subject: 'Delta POS Việt Vang',
        recipients: [],
        ccRecipients: [],
        bccRecipients: [],
        body: `Bản hỗ trợ khai báo thuế ${startDay} đến ngày ${endDay}`,
        attachments: [
          {
            path: res,
            type: 'xlsx',
          },
        ],
      },
      (error, event) => {
        if (error == 'not_available') {
          Alert.alert('Vui lòng đăng nhập email trên máy bạn');
          return;
        }
        if (event == 'cancelled') {
          Alert.alert('Đã huỷ bỏ');
          return;
        }
        if (event == 'sent') {
          Alert.alert('Gửi thành công');
          return;
        }
      },
    );
  };
  return (
    <View>
      <Button title="Gửi báo cáo doanh thu" onPress={exportFile} />
      <Button title="Gửi bản hỗ trợ khai báo thuế" onPress={exportFileTax} />
    </View>
  );
};

export default React.memo(SendEmailOnIos);
