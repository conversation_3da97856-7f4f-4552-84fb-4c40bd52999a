import {useEffect, useState} from 'react';
import {
  Alert,
  Image,
  TextInput,
  TouchableOpacity,
  Platform,
  Keyboard,
  TouchableWithoutFeedback,
} from 'react-native';
import {useDispatch} from 'react-redux';
import {Text, View} from '../../index';

import AntDesign from 'react-native-vector-icons/AntDesign';
import {launchImageLibrary} from 'react-native-image-picker';
import DropDownComponents from '../../dropDownComponents/DropDownComponents';

import {ModalADDitemCss} from './ModalAddItemCss';
import {BASE_URL} from '../../../api/ApiManager';
import {createInventoryItem, fetchListSubProduct} from '../../../Redux/GetData';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import {
  setStateEdit,
  setFlagProduct,
  setLoadingScreen,
  setStateShowOption,
} from '../../../Redux/Slide';
import {useAppSelector} from '../../../hooks';
import {heightWindow, scale} from '../../../utils/style/Reponsive';
import {KeyboardAvoidingView} from 'react-native';
import {ThunkDispatch} from '@reduxjs/toolkit';
import {revertProduct} from '../../../api/handleApi';
import {rootInterface} from '../../../interface';
import {languages} from '../../../../src/constants';
import { heightPercentageToDP as hp , widthPercentageToDP as wp} from 'react-native-responsive-screen'

interface CategoryItem {
  value: string;
  key: number;
}
interface ISelectedImage {
  uri: string;
  type: string;
  name: string;
}

interface IAddProducts {
  [key: string]: string | undefined;
  componentAddProductChooseCategory?: string;
  componentAddProductCategories?: string;
  componentAddProductName?: string;
  componentAddProductPrice?: string;
  componentAddProductVat?: string;
  componentAddProductImage?: string;
  componentAddProductCreate?: string;
  componentChangeProductImage?: string;
}

const ModalADDitem = () => {
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);

  const {maincolor, blackcolor, graycolor, whitecolor} = xmlData;
  const options: rootInterface['ImageOptions'] = {
    title: 'Select Image',
    type: 'library',
    maxWidth: 200,
    maxHeight: 200,
    quality: 1,
    mediaType: 'photo',
    includeBase64: false,
    selectionLimit: 1,
  };
  const [name, setName] = useState<string>('');
  const [price, setPrice] = useState<string>('');
  const [vat, setVAT] = useState<string>('');
  const [imageUri, setImageUri] = useState<string>();
  const [fromData, setFromData] = useState<FormData>(new FormData());
  const [checkValue, setCheckValue] = useState<boolean>(true);
  const [checkUpload, setCheckUpload] = useState<boolean>(true);
  const [selectedCategory, setSelectedCategory] = useState<CategoryItem>();
  const [selectedImage, setSelectedImage] = useState<ISelectedImage>();
  const [selectedValue, setSelectedValue] = useState<string>('0');
  // get state on redux
  const access_token: any = useAppSelector(state => state.counter.access_token);
  const listCategory = useAppSelector(state => state.getData.listCategory);
  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();

  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): IAddProducts | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: IAddProducts = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    productsScreenListCategories,
    componentAddProductChooseCategory,
    componentAddProductCategories,
    componentAddProductName,
    componentAddProductPrice,
    componentAddProductVat,
    componentAddProductImage,
    componentAddProductCreate,
    agree,
    cancel,
    messageApp,
    addProductExistButDeleted,
    addProductFail,
    addProductSuccess,
    alertVat,
    findNotCategory,
    notFullValid,
    notSelectedCategory,
    productNameExist,
    RestoreProductSuccess,
    productsScreenDishType,
    productsScreenMainCourse,
    productsScreenSideDish,
    productsScreenCategoriesTitle,
    componentChangeProductImage
  }: any = langData;
  //handle get all category

  const handleCategorySelect = (item: CategoryItem) => {
    setSelectedCategory(item);
  };
  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };
  const currencySymbol = useAppSelector(state => state.counter?.infoAdmin?.admin?.store?.currency_symbol) || "đ";
  //addProduct
  const addProductWidthImage = async () => {
    const images = await launchImageLibrary(options);
    const formdata: any = new FormData();
    // if (images.didCancel) {
    //   formdata.append('image', selectedImage);
    //   setFromData(formdata);
    //   setCheckUpload(true);
    // }
    if (images.assets) {
      formdata.append('image', {
        uri: images?.assets?.[0]?.uri ?? '',
        type: images?.assets?.[0]?.type ?? '',
        name: images?.assets?.[0]?.fileName ?? '',
      });
      setSelectedImage({
        uri: images?.assets?.[0]?.uri ?? '',
        type: images?.assets?.[0]?.type ?? '',
        name: images?.assets?.[0]?.fileName ?? '',
      });
      setImageUri(images?.assets?.[0]?.uri);
    }
    formdata.append('ProductCode', null);
    formdata.append('title', name);
    formdata.append('price', price);
    formdata.append('vat', vat);
    formdata.append('category_id', selectedCategory?.key.toString());
    formdata.append('type_commodity', 0);
    formdata.append('is_extra', selectedValue);
    formdata.append('type_final_product', 1);
    formdata.append('inventory_required', 0);
    formdata.append('type_commodity', 0);
    formdata.append('min_quantity', 0);
    formdata.append('check', 0);
    setFromData(formdata);
  };
  // Create Product
  const CreateProduct = async () => {
    setCheckValue(true);
    dispatch(setLoadingScreen({valid: true}));
    const formdata: any = new FormData();
    formdata.append('title', '22222');
    formdata.append('price', 1);
    formdata.append('vat', 1);
    formdata.append('category_id', 8);
    formdata.append('type_commodity', 0);
    formdata.append('is_extra', selectedValue);
    formdata.append('type_final_product', 1);
    formdata.append('inventory_required', 0);
    formdata.append('type_commodity', 0);
    formdata.append('min_quantity', 0);
    formdata.append('check', 0);
    const uri = imageUri || null;
    try {
      const res = await fetch(`${BASE_URL}/api/admin/product/add_product_new`, {
        method: 'post',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${access_token}`,
        },
        body: uri ? fromData : formdata,
      });
      const obj = await res.json();
      const {status_code, message, id} = obj;
      if (status_code === 200) {
        dispatch(
          createInventoryItem({
            product: obj.product,
            id: obj.product.id,
            quantity: 0,
            product_id: obj.product.id,
          }),
        );

        dispatch(
          setStateShowOption({
            valid: true,
            nameOpiton: '',
          }),
        );
        Alert.alert(messageApp, addProductSuccess);
        dispatch(fetchListSubProduct({access_token: access_token, messageApp}));
      } else if (status_code === 399) {
        Alert.alert(messageApp, productNameExist);
      } else if (status_code === 398) {
        Alert.alert(
          messageApp,
          addProductExistButDeleted,
          [
            {
              text: agree,
              onPress: async () => {
                revertProduct(id, access_token).then(() => {
                  Alert.alert(messageApp, RestoreProductSuccess);
                  dispatch(setFlagProduct());
                  dispatch(
                    setStateShowOption({
                      valid: true,
                      nameOpiton: '',
                    }),
                  );
                });
              },
            },
            {
              text: cancel,
              onPress: () => console.log('Cancel Pressed'),
              style: 'cancel',
            },
          ],
          {cancelable: false},
        );
      } else if (status_code === 400) {
        Alert.alert(messageApp, notFullValid);
      } else if (message === findNotCategory) {
        Alert.alert(messageApp, notSelectedCategory);
      } else if (status_code === 404) {
        Alert.alert(messageApp, addProductFail);
      }
    } catch (err: unknown) {
      console.log(err);
    } finally {
      setName('');
      setPrice('');
      setVAT('');
      setImageUri('');
      setCheckValue(true);
      dispatch(setFlagProduct());
      dispatch(setLoadingScreen({valid: false}));
      setCheckUpload(true);
    }
  };

  if (parseInt(vat) >= 100 || parseInt(vat) < 0) {
    Alert.alert(messageApp, alertVat);
    setVAT('');
  }

  const ClearImage = () => {
    setImageUri('');
  };

  useEffect(() => {
    if (
      selectedCategory !== undefined &&
      parseInt(vat) >= 0 &&
      parseInt(price) > 0 &&
      name.length > 0
    ) {
      setCheckValue(false);
    } else {
      setCheckValue(true);
    }
  }, [vat, price, name, selectedCategory]);
  useEffect(() => {
    if (selectedImage !== undefined) {
      setCheckUpload(false);
    }
  }, [selectedImage]);
  const CustomRadioButton = ({label, selected, onSelect}: any) => (
    <TouchableOpacity
      style={[
        ModalADDitemCss.radioButton,
        {
          backgroundColor: selected ? maincolor : whitecolor,
          borderColor: maincolor,
        },
      ]}
      onPress={onSelect}>
      <Text
        style={[
          ModalADDitemCss.radioButtonText,
          {color: selected ? '#FFF' : '#000'},
        ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );
  return (
    <KeyboardAwareScrollView
      // style={ModalADDitemCss.container}
      contentContainerStyle={{flexGrow: 1,}}
      keyboardShouldPersistTaps="handled">
      <TouchableWithoutFeedback onPress={dismissKeyboard}>
        <KeyboardAvoidingView
          style={{
            height: heightWindow,
            ...Platform.select({
              ios: {
                // marginTop: scale(10),
              },
              android: {
                // marginTop: heightWindow * 0.15,
              },
            }),
            // backgroundColor: 'red',
          }}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
          <View
            style={{
              marginTop:hp('10%'),
            }}>
            <View
              style={{
                zIndex: 1,
                alignSelf: 'flex-end',
                borderRadius: scale(50),
                position: 'absolute',
                ...Platform.select({
                  ios: {
                    top: scale(15),
                  },
                  android: {
                    top: scale(15),
                  },
                }),
              }}
              whiteBGColor>
              <TouchableOpacity
                onPress={() =>
                  dispatch(
                    setStateEdit({
                      valid: true,
                      nameOpiton: '',
                    }),
                  )
                }>
                <AntDesign
                  name="closecircle"
                  size={scale(35)}
                  color={maincolor}
                />
              </TouchableOpacity>
            </View>
            <View
              style={{
                marginTop: scale(25),
                paddingHorizontal: scale(10),
                paddingVertical: scale(20),
              }}
              bRadius20
              whiteBGColor>
              <View >
                <Text marginBottom4 blackColor>
                  {productsScreenCategoriesTitle}
                </Text>
                <View>
                  <DropDownComponents
                    value={
                      listCategory?.length == 0
                        ? componentAddProductCategories
                        : componentAddProductChooseCategory
                    }
                    data={listCategory}
                    onSelect={handleCategorySelect}
                    widthComponent="90%"
                  />
                </View>
              </View>
              <View  mTop6>
                <Text blackColor>{componentAddProductName}</Text>
                <View
                  flDirectionRow
                  borderWidth1
                  bRadius5
                  aItemsCenter
                  h_40
                  borderMainColor>
                  <TextInput
                    onChangeText={e => {
                      setName(e);
                    }}
                    style={[
                      ModalADDitemCss.inputText,
                      {
                        color: blackcolor,
                      },
                    ]}
                    value={name}
                  />
                </View>
              </View>
              <View  mTop6>
                <Text blackColor>
                  {componentAddProductPrice}
                  {` `}({currencySymbol})
                </Text>
                <View
                  flDirectionRow
                  
                  borderWidth1
                  bRadius5
                  aItemsCenter
                  h_40
                  borderMainColor>
                  <TextInput
                    onChangeText={(e: string) => {
                      setPrice(e);
                    }}
                    style={[
                      ModalADDitemCss.inputText,
                      {
                        color: blackcolor,
                      },
                    ]}
                    keyboardType={'numeric'}
                    value={price ? price.toString() : undefined}
                  />
                </View>
              </View>
              <View  mTop6>
                <Text blackColor>
                  {componentAddProductVat}
                  {` `}(%)
                </Text>
                <View
                  flDirectionRow
                  
                  borderWidth1
                  bRadius5
                  aItemsCenter
                  h_40
                  borderMainColor>
                  <TextInput
                    onChangeText={e => {
                      setVAT(e);
                    }}
                    style={[
                      ModalADDitemCss.inputText,
                      {
                        color: blackcolor,
                      },
                    ]}
                    keyboardType={'numeric'}
                    value={vat}
                  />
                </View>
              </View>
              <View  mTop6>
                <Text blackColor>{productsScreenDishType}</Text>
                <View  flDirectionRow jContentBetween>
                  <CustomRadioButton
                    label={productsScreenMainCourse}
                    selected={selectedValue === '0'}
                    onSelect={() => setSelectedValue('0')}
                  />
                  <CustomRadioButton
                    label={productsScreenSideDish}
                    selected={selectedValue === '1'}
                    onSelect={() => setSelectedValue('1')}
                  />
                </View>
              </View>
              {imageUri ? (
                <View>
                  <View style={ModalADDitemCss.btnClearImage} aItemsFlexEnd>
                    {/* <TouchableOpacity onPress={ClearImage}>
                    <AntDesign
                      name="closecircle"
                      size={scale(35)}
                      color={maincolor}
                    />
                  </TouchableOpacity> */}
                  </View>
                  <Image
                    source={{uri: imageUri}}
                    style={ModalADDitemCss.imageContent}
                  />
                </View>
              ) : null}

              <View>
                <View style={ModalADDitemCss.addImage}>
                  <TouchableOpacity
                    onPress={addProductWidthImage}
                    style={[
                      ModalADDitemCss.btnAddImage,
                      {
                        backgroundColor: checkValue ? graycolor : maincolor,
                      },
                    ]}
                    disabled={checkValue}>
                    <Text whiteColor fontWeight800 fontSize18>
                      {checkUpload ? componentAddProductImage : componentChangeProductImage}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>

              <View style={ModalADDitemCss.viewAddProduct}>
                <TouchableOpacity
                  onPress={CreateProduct}
                  style={[
                    ModalADDitemCss.btnAddProduct,
                    {
                      backgroundColor: checkUpload ? graycolor : maincolor,
                    },
                  ]}
                  disabled={checkUpload}>
                  <Text whiteColor fontWeight800 fontSize18>
                    {componentAddProductCreate}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </KeyboardAwareScrollView>
  );
};

export default ModalADDitem;
