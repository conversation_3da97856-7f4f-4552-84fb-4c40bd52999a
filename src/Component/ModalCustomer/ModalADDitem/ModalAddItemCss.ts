import { StyleSheet } from 'react-native';
import { scale } from '../../../utils/style/Reponsive';

export const ModalADDitemCss = StyleSheet.create({
  addImage: {
    container: { flex: 1 },
    marginVertical: scale(6),
    width: '100%',
    height: scale(40),
    fontSize: scale(12),
    borderRadius: scale(5),
    marginTop: scale(14),
  },
  viewAddProduct: {
    marginTop: scale(10),

  },
  btnClearImage: {
    bottom: -20,
    zIndex: 1,
    right: -15,
    marginTop: scale(10),
  },
  imageContent: {
    height: scale(240),
  },
  btnAddImage: {
    height: scale(40),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scale(12),
  },
  btnAddProduct: {
    height: scale(40),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scale(12),
  },
  inputText: {
    borderRadius: scale(5),
    height: scale(44),
    paddingLeft: scale(10),
    fontSize: scale(14),
    width: '100%',
  },
  radioButton: {
    paddingVertical: scale(12),
    paddingHorizontal: scale(16),
    borderRadius: scale(8),
    marginVertical: scale(8),
    borderWidth: scale(1),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '48%',
  },
  radioButtonText: {
    fontSize: scale(20),
  },
});
