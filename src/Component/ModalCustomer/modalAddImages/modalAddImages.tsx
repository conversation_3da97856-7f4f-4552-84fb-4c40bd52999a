import {TouchableOpacity, Image, Alert, SafeAreaView} from 'react-native';
import {useEffect, useState} from 'react';
import {useDispatch} from 'react-redux';
import {
  setFlagFooter,
  setFlagPrint,
  showModalFooter,
} from '../../../Redux/Slide';
import View from '../../View/View';
import Text from '../../Text/Text';

import {launchImageLibrary} from 'react-native-image-picker';
import AntDesign from 'react-native-vector-icons/AntDesign';
import {modalAddImagesCss} from './modalAddImagesCss';
import {useAppSelector} from '../../../hooks';
import {ThunkDispatch} from '@reduxjs/toolkit';
import {heightWindow} from '../../../utils/style/Reponsive';
import {updateFooter} from '../../../api/handleApi';
import {rootInterface} from '../../../interface';
import {languages} from '../../../../src/constants';

interface IAddCustomers {
  [key: string]: string | undefined;
  componentAddImagesUpdate?: string;
  componentAddImagesUpload?: string;
  componentAddImagesTitle?: string;
  componentAddImagesSuccess?: string;
  staffScreenCancelStaff?: string;
}
const ModalAddImages = () => {
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {maincolor, graycolor} = xmlData;
  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const [imageUri, setImageUri] = useState<string>();
  const [fromData, setFromData] = useState<FormData>(new FormData());
  const [fromDataNoImage, setFromDataNoImage] = useState<FormData>(
    new FormData(),
  );
  const [buttonEditFooter, setButtonEditFooter] = useState<boolean>(true);
  const {urlLogo, idFooter, footerPrinter} = useAppSelector(
    state => state.getData,
  );
  const access_token = useAppSelector(state => state.counter.access_token);
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): IAddCustomers | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: IAddCustomers = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    componentAddImagesUpdate,
    componentAddImagesUpload,
    componentAddImagesTitle,
    componentAddImagesSuccess,
    staffScreenCancelStaff,
    messageApp,
  }: any = langData;
  const close = () => {
    dispatch(
      showModalFooter({
        valid: true,
        modalFooter: '',
      }),
    );
  };
  useEffect(() => {
    setImageUri(urlLogo);
  }, []);
  const options: rootInterface['ImageOptions'] = {
    title: 'Select Image',
    type: 'library',
    maxWidth: 200,
    maxHeight: 200,
    quality: 1,
    mediaType: 'photo',
    includeBase64: false,
    selectionLimit: 1,
  };

  //addProduct
  const addImages = async () => {
    setButtonEditFooter(false);
    const images = await launchImageLibrary(options);
    const formdata: any = new FormData();
    formdata.append('image', {
      uri: images?.assets?.[0].uri ?? '',
      type: images?.assets?.[0].type ?? '',
      name: images?.assets?.[0].fileName ?? '',
    });
    formdata.append('id', idFooter);
    formdata.append('content', footerPrinter);
    if (images.didCancel) {
      setImageUri(imageUri);
      setButtonEditFooter(true);
    }
    if (images.assets) {
      setButtonEditFooter(false);
      setImageUri(images?.assets?.[0].uri ?? '');
    }
    setFromData(formdata);
  };

  const UpdateImages = async () => {
    let i = 0;
    let isSuccess = false;
    do {
      try {
        i++;
        setButtonEditFooter(true);
        const response = await updateFooter(
          access_token,
          imageUri,
          fromData,
          fromDataNoImage,
        );
        const {status_code} = response.data;
        if (status_code === 200) {
          dispatch(setFlagPrint());
          dispatch(showModalFooter({valid: true, modalFooter: ''}));
          Alert.alert(messageApp, componentAddImagesSuccess);
          isSuccess = true;
        }
      } catch (err) {
        console.log(err);
      }
    } while (!isSuccess && i < 5);
    setButtonEditFooter(false);
    dispatch(showModalFooter({valid: true, modalFooter: ''}));
    dispatch(setFlagFooter());
  };

  const ClearImage = () => {
    setButtonEditFooter(false);
    setImageUri('');
    const formdata: any = new FormData();
    formdata.append('id', idFooter);
    formdata.append('content', footerPrinter);
    formdata.append('delete_image', 1);
    setFromDataNoImage(formdata);
  };

  return (
    <SafeAreaView>
      <View style={modalAddImagesCss.container} whiteBGColor>
        <View
          style={{
            marginTop: heightWindow * 0.05,
          }}>
          <Text fontSize32 textCenter fontWeight700 mainColor>
            {componentAddImagesTitle}
          </Text>
          <View>
            {imageUri === `` || imageUri === null ? (
              ''
            ) : (
              <View style={modalAddImagesCss.btnClearImage} aItemsFlexEnd>
                <TouchableOpacity onPress={ClearImage}>
                  <AntDesign name="closecircle" size={40} color={maincolor} />
                </TouchableOpacity>
              </View>
            )}
            {imageUri === `` || imageUri === null ? (
              ''
            ) : (
              <View aItemsCenter>
                <Image
                  source={{
                    uri: imageUri,
                  }}
                  style={modalAddImagesCss.viewImage}
                />
              </View>
            )}
          </View>
        </View>
        <View>
          <View
            style={modalAddImagesCss.contentImage}
            flDirectionRow
            jContentCenter>
            <TouchableOpacity
              style={[
                modalAddImagesCss.btnAddImage,
                {
                  backgroundColor: maincolor,
                },
              ]}
              onPress={addImages}>
              <Text whiteColor fontSize20 fontWeight600 textCenter>
                {componentAddImagesUpload}
              </Text>
            </TouchableOpacity>
          </View>
          <View flDirectionRow jContentAround>
            <TouchableOpacity
              style={[
                modalAddImagesCss.btnAddImage,
                modalAddImagesCss.view40Percentage,
                {
                  backgroundColor: buttonEditFooter ? graycolor : maincolor,
                },
              ]}
              onPress={UpdateImages}
              disabled={buttonEditFooter}>
              <Text whiteColor fontSize20 fontWeight600 textCenter>
                {componentAddImagesUpdate}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={close}
              style={[
                modalAddImagesCss.btnAddImage,
                modalAddImagesCss.view40Percentage,
                {
                  backgroundColor: maincolor,
                },
              ]}>
              <Text whiteColor fontSize20 fontWeight600 textCenter>
                {staffScreenCancelStaff}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default ModalAddImages;
