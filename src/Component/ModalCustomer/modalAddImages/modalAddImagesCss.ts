import { StyleSheet } from 'react-native';
import { heightWindow, widthWindow, scale } from '../../../utils/style/Reponsive';

export const modalAddImagesCss = StyleSheet.create({
  container: {
    // marginTop: scale(60),
    // backgroundColor: BACKGROUND_COLOR,
    height: '100%',
  },
  viewCenter: {
    alignItems: 'center',
  },
  btnClearImage: {
    bottom: -20,
    zIndex: 1,
  },
  viewImage: {
    width: widthWindow * 0.9,
    height: heightWindow * 0.4,
  },
  contentImage: {
    marginVertical: scale(20),
  },
  btnAddImage: {
    // backgroundColor: MAIN_COLOR,
    width: '90%',
    height: scale(44),
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: scale(5),
  },
  viewBtn: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },

  view40Percentage: {
    width: '40%',
  },
});
