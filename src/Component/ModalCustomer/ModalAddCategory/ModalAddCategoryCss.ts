import { Platform, StyleSheet } from 'react-native';
import { heightWindow, scale, widthWindow } from '../../../utils/style/Reponsive';
import { heightPercentageToDP as hp , widthPercentageToDP as wp} from 'react-native-responsive-screen'

export const ModalADDCategoryCss = StyleSheet.create({
  Modal: {
    width: wp('100%'),
    alignItems: 'center',
    justifyContent: 'center',
  },
  inputEdit: {
    marginVertical: scale(6),
    // width: wp('100%'),
    // height: scale(40),
    // fontSize: scale(12),
    borderRadius: scale(5),
    borderWidth: scale(1),
    ...Platform.select({
      ios: {
        padding: scale(10),
      },
    }),
  },

  modalView: {
    width: '80%',
    margin: scale(20),
    borderRadius: scale(20),
    padding: scale(32),
    alignItems: 'center',
    shadowOffset: {
      width: 0,
      height: scale(2),
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: scale(5),
  },
  button: {
    borderRadius: scale(20),
    padding: scale(10),
    elevation: scale(2),
    marginHorizontal: scale(5),
    paddingHorizontal: 20,
  },

  modalText: {
    marginVertical: scale(8),
  },

  viewContent: {
    height: hp('30%'),
  },

  listItemInput: {
    borderWidth: scale(1),
    borderRadius: scale(8),
    ...Platform.select({
      android: {
        padding: scale(6),
      },
      ios: {
        padding: scale(10),
      },
    }),
  },
  // iconCreateListCategory: {
  //   paddingHorizontal: widthWindow * 0.01,
  // },
  TextModalInput: {
    ...Platform.select({
      android: {
        padding: scale(8),
      },
      ios: {
        padding: scale(0),
      },
    }),
  },
});
