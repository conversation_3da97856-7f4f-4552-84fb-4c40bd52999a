import React, {useCallback, useMemo, useState} from 'react';
import {
  Keyboard,
  KeyboardAvoidingView,
  Modal,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import {useDispatch} from 'react-redux';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import AntDesign from 'react-native-vector-icons/AntDesign';
import {setStateEdit, setFlagProduct} from '../../../Redux/Slide';
import {ModalADDCategoryCss} from './ModalAddCategoryCss';
import {
  fetchListCategory,
  handleCreateCategoryRedux,
  handleDeleteCategoryRedux,
  handleEditCategoryRedux,
  fetchListProduct,
} from '../../../Redux/GetData';
import {Text, View} from '../../index';

import {heightWindow, scale} from '../../../utils/style/Reponsive';
import {useAppSelector} from '../../../hooks';
import {ThunkDispatch} from '@reduxjs/toolkit';
import {languages} from '../../../../src/constants';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import DraggableFlatList, {
  RenderItemParams,
  ScaleDecorator,
} from 'react-native-draggable-flatlist';
import {set} from 'lodash';
import {sortRankListCategories} from '../../../api/handleApi';
interface ICreateCategories {
  [key: string]: string | undefined;
  componentCategoriesTitle?: string;
  componentCategoriesWarning?: string;
  componentCategoriesCreateCategory?: string;
  componentCategoriesCreate?: string;
  componentCategoriesWarningDelete?: string;
  agree?: string;
  cancel?: string;
  messageApp?: string;
  addCategorySuccess?: string;
  fillInBlankCategory?: string;
  addCategoryExistButDelete?: string;
  categoryNameExist?: string;
  addCategoryFail?: string;
  editCategoryBlank?: string;
  editCategorySuccess?: string;
  editCategoryFail?: string;
  deleteCategorySuccess?: string;
  categoryNameNotValid?: string;
  deleteCategoryFail?: string;
}
interface SortRankItem {
  id: string;
  sort_rank: string;
}
interface SortRanks {
  sortedData: SortRankItem[];
}
const ModalAddCategory = () => {
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {maincolor, blackcolor, graycolor, backgroundcolor, whitecolor} =
    xmlData;

  const [categoryName, setCategoryName] = useState<string>('');
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [idCategory, setIdCategory] = useState<number>(0);
  const [checkValid, setCheckValid] = useState<boolean>(true);
  const [checkValid1, setCheckValid1] = useState<boolean>(true);
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [valueCategory, setValueCategory] = useState<string>('');
  const [isEditCategory, setIsEditCategory] = useState<boolean>(false);
  useCallback(() => {
    if (categoryName.trim() === '') {
      setIsEditCategory(true);
    } else {
      setIsEditCategory(false);
    }
  }, [categoryName]);

  const access_token = useAppSelector(state => state.counter.access_token);
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): ICreateCategories | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: ICreateCategories = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    componentCategoriesTitle,
    componentCategoriesCreateCategory,
    componentCategoriesCreate,
    componentCategoriesWarningDelete,
    agree,
    cancel,
    messageApp,
    addCategorySuccess,
    fillInBlankCategory,
    addCategoryExistButDelete,
    categoryNameExist,
    addCategoryFail,
    editCategoryBlank,
    editCategorySuccess,
    editCategoryFail,
    deleteCategorySuccess,
    categoryNameNotValid,
    deleteCategoryFail,
    RestoreCategories,
    CategoryExist,
  }: any = langData;
  const listCategory =
    useAppSelector(state => state.getData.listCategory) || [];
  // console.log("listCategory", listCategory);
  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  //handle get all category
  const handleUpdateCategory = (e: string) => {
    setCategoryName(e);
    e ? setCheckValid(false) : setCheckValid(true);
    setCheckValid1(!checkValid1);
  };
  //handle Create category
  const handleCreateCategory =  async (categoryName: string) => {
    try {
      await dispatch(
        handleCreateCategoryRedux({
          access_token,
          categoryName,
          messageApp,
          addCategorySuccess,
          fillInBlankCategory,
          addCategoryExistButDelete,
          categoryNameExist,
          addCategoryFail,
          agree,
          cancel,
          RestoreCategories,
          CategoryExist,
        }),
      );
      await dispatch(fetchListCategory({access_token, messageApp}));
      dispatch(setFlagProduct());
      setCheckValid(true);
      setCategoryName('');
      setCheckValid1(!checkValid1);
    } catch (err: unknown) {
      console.log(err);
    }
  };

  //handle delete category
  const deleleCategory = async () => {
    dispatch(
      handleDeleteCategoryRedux({
        idCategory,
        access_token,
        deleteCategorySuccess,
        messageApp,
        categoryNameNotValid,
        deleteCategoryFail,
      }),
    );
    setModalVisible(!modalVisible);
    setCheckValid1(!checkValid1);
    dispatch(setFlagProduct());
    setCheckValid(true);
    dispatch(fetchListCategory({access_token, messageApp}));
    dispatch(fetchListProduct({access_token, messageApp}));
  };
  const handleShowEditCategory = (item: {key: number}) => {
    const idCategory = item.key;
    setIdCategory(item.key);
    const categoryName = listCategory.find(
      (item: {key: number}) => item.key === idCategory,
    )?.value;
    setValueCategory(categoryName);
    setIsEdit(!isEdit);
  };
  //handle edit category
  const handleEditCategory = async (item: {key: number}) => {
    const idCategory = item.key;
    await dispatch(
      handleEditCategoryRedux({
        access_token,
        idCategory,
        valueCategory,
        messageApp,
        editCategoryBlank,
        editCategorySuccess,
        categoryNameExist,
        addCategoryExistButDelete,
        editCategoryFail,
        agree,
        cancel,
        CategoryExist,
      }),
    );
    dispatch(setFlagProduct());
    setTimeout(() => {
      setIsEdit(!isEdit);
    }, 1000);
    //Thêm clear timeout để tránh lỗi
    setValueCategory('');
    setCheckValid1(!checkValid1);
    setCheckValid(true);
    setTimeout(() => {
      setIsEdit(!isEdit);
    }, 1000);
  };

  const getModalDelete = (item: {key: React.SetStateAction<number>}) => {
    setIdCategory(item.key);
    setModalVisible(!modalVisible);
  };
  const handleInputChange = (text: string, id: number) => {
    //   setInputValues((prevValues) => ({ ...prevValues, [id]: text }));
    //   setDisabledStates((prevStates) => ({ ...prevStates, [id]: text.trim() === '' }));
    //   setChangeValue(text);
    setValueCategory(text);
  };
  const NUM_ITEMS = listCategory.length || 0;

  type Item = {
    key: any;
    id: string;
    value: any;
  };

  const initialData: Item[] = [...Array(NUM_ITEMS)].map((d, index) => {
    return {
      key: `item-${index}`,
      id: `item-${index}`,
      value: `Item ${index}`,
    };
  });

  const RenderCardItem = () => {
    const [data, setData] = useState(listCategory);
    const stableListProduct = useMemo(() => listCategory, [listCategory]);
    const debounceSetSortedData = useCallback(
      async (updatedSortRanks: SortRanks) => {
        await sortRankListCategories(access_token, updatedSortRanks);
        await dispatch(fetchListCategory({access_token, messageApp}));
      },
      [],
    );
    const renderItem = useCallback(
      ({item, drag, isActive}: RenderItemParams<Item>) => {
        return (
          <ScaleDecorator>
            <TouchableOpacity onLongPress={drag} disabled={isActive}>
              <View key={item.key} flDirectionRow aItemsCenter mVertical2>
                <View
                  style={{
                    width: wp('75%'),
                  }}>
                  <Text>{item.value}</Text>
                </View>
                <View
                  style={{
                    width: wp('8%'),
                    marginHorizontal: wp('1%'),
                  }}>
                  <TouchableOpacity
                    onPress={() => handleShowEditCategory(item)}>
                    <FontAwesome
                      name="pencil-square-o"
                      size={scale(32)}
                      color={maincolor}
                    />
                  </TouchableOpacity>
                </View>
                <View
                  style={{
                    width: wp('8%'),
                  }}>
                  <TouchableOpacity onPress={() => getModalDelete(item)}>
                    <AntDesign
                      name="delete"
                      size={scale(32)}
                      color={maincolor}
                    />
                  </TouchableOpacity>
                </View>
              </View>
            </TouchableOpacity>
          </ScaleDecorator>
        );
      },
      [stableListProduct, fetchListCategory],
    );

    return (
      <DraggableFlatList
        data={data}
        onDragEnd={({data: newData}) => {
          setData(newData);
          const updatedSortRanks: SortRanks = {
            sortedData: newData.map((item: Item, index: number) => {
              const id: string = item.key || '';
              return {
                id: id.toString(),
                sort_rank: (index + 1).toString(),
              };
            }),
          };
          debounceSetSortedData(updatedSortRanks);
        }}
        keyExtractor={item => item.key}
        renderItem={renderItem}
      />
    );
  };

  return (
    <View>
      <TouchableOpacity
        style={{
          zIndex: 1,
          alignSelf: 'flex-end',
          backgroundColor: backgroundcolor,
          borderRadius: scale(50),
          position: 'absolute',
          top: -16,
        }}
        onPress={() => dispatch(setStateEdit({valid: true, nameOpiton: ''}))}>
        <AntDesign name="closecircle" size={40} color={maincolor} />
      </TouchableOpacity>
      <KeyboardAvoidingView contentContainerStyle={ModalADDCategoryCss.Modal}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View
            style={{
              borderRadius: scale(20),
              height: hp('55%'),
              paddingHorizontal: scale(4),
              paddingVertical: scale(20),
            }}
            whiteBGColor>
            <View>
              <View style={ModalADDCategoryCss.viewContent}>
                <Text
                  fontSize28
                  fontWeight700
                  textCenter
                  mainColor
                  marginBottom4>
                  {componentCategoriesTitle}
                </Text>
                <RenderCardItem />
              </View>
              <View
                style={{
                  marginTop: scale(50),
                }}>
                <Text>{componentCategoriesCreateCategory}</Text>
                <View
                  style={[
                    ModalADDCategoryCss.inputEdit,
                    {
                      borderColor: maincolor,
                      backgroundColor: backgroundcolor,
                    },
                  ]}>
                  <TextInput
                    onChangeText={e => handleUpdateCategory(e)}
                    style={[
                      ModalADDCategoryCss.TextModalInput,
                      {
                        color: blackcolor,
                      },
                    ]}
                    defaultValue={categoryName}
                  />
                </View>
              </View>
              <View flDirectionRow jContentCenter mTop10>
                <TouchableOpacity
                  style={{
                    backgroundColor: checkValid ? graycolor : maincolor,
                    borderColor: checkValid ? graycolor : maincolor,
                    width: '100%',
                    height: heightWindow * 0.05,
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderWidth: 1,
                    borderRadius: 12,
                  }}
                  onPress={() => handleCreateCategory(categoryName)}
                  disabled={checkValid}>
                  <Text whiteColor fontSize18 fontWeight800>
                    {componentCategoriesCreate}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
            <View aItemsCenter jContentCenter>
              <Modal
                animationType="fade"
                transparent={true}
                visible={modalVisible}>
                <View aItemsCenter jContentCenter flex1>
                  <View
                    style={[
                      ModalADDCategoryCss.modalView,
                      {
                        backgroundColor: backgroundcolor,
                        shadowColor: blackcolor,
                      },
                    ]}>
                    <Text
                      fontSize18
                      fontWeight600
                      mainColor
                      style={ModalADDCategoryCss.modalText}>
                      {componentCategoriesWarningDelete}
                    </Text>
                    <View flDirectionRow>
                      <TouchableOpacity
                        style={[
                          ModalADDCategoryCss.button,
                          {
                            backgroundColor: maincolor,
                          },
                        ]}
                        onPress={deleleCategory}>
                        <Text whiteColor fontWeightBold textCenter>
                          {agree}
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[
                          ModalADDCategoryCss.button,
                          {
                            backgroundColor: maincolor,
                          },
                        ]}
                        onPress={() => setModalVisible(!modalVisible)}>
                        <Text whiteColor fontWeightBold textCenter>
                          {cancel}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </Modal>
            </View>
            <View aItemsCenter jContentCenter>
              <Modal animationType="fade" transparent={true} visible={isEdit}>
                <View aItemsCenter jContentCenter flex1>
                  <View
                    style={[
                      ModalADDCategoryCss.modalView,
                      {
                        backgroundColor: backgroundcolor,
                        shadowColor: blackcolor,
                      },
                    ]}>
                    <TextInput
                      style={[
                        ModalADDCategoryCss.TextModalInput,
                        {
                          color: blackcolor,
                          padding: scale(10),
                          borderWidth: 1,
                          marginVertical: scale(10),
                          width: wp('70%'),
                          borderColor: maincolor,
                        },
                      ]}
                      value={valueCategory}
                      onChangeText={text => handleInputChange(text, idCategory)}
                    />
                    <View flDirectionRow>
                      <TouchableOpacity
                        style={[
                          ModalADDCategoryCss.button,
                          {
                            backgroundColor: isEditCategory
                              ? graycolor
                              : maincolor,
                          },
                        ]}
                        disabled={isEditCategory}
                        onPress={() => {
                          handleEditCategory({key: idCategory});
                        }}>
                        <Text
                          whiteColor
                          fontWeightBold
                          textCenter
                          style={{
                            color: backgroundcolor,
                          }}>
                          {agree}
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[
                          ModalADDCategoryCss.button,
                          {
                            backgroundColor: maincolor,
                          },
                        ]}
                        onPress={() => setIsEdit(!isEdit)}>
                        <Text whiteColor fontWeightBold textCenter>
                          {cancel}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </Modal>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </View>
  );
};

export default ModalAddCategory;
