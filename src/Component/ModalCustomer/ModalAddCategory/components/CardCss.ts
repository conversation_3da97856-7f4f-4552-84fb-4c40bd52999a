import { Platform, StyleSheet } from 'react-native';
import { heightPercentageToDP as hp , widthPercentageToDP as wp} from 'react-native-responsive-screen'
import { scale } from '../../../../utils/style/Reponsive';

export const CardCss = StyleSheet.create({
    listItemInput: {
        borderWidth: scale(1),
        borderRadius: scale(8),
        ...Platform.select({
          android: {
            padding: scale(6),
          },
          ios: {
            padding: scale(10),
          },
        }),
      },
})