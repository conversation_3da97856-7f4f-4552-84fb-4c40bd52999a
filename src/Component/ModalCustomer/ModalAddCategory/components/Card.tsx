// import {Alert,  TextInput, TouchableOpacity} from 'react-native';
// import {useState} from 'react';
// import { View} from '../../../index';
// import {useAppSelector} from '../../../../hooks';
// import {
//   widthPercentageToDP as wp,
// } from 'react-native-responsive-screen';
// import FontAwesome from 'react-native-vector-icons/FontAwesome';
// import AntDesign from 'react-native-vector-icons/AntDesign';
// import {CardCss} from './CardCss';
// import { scale } from '../../../../utils/style/Reponsive';
// import {ThunkDispatch} from '@reduxjs/toolkit';
// import {useDispatch} from 'react-redux';
// import {
//   fetchListCategory,
//   handleEditCategoryRedux,
// } from '../../../../Redux/GetData';
// import {setFlagProduct} from '../../../../Redux/Slide';
// import {languages} from '../../../../../src/constants';

// const Card = (props: {
//   item: any;
//   getModalDelete: any;
// }) => {
//   const {
//     item,
//     getModalDelete,
//   } = props;
//   const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  
//   const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
//   const {maincolor, blackcolor} = xmlData;
//     const [inputValues, setInputValues] = useState<{ [key: number]: string }>({});
//     const [disabledStates, setDisabledStates] = useState<boolean>(true)
//     const [changeValue, setChangeValue] = useState<string>('');
//     const access_token = useAppSelector(state => state.counter.access_token);
//       const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
//       const xmlDataLang = useAppSelector(state => state.getData.xmlData);
//       const selectedLang = (languages[isCheckLang as number] || {})?.lang;
    
//     const checkLangue = (): any | null => {
//       const langData = languages[isCheckLang as number];
//       if (langData) {
//         const result: any = {};
//         langData.fields.forEach((field: string) => {
//           result[field] = xmlDataLang?.[selectedLang]?.[field];
//         });
//         return result;
//       }
//       return null;
//     };
//     const langData = checkLangue();
//     const {
//       agree,
//       cancel,
//       addCategoryExistButDelete,
//       categoryNameExist,
//       editCategoryBlank,
//       editCategorySuccess,
//       editCategoryFail,
//       CategoryExist,
//       messageApp,
//       componentCategoriesWarning
//     }: any = langData;
//     //handle edit category
//     const handleEditCategory = async (item: {key: number}) => {
//       const idCategory = item.key;
//       dispatch(
//         handleEditCategoryRedux({
//           access_token,
//           idCategory,
//           changeValue,
//           messageApp,
//           editCategoryBlank,
//           editCategorySuccess,
//           categoryNameExist,
//           addCategoryExistButDelete,
//           editCategoryFail,
//           agree,
//           cancel,
//           CategoryExist,
          
//         }),
//       );
//       setInputValues((prevValues) => ({ ...prevValues, [idCategory]: '' }));
//       setDisabledStates((prevStates) => ({ ...prevStates, [idCategory]: true }));
//       dispatch(setFlagProduct());
//       dispatch(fetchListCategory({access_token, messageApp}));
//     };
//     const handleInputChange = (text: string, id: number) => {
//       console.log("first", text, id)
//       // setInputValues((prevValues) => ({ ...prevValues, [id]: text }));
//       // setDisabledStates((prevStates) => ({ ...prevStates, [id]: text.trim() === '' }));
//       // setChangeValue(text);
//     };
//   return (
//     <View  flDirectionRow aItemsCenter mVertical2>
//       <View
//         style={{
//           width: wp('75%'),
//         }}
//         >
//         <TextInput
//           style={[
//             CardCss.listItemInput,
//             {
//               color: blackcolor,
//               borderColor: maincolor,
//             },
//           ]}
//           onChangeText={text => handleInputChange(text, item.key)}
//           placeholderTextColor="silver"
//           underlineColorAndroid="transparent"
//           value={item.value}
//           defaultValue={item.value}
//         />
//       </View>
//       <View
//         style={{
//           width: wp('8%'),
//           marginHorizontal: wp('1%'),
//         }}>
//         <TouchableOpacity
//           onPress={() => {
//             changeValue == null
//               ? Alert.alert(messageApp, componentCategoriesWarning)
//               : handleEditCategory(item);
//           }}
//           disabled={disabledStates}>
//           <FontAwesome
//             name="pencil-square-o"
//             size={scale(32)}
//             color={disabledStates ? 'gray' : maincolor}
//           />
//         </TouchableOpacity>
//       </View>
//       <View
//         style={{
//           width: wp('8%'),
//         }}>
//         <TouchableOpacity onPress={() => getModalDelete(item)}>
//           <AntDesign name="delete" size={scale(32)} color={maincolor} />
//         </TouchableOpacity>
//       </View>
//     </View>
//   );
// };

// export default Card;
