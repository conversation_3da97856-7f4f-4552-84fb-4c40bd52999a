import { Platform, StyleSheet } from 'react-native';
import { scale } from '../../../utils/style/Reponsive';
export const ModalCreateAcencyCss = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: scale(12),
    left: 0,
    ...Platform.select({
      android: {
      },
      ios: {
        marginTop: scale(30),
      },
    }),
    top: 0,
  },
  inputEdit: {
    marginVertical: scale(2),
    height: scale(40),
    fontSize: scale(14),
    borderRadius: scale(5),
    borderWidth: 1,
    ...Platform.select({
      android: {
        // padding: scale(10),
      },
      ios: {
        padding: scale(10),
      },
    }),
  },
  inputContact: {
    marginVertical: scale(2),
    height: scale(60),
    fontSize: scale(12),
    borderRadius: scale(5),
    borderWidth: 1,
    ...Platform.select({
      ios: {
        padding: scale(10),
      },
    }),
  },

  btnSendInfo: {
    marginRight: scale(10),
    width: scale(100),
    height: scale(48),
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: scale(12),
  },
  btnModalClose: {
    backgroundColor: 'rgba(255,0,0,0.65)',
    borderColor: 'rgba(255,0,0,0.5)',
    marginRight: scale(10),
    width: scale(100),
    height: scale(48),
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderRadius: scale(12),
  },

  TextModalInput: {
    paddingLeft: scale(4),
    fontSize: scale(14),
  },
});
