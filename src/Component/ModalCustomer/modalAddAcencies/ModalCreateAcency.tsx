import {
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Keyboard,
  Alert,
} from 'react-native';
import {useDispatch} from 'react-redux';
import {Formik} from 'formik';
import {Text, View} from '../../index';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import {ModalCreateAcencyCss} from './ModalCreateAcencyCss';
import {setStateEdit} from '../../../Redux/Slide';

import * as Yup from 'yup';
import {ThunkDispatch} from '@reduxjs/toolkit';
import {useAppSelector} from '../../../hooks';
import {languages} from '../../../constants';
import {createAcency} from '../../../api/handleApi';
import {getListAgencies} from '../../../Redux/GetData';
interface ISendInfo {
  [key: string]: string | undefined;
  minValidMessageNumberPhone?: string;
  maxValidMessageNumberPhone?: string;
  fieldIsBlank?: string;
  matchesNumberPhone?: string;
  minValidMessageName?: string;
  maxValidMessageName?: string;
  minValidMessageAddress?: string;
  maxValidMessageAddress?: string;
  minValidMessagePSendInfo?: string;
  componentSendInfo?: string;
  componentCustomersFullName?: string;
  componentCustomersPhone?: string;
  staffScreenAddressStaff?: string;
  componentSendInfoContent?: string;
  staffScreenCancelStaff?: string;
  send?: string;
}
const ModalAddCategory = () => {
  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {
    minValidNumber,
    maxValidNumberPhone,
    matchNumberPhone,
    maxValidNumber,
    maxValidSendInfo,
    blackcolor,
    maincolor,
    graycolor,
  } = xmlData;
  const regexPattern = matchNumberPhone;
  const regexPhone = new RegExp(regexPattern);
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const access_token = useAppSelector(state => state.counter.access_token);

  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): ISendInfo | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: ISendInfo = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    minValidMessageNumberPhone,
    maxValidMessageNumberPhone,
    fieldIsBlank,
    matchesNumberPhone,
    minValidMessageName,
    maxValidMessageName,
    minValidMessageAddress,
    maxValidMessageAddress,
    minValidMessagePSendInfo,
    staffScreenCancelStaff,
    send,
    maxValidMessagePSendInfo,
    TitleInfoAgency,
    CompanyName,
    AgencyTaxCode,
    AgencyAddress,
    AgencyRepresentative,
    AgencyEmail,
    AgencyNote,
    CreateAgencySuccess,
    CreateAgencyFail,
    messageApp,
    AgencyNameInfo,
    AgencyPhoneNumberInfo,
  }: any = langData;

  const SignupSchema = Yup.object().shape({
    companyName: Yup.string()
      .min(minValidNumber, minValidMessageName)
      .max(maxValidNumber, maxValidMessageName)
      .required(fieldIsBlank),
    acencyName: Yup.string()
      .min(minValidNumber, minValidMessageName)
      .max(maxValidNumber, maxValidMessageName)
      .required(fieldIsBlank),
    tax: Yup.string()
      .min(minValidNumber, minValidMessageName)
      .max(maxValidNumber, maxValidMessageName)
      .required(fieldIsBlank),
    contactName: Yup.string()
      .min(minValidNumber, minValidMessageAddress)
      .max(maxValidNumber, maxValidMessageAddress)
      .required(fieldIsBlank),
    email: Yup.string()
      .min(minValidNumber, minValidMessagePSendInfo)
      .max(maxValidSendInfo, maxValidMessagePSendInfo)
      .required(fieldIsBlank),
    phone: Yup.string()
      .min(minValidNumber, minValidMessageNumberPhone)
      .max(maxValidNumberPhone, maxValidMessageNumberPhone)
      .required(fieldIsBlank)
      .matches(regexPhone, matchesNumberPhone),
    address: Yup.string()
      .min(minValidNumber, minValidMessageAddress)
      .max(maxValidNumber, maxValidMessageAddress)
      .required(fieldIsBlank),
    note: Yup.string()
      .min(minValidNumber, minValidMessagePSendInfo)
      .max(maxValidSendInfo, maxValidMessagePSendInfo)
      .required(fieldIsBlank),
  });
  const createAcencies = async (values: {
    companyName: string;
    acencyName: string;
    tax: string;
    address: string;
    contactName: string;
    email: string;
    phone: string;
    note: string;
  }) => {
    createAcency(values, access_token).then(res => {
      if (res.status === 200) {
        Alert.alert(messageApp, CreateAgencySuccess);
        dispatch(
          setStateEdit({
            valid: true,
            nameOpiton: '',
          }),
        );
        dispatch(getListAgencies({access_token, messageApp}));
      } else {
        Alert.alert(messageApp, CreateAgencyFail);
      }
    });
  };

  return (
    <KeyboardAwareScrollView
      style={ModalCreateAcencyCss.container}
      contentContainerStyle={{flexGrow: 1}}
      keyboardShouldPersistTaps="handled">
      <View flex1 jContentCenter>
        <Formik
          initialValues={{
            companyName: '',
            acencyName: '',
            tax: '',
            address: '',
            contactName: '',
            email: '',
            phone: '',
            note: '',
          }}
          onSubmit={() => {}}
          validationSchema={SignupSchema}>
          {({
            handleChange,
            setFieldTouched,
            values,
            errors,
            touched,
            isValid,
            dirty,
          }) => (
            <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
              <View whiteBGColor bRadius20 p_20>
                <Text fontSize28 textCenter fontWeight700 mainColor>
                  {TitleInfoAgency}
                </Text>
                <View>
                  <Text blackColor>{CompanyName}</Text>
                  <View
                    style={ModalCreateAcencyCss.inputEdit}
                    borderMainColor
                    whiteBGColor>
                    <TextInput
                      style={[
                        ModalCreateAcencyCss.TextModalInput,
                        {
                          color: blackcolor,
                        },
                      ]}
                      onChangeText={handleChange('companyName')}
                      onBlur={() => setFieldTouched('companyName')}
                      value={values.companyName}
                    />
                  </View>
                  {errors.companyName && touched.companyName ? (
                    <Text paddingLeft6 redText>
                      {errors.companyName}
                    </Text>
                  ) : null}
                </View>
                <View>
                  <Text blackColor>{AgencyNameInfo}</Text>
                  <View
                    style={ModalCreateAcencyCss.inputEdit}
                    borderMainColor
                    whiteBGColor>
                    <TextInput
                      style={[
                        ModalCreateAcencyCss.TextModalInput,
                        {
                          color: blackcolor,
                        },
                      ]}
                      onChangeText={handleChange('acencyName')}
                      onBlur={() => setFieldTouched('acencyName')}
                      value={values.acencyName}
                    />
                  </View>
                  {errors.acencyName && touched.acencyName ? (
                    <Text paddingLeft6 redText>
                      {errors.acencyName}
                    </Text>
                  ) : null}
                </View>

                <View>
                  <Text blackColor>{AgencyTaxCode}</Text>
                  <View
                    style={ModalCreateAcencyCss.inputEdit}
                    borderMainColor
                    whiteBGColor>
                    <TextInput
                      style={[
                        ModalCreateAcencyCss.TextModalInput,
                        {
                          color: blackcolor,
                        },
                      ]}
                      onChangeText={handleChange('tax')}
                      onBlur={() => setFieldTouched('tax')}
                      value={values.tax}
                    />
                  </View>
                  {errors.tax && touched.tax ? (
                    <Text paddingLeft6 redText>
                      {errors.tax}
                    </Text>
                  ) : null}
                </View>
                <View>
                  <Text blackColor>{AgencyAddress}</Text>
                  <View
                    style={ModalCreateAcencyCss.inputEdit}
                    borderMainColor
                    whiteBGColor>
                    <TextInput
                      style={[
                        ModalCreateAcencyCss.TextModalInput,
                        {
                          color: blackcolor,
                        },
                      ]}
                      onChangeText={handleChange('address')}
                      onBlur={() => setFieldTouched('address')}
                      value={values.address}
                    />
                  </View>
                  {errors.address && touched.address ? (
                    <Text paddingLeft6 redText>
                      {errors.address}
                    </Text>
                  ) : null}
                </View>
                <View>
                  <Text blackColor>{AgencyRepresentative}</Text>
                  <View
                    style={ModalCreateAcencyCss.inputEdit}
                    borderMainColor
                    whiteBGColor>
                    <TextInput
                      style={[
                        ModalCreateAcencyCss.TextModalInput,
                        {
                          color: blackcolor,
                        },
                      ]}
                      onChangeText={handleChange('contactName')}
                      onBlur={() => setFieldTouched('contactName')}
                      value={values.contactName}
                    />
                  </View>
                  {errors.contactName && touched.contactName ? (
                    <Text paddingLeft6 redText>
                      {errors.contactName}
                    </Text>
                  ) : null}
                </View>
                <View>
                  <Text blackColor>{AgencyEmail}</Text>
                  <View
                    style={ModalCreateAcencyCss.inputEdit}
                    borderMainColor
                    whiteBGColor>
                    <TextInput
                      style={ModalCreateAcencyCss.TextModalInput}
                      onChangeText={handleChange('email')}
                      onBlur={() => setFieldTouched('email')}
                      value={values.email}
                      keyboardType={'numeric'}
                    />
                  </View>
                  {errors.email && touched.email ? (
                    <Text redText paddingLeft6>
                      {errors.email}
                    </Text>
                  ) : null}
                </View>
                <View>
                  <Text blackColor>{AgencyPhoneNumberInfo}</Text>
                  <View
                    style={ModalCreateAcencyCss.inputEdit}
                    borderMainColor
                    whiteBGColor>
                    <TextInput
                      style={ModalCreateAcencyCss.TextModalInput}
                      onChangeText={handleChange('phone')}
                      onBlur={() => setFieldTouched('phone')}
                      value={values.phone}
                    />
                  </View>
                  {errors.phone && touched.phone ? (
                    <Text redText paddingLeft6>
                      {errors.phone}
                    </Text>
                  ) : null}
                </View>
                <View>
                  <Text blackColor>{AgencyNote}</Text>
                  <View
                    style={ModalCreateAcencyCss.inputContact}
                    borderMainColor
                    whiteBGColor>
                    <TextInput
                      multiline
                      numberOfLines={4}
                      onChangeText={handleChange('note')}
                      onBlur={() => setFieldTouched('note')}
                      value={values.note}
                      style={ModalCreateAcencyCss.TextModalInput}
                    />
                  </View>
                  {errors.note && touched.note ? (
                    <Text redText paddingLeft6>
                      {errors.note}
                    </Text>
                  ) : null}
                </View>
                <View flDirectionRow jContentCenter mVertical6>
                  <TouchableOpacity
                    style={[
                      ModalCreateAcencyCss.btnSendInfo,
                      {
                        backgroundColor:
                          !isValid || !dirty ? graycolor : maincolor,
                      },
                    ]}
                    onPress={() => createAcencies(values)}
                    disabled={!isValid || !dirty}>
                    <Text whiteColor fontSize18 fontWeight600>
                      {send}
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() =>
                      dispatch(
                        setStateEdit({
                          valid: true,
                          nameOpiton: '',
                        }),
                      )
                    }
                    style={ModalCreateAcencyCss.btnModalClose}>
                    <Text whiteColor fontSize18 fontWeight600>
                      {staffScreenCancelStaff}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </TouchableWithoutFeedback>
          )}
        </Formik>
      </View>
    </KeyboardAwareScrollView>
  );
};

export default ModalAddCategory;
