import {SafeAreaView, TouchableOpacity, Image, Alert} from 'react-native';
import React, {useState} from 'react';
import {useDispatch} from 'react-redux';

import AntDesign from 'react-native-vector-icons/AntDesign';
import {setFlag, setStateShowOption, setUpdateLogo} from '../../../Redux/Slide';
import {launchImageLibrary} from 'react-native-image-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {ModalUploadLogoCss} from './ModalUploadLogoCss';
import {useAppSelector} from '../../../hooks';
import {Text, View} from '../../index';
import {ThunkDispatch} from '@reduxjs/toolkit';
import {updateLogoApp} from '../../../api/handleApi';
import {rootInterface} from '../../../interface';
import {languages} from '../../../../src/constants';
import {BASE_URL} from '../../../api/ApiManager';

interface IUploadLogo {
  [key: string]: string | undefined;
  componentUpdateLogoSuccess?: string;
  componentLogoTitle?: string;
  componentLogoUploadLogo?: string;
  handleProductScreenUpdate?: string;
  staffScreenCancelStaff?: string;
}
interface IProps {
  urlImages: string;
}

const ModalUploadLogo = (props: IProps) => {
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {maincolor, graycolor} = xmlData;
  const {urlImages} = props;
  const {infoAdmin, access_token} = useAppSelector(state => state.counter);
  const [imageUri, setImageUri] = useState<string | null>(urlImages);
  const [buttonEditFooter, setButtonEditFooter] = useState<boolean>(true);
  const [fromDataNoImage, setFromDataNoImage] = useState<FormData>(
    new FormData(),
  );
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): IUploadLogo | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: IUploadLogo = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    componentUpdateLogoSuccess,
    componentLogoTitle,
    componentLogoUploadLogo,
    handleProductScreenUpdate,
    staffScreenCancelStaff,
    messageApp,
    upLoadImgFail,
  }: any = langData;
  const [fromData, setFromData] = useState<FormData>(new FormData());
  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const addImages = async () => {
    setButtonEditFooter(false);
    const options: rootInterface['ImageOptions'] = {
      title: 'Select Image',
      type: 'library',
      maxWidth: 200,
      maxHeight: 200,
      quality: 1,
      mediaType: 'photo',
      includeBase64: false,
      selectionLimit: 1,
    };
    const images = await launchImageLibrary(options);
    const formdata: any = new FormData();
    formdata.append('image', {
      uri: images?.assets?.[0].uri ?? '',
      type: images?.assets?.[0].type ?? '',
      name: images?.assets?.[0].fileName ?? '',
    });
    formdata.append('id', infoAdmin.admin.id);
    formdata.append('storename', infoAdmin.admin.storename);
    formdata.append('address', infoAdmin.admin.address);
    formdata.append('name', infoAdmin.admin.name);
    if (images.didCancel) {
      setImageUri(imageUri);
      setButtonEditFooter(true);
    }
    if (images.assets) {
      setButtonEditFooter(false);
      setImageUri(images?.assets?.[0].uri ?? '');
    }

    setFromData(formdata);
  };

  const UpdateImages = async () => {
    let i = 0;
    do {
      try {
        setButtonEditFooter(true);
        const response = await updateLogoApp(
          access_token,
          imageUri,
          fromData,
          fromDataNoImage,
        );
        const {status_code, admin} = response.data;
        if (status_code === 200) {
          dispatch(setUpdateLogo(admin.image));
          await AsyncStorage.setItem('LogoApp', JSON.stringify(admin.image));
          if (imageUri !== null) {
            AsyncStorage.removeItem('LogoApp');
            AsyncStorage.setItem('LogoApp', JSON.stringify(admin.image));
          }
          dispatch(setFlag());
          Alert.alert(messageApp, componentUpdateLogoSuccess);
          dispatch(setStateShowOption({valid: true, nameOpiton: ''}));
          break;
        }
      } catch (err: unknown) {
        console.log(err);
      }
      i++;
    } while (i < 5);
    if (i === 5) {
      Alert.alert(messageApp, upLoadImgFail);
    }
  };

  const ClearImage = async () => {
    setButtonEditFooter(false);
    setImageUri(null);
    const formdata: any = new FormData();
    formdata.append('id', infoAdmin.admin.id);
    formdata.append('storename', infoAdmin.admin.storename);
    formdata.append('address', infoAdmin.admin.address);
    formdata.append('name', infoAdmin.admin.name);
    formdata.append('delete_image', 1);
    setFromDataNoImage(formdata);
  };

  const close = () => {
    dispatch(
      setStateShowOption({
        valid: true,
        nameOpiton: '',
      }),
    );
  };

  return (
    <SafeAreaView
      style={{
        flex: 1,
      }}>
      <View style={ModalUploadLogoCss.container} whiteBGColor>
        <View style={ModalUploadLogoCss.headerTop}>
          <Text fontSize32 textCenter fontWeight700 mainColor>
            {componentLogoTitle}
          </Text>
          <View aItemsCenter>
            {imageUri === null || imageUri === undefined ? (
              ''
            ) : (
              <View style={ModalUploadLogoCss.btnClearImage}>
                <TouchableOpacity onPress={ClearImage}>
                  <AntDesign name="closecircle" size={40} color={maincolor} />
                </TouchableOpacity>
              </View>
            )}
            {imageUri === null || imageUri === undefined ? (
              <Image
                source={{
                  uri: `${BASE_URL}/image/default_logo.png`,
                }}
                style={ModalUploadLogoCss.imageContent}
                resizeMode="contain"
              />
            ) : (
              <Image
                source={{
                  uri: imageUri,
                }}
                style={ModalUploadLogoCss.imageContent}
                resizeMode="contain"
              />
            )}
          </View>
        </View>
        <View>
          <View
            style={ModalUploadLogoCss.viewUploadImage}
            flDirectionRow
            jContentAround>
            <TouchableOpacity
              style={[
                ModalUploadLogoCss.btnAddImages,
                {
                  backgroundColor: maincolor,
                },
              ]}
              onPress={addImages}>
              <Text whiteColor fontSize20 fontWeight600 textCenter>
                {componentLogoUploadLogo}
              </Text>
            </TouchableOpacity>
          </View>
          <View style={ModalUploadLogoCss.footerBtn}>
            <TouchableOpacity
              style={[
                ModalUploadLogoCss.btnHandleImage,
                {
                  backgroundColor: buttonEditFooter ? graycolor : maincolor,
                },
              ]}
              disabled={buttonEditFooter}
              onPress={UpdateImages}>
              <Text whiteColor fontSize20 fontWeight600 textCenter>
                {handleProductScreenUpdate}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={close}
              style={[
                ModalUploadLogoCss.btnHandleImage,
                {
                  backgroundColor: maincolor,
                },
              ]}>
              <Text whiteColor fontSize20 fontWeight600 textCenter>
                {staffScreenCancelStaff}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default React.memo(ModalUploadLogo);
