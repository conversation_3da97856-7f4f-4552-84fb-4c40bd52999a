import { StyleSheet } from 'react-native';
import { heightWindow, widthWindow, scale } from '../../../utils/style/Reponsive';

export const ModalUploadLogoCss = StyleSheet.create({
  container: {
    height: '100%',
    marginTop: scale(20),
  },
  headerTop: {
    marginTop: scale(60),
  },

  btnClearImage: {
    bottom: -20,
    zIndex: 1,
    right: -widthWindow * 0.4,
  },
  imageContent: {
    width: widthWindow * 0.9,
    height: heightWindow * 0.4,
  },
  viewUploadImage: {
    marginVertical: scale(20),
  },
  btnAddImages: {
    width: '90%',
    height: scale(44),
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: scale(5),
  },
  footerBtn: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },

  btnHandleImage: {
    width: '40%',
    height: scale(44),
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: scale(5),
  },
});
