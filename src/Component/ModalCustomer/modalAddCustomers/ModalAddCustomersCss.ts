import { Platform, StyleSheet } from 'react-native';
import { heightWindow, scale } from '../../../utils/style/Reponsive';

export const ModalAddCustomersCss = StyleSheet.create({
  container: {
    ...Platform.select({
      android: {
        height: heightWindow > 838 ? heightWindow * 0.35 : heightWindow * 0.45,
      },
      ios: {
        height: heightWindow * 0.3,
      },
    }),
  },
  btnBottom: {
    width: '100%',
    height: scale(40),
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: scale(5),
  },

  iosPadding: {
    ...Platform.select({
      ios: {
        padding: scale(10),
      },
    }),
  },
});
