import {
  Alert,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import {useCallback} from 'react';
import {ModalAddCustomersCss} from './ModalAddCustomersCss';
import {useDispatch} from 'react-redux';
import {
  getCustomers,
  setFlagCustomers,
  setShowCreateCustomers,
} from '../../../Redux/Slide';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Ionicons from 'react-native-vector-icons/Ionicons';

import {Text, View} from '../../index';

import {createCustomers} from '../../../api/handleApi';
import * as Yup from 'yup';

import {useAppSelector} from '../../../hooks';
import {ThunkDispatch} from '@reduxjs/toolkit';
import {Formik} from 'formik';
import {scale} from '../../../utils/style/Reponsive';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import {languages} from '../../../../src/constants';

interface ICreateCustomers {
  name: string;
  phone: string;
}

interface IAddCustomers {
  [key: string]: string | undefined;
  componentCustomersWarningPhone?: string;
  componentCustomersName?: string;
  componentCustomersPhone?: string;
  componentCustomersCreate?: string;
  createCustomersSuccess?: string;
  fieldIsBlank?: string;
  messageApp?: string;
  matchesNumberPhone?: string;
  maxValidMessageNumberPhone?: string;
  minValidMessageNumberPhone?: string;
  minValidMessageName?: string;
  maxValidMessageName?: string;
}

const ModalAddCustomers = () => {
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {
    maincolor,
    blackcolor,
    graycolor,
    minValidNumber,
    maxValidNumberPhone,
    matchNumberPhone,
    minValidPassword,
    maxValidNumber,
  } = xmlData;
  const token = useAppSelector(state => state.counter.access_token);
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): IAddCustomers | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: IAddCustomers = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    componentCustomersWarningPhone,
    componentCustomersName,
    componentCustomersPhone,
    componentCustomersCreate,
    createCustomersSuccess,
    fieldIsBlank,
    messageApp,
    matchesNumberPhone,
    maxValidMessageNumberPhone,
    minValidMessageNumberPhone,
    minValidMessageName,
    maxValidMessageName,
  }: any = langData;
  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const regexPattern = matchNumberPhone;
  const regexPhone = new RegExp(regexPattern);
  const SignupSchema = Yup.object().shape({
    phone: Yup.string()
      .min(minValidNumber, minValidMessageNumberPhone)
      .max(maxValidNumberPhone, maxValidMessageNumberPhone)
      .required(fieldIsBlank)
      .matches(regexPhone, matchesNumberPhone),
    name: Yup.string()
      .min(minValidPassword, minValidMessageName)
      .max(maxValidNumber, maxValidMessageName)
      .required(fieldIsBlank),
  });
  const handleCloseCreateCustomerModal = useCallback(() => {
    dispatch(setShowCreateCustomers({valid: true, nameOpiton: ''}));
  }, [dispatch, setShowCreateCustomers]);

  const createCustomerss = (values: ICreateCustomers) => {
    const {name, phone} = values;
    dispatch(setFlagCustomers());
    try {
      createCustomers(name, phone, token)
        .then(res => {
          if (res.data.status_code === 200) {
            dispatch(setShowCreateCustomers({valid: true, nameOpiton: ''}));
            dispatch(getCustomers());
            Alert.alert(messageApp, createCustomersSuccess);
          }
          if (res.data.status_code === 399) {
            Alert.alert(messageApp, componentCustomersWarningPhone);
          }
        })
        .catch((err: string) => {
          console.log(err);
        })
        .finally(() => {
          dispatch(setFlagCustomers());
        });
    } catch (err: unknown) {
      console.log(err);
    }
  };

  return (
    <KeyboardAvoidingView
      style={ModalAddCustomersCss.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <Formik
        initialValues={{
          phone: '',
          name: '',
        }}
        validationSchema={SignupSchema}
        onSubmit={createCustomerss}>
        {({
          handleChange,
          values,
          errors,
          touched,
          isValid,
          dirty,
          setFieldTouched,
        }) => (
          <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <View
              style={{
                backgroundColor: 'white',
                borderRadius: scale(20),
              }}>
              <View w_100 flDirectionRow>
                <View w_90 />
                <View w_10>
                  <TouchableOpacity
                    style={{
                      zIndex: 1,
                      top: -20,
                    }}
                    onPress={handleCloseCreateCustomerModal}>
                    <AntDesign
                      name="closecircle"
                      size={scale(40)}
                      color={maincolor}
                    />
                  </TouchableOpacity>
                </View>
              </View>
              <View
                style={{
                  marginHorizontal: scale(8),
                }}>
                <View>
                  <View
                    borderMainColor
                    aItemsCenter
                    borderWidth1
                    flDirectionRow
                    bRadius5
                    style={{
                      marginVertical: scale(8),
                    }}>
                    <Ionicons
                      name="people"
                      size={30}
                      style={[
                        {
                          color: maincolor,
                          marginHorizontal: scale(10),
                        },
                      ]}
                    />
                    <TextInput
                      placeholder={componentCustomersName}
                      style={{
                        color: blackcolor,
                        padding: scale(12),
                        flex: 1,
                      }}
                      onChangeText={handleChange('name')}
                      onBlur={() => setFieldTouched('name')}
                      value={values.name}
                      placeholderTextColor={blackcolor}
                    />
                  </View>
                  {errors.name && touched.name ? (
                    <Text
                      paddingLeft6
                      redText
                      style={{
                        paddingVertical: 4,
                      }}>
                      {errors.name}
                    </Text>
                  ) : null}
                  <View
                    borderMainColor
                    aItemsCenter
                    borderWidth1
                    flDirectionRow
                    bRadius5
                    style={{
                      marginVertical: scale(8),
                    }}>
                    <FontAwesome
                      name="phone"
                      size={30}
                      style={[
                        {
                          color: maincolor,
                          marginHorizontal: scale(10),
                        },
                      ]}
                    />
                    <TextInput
                      placeholder={componentCustomersPhone}
                      style={{
                        color: blackcolor,
                        padding: scale(12),
                        flex: 1,
                      }}
                      onChangeText={handleChange('phone')}
                      onBlur={() => setFieldTouched('phone')}
                      value={values.phone}
                      keyboardType={'numeric'}
                      placeholderTextColor={blackcolor}
                    />
                  </View>
                  {errors.phone && touched.phone ? (
                    <Text
                      paddingLeft6
                      redText
                      style={{
                        paddingVertical: 4,
                      }}>
                      {errors.phone}
                    </Text>
                  ) : null}
                </View>
                <TouchableOpacity
                  onPress={() => createCustomerss(values)}
                  disabled={!isValid || !dirty}
                  style={[
                    {
                      backgroundColor:
                        !isValid || !dirty ? graycolor : maincolor,
                    },
                    {
                      height: scale(48),
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRadius: scale(4),
                      marginVertical: scale(4),
                      marginBottom: scale(20),
                    },
                  ]}>
                  <Text fontSize22 whiteColor>
                    {componentCustomersCreate}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </TouchableWithoutFeedback>
        )}
      </Formik>
    </KeyboardAvoidingView>
  );
};

export default ModalAddCustomers;
