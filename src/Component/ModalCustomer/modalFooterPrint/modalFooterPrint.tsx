import {Alert, TextInput, TouchableOpacity} from 'react-native';
import React from 'react';
import {
  setFlag,
  setFlagPrint,
  setStateEdit,
  showModalFooter,
} from '../../../Redux/Slide';
import {useDispatch} from 'react-redux';
import {useState} from 'react';
import {Text, View} from '../../index';

import {createFooters} from '../../../api/handleApi';
import {modalFooterPrintCss} from './modalFooterPrintCss';
import {useAppSelector} from '../../../hooks';
import {ThunkDispatch} from '@reduxjs/toolkit';
import {languages} from '../../../../src/constants';

interface IFooter {
  [key: string]: string | undefined;
  componentCreateFooterSuccess?: string;
  componentExistFooter?: string;
  componentFailFooter?: string;
  componentFooterTitle?: string;
  componentCreateTitle?: string;
  componentCloseTitle?: string;
}
interface IProps {
  navigation: {
    pop: (count?: number) => void;
  };
}
const ModalFooterPrint = (props: IProps) => {
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);

  const {navigation} = props;
  const {maincolor, blackcolor, graycolor, backgroundcolor} = xmlData;
  const token = useAppSelector(state => state.counter.access_token);
  const [contentFooter, setContentFooter] = useState<string>('');
  const [buttonEditFooter, setButtonEditFooter] = useState<boolean>(true);

  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): IFooter | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: IFooter = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    componentCreateFooterSuccess,
    componentExistFooter,
    componentFailFooter,
    componentFooterTitle,
    componentCreateTitle,
    componentCloseTitle,
    messageApp,
  }: any = langData;

  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const createFooter = async () => {
    createFooters(contentFooter, token).then(res => {
      if (res.data.message == 'Tạo footer thành công') {
        dispatch(setFlagPrint());
        dispatch(
          setStateEdit({
            valid: true,
            nameOpiton: '',
          }),
        );
        Alert.alert(messageApp, componentCreateFooterSuccess);
        dispatch(setFlag());
        dispatch(showModalFooter({valid: true, modalFooter: ''}));
        navigation.pop(1);
      } else if (res.data.message == 'Cửa hàng đã tồn tại footer!') {
        Alert.alert(messageApp, componentExistFooter);
      } else {
        Alert.alert(messageApp, componentFailFooter);
      }
    });
  };

  const close = () => {
    dispatch(
      showModalFooter({
        valid: true,
        modalFooter: '',
      }),
    );
  };

  const handleCreateFooter = (e: string) => {
    setContentFooter(e);
    e ? setButtonEditFooter(false) : setButtonEditFooter(true);
  };

  return (
    <View
      style={[
        modalFooterPrintCss.container,
        {
          backgroundColor: backgroundcolor,
        },
      ]}>
      <View style={modalFooterPrintCss.headerTop}>
        <Text fontSize32 fontWeight700 mainColor textCenter>
          {componentFooterTitle}
        </Text>
        <View>
          <TextInput
            placeholderTextColor={blackcolor}
            onChangeText={e => handleCreateFooter(e)}
            style={[
              modalFooterPrintCss.textInput,
              {
                borderColor: maincolor,
              },
            ]}
            multiline={true}
            inputMode="text"
            textAlignVertical="top"
          />
        </View>
      </View>
      <View>
        <View flDirectionRow jContentAround>
          <TouchableOpacity
            onPress={createFooter}
            style={[
              modalFooterPrintCss.btnBottom,
              {
                backgroundColor: buttonEditFooter ? graycolor : maincolor,
              },
            ]}
            disabled={buttonEditFooter}>
            <Text whiteColor fontSize20 fontWeight600 textCenter>
              {componentCreateTitle}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={close}
            style={[
              modalFooterPrintCss.btnBottom,
              {
                backgroundColor: maincolor,
              },
            ]}>
            <Text whiteColor fontSize20 fontWeight600 textCenter>
              {componentCloseTitle}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default React.memo(ModalFooterPrint);
