import { StyleSheet } from 'react-native';
import { scale } from '../../../utils/style/Reponsive';

export const modalFooterPrintCss = StyleSheet.create({
  container: {
    height: '100%',
    zIndex: 1,
    backgroundColor: 'white',
  },
  headerTop: {
    marginTop: scale(200),
  },
  textInput: {
    width: '90%',
    borderWidth: scale(2),
    height: scale(160),
    fontSize: scale(18),
    marginBottom: scale(20),
    padding: scale(10),
    paddingTop: scale(14),
    marginLeft: scale(20),
    borderRadius: scale(20),
    marginVertical: scale(50),
  },
  btnBottom: {
    width: '40%',
    height: scale(40),
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: scale(5),
  },
});
