import React from 'react';
import {
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import {useDispatch} from 'react-redux';
import {Formik} from 'formik';
import {Text, View} from '../../index';

import {ModalSendInfoCss} from './ModalSendInfoCss';
import {setStateEdit} from '../../../Redux/Slide';

import * as Yup from 'yup';
import {handleSendInfoContact} from '../../../Redux/GetData';
import {ThunkDispatch} from '@reduxjs/toolkit';
import {useAppSelector} from '../../../hooks';
import {languages} from '../../../../src/constants';

interface ISendInfo {
  [key: string]: string | undefined;
  minValidMessageNumberPhone?: string;
  maxValidMessageNumberPhone?: string;
  fieldIsBlank?: string;
  matchesNumberPhone?: string;
  minValidMessageName?: string;
  maxValidMessageName?: string;
  minValidMessageAddress?: string;
  maxValidMessageAddress?: string;
  minValidMessagePSendInfo?: string;
  componentSendInfo?: string;
  componentCustomersFullName?: string;
  componentCustomersPhone?: string;
  staffScreenAddressStaff?: string;
  componentSendInfoContent?: string;
  staffScreenCancelStaff?: string;
  send?: string;
}
const ModalAddCategory = () => {
  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {
    minValidNumber,
    maxValidNumberPhone,
    matchNumberPhone,
    maxValidNumber,
    maxValidSendInfo,
    blackcolor,
    maincolor,
    graycolor,
  } = xmlData;
  const regexPattern = matchNumberPhone;
  const regexPhone = new RegExp(regexPattern);
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): ISendInfo | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: ISendInfo = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    minValidMessageNumberPhone,
    maxValidMessageNumberPhone,
    fieldIsBlank,
    matchesNumberPhone,
    minValidMessageName,
    maxValidMessageName,
    minValidMessageAddress,
    maxValidMessageAddress,
    minValidMessagePSendInfo,
    componentSendInfo,
    componentCustomersFullName,
    componentCustomersPhone,
    staffScreenAddressStaff,
    componentSendInfoContent,
    staffScreenCancelStaff,
    send,
    messageApp,
    sendInfoSuccess,
    sendInfoFail,
    maxValidMessagePSendInfo,
    createCustomersFail,
  }: any = langData;
  const SignupSchema = Yup.object().shape({
    name: Yup.string()
      .min(minValidNumber, minValidMessageName)
      .max(maxValidNumber, maxValidMessageName)
      .required(fieldIsBlank),
    phone: Yup.string()
      .min(minValidNumber, minValidMessageNumberPhone)
      .max(maxValidNumberPhone, maxValidMessageNumberPhone)
      .required(fieldIsBlank)
      .matches(regexPhone, matchesNumberPhone),
    address: Yup.string()
      .min(minValidNumber, minValidMessageAddress)
      .max(maxValidNumber, maxValidMessageAddress)
      .required(fieldIsBlank),
    infoContact: Yup.string()
      .min(minValidNumber, minValidMessagePSendInfo)
      .max(maxValidSendInfo, maxValidMessagePSendInfo)
      .required(fieldIsBlank),
  });
  const getInfoContact = async (values: {
    name: string;
    phone: string;
    address: string;
    infoContact: string;
  }) => {
    const {name, phone, address, infoContact} = values;
    dispatch(
      handleSendInfoContact({
        name,
        phone,
        address,
        infoContact,
        messageApp,
        sendInfoSuccess,
        sendInfoFail,
        createCustomersFail,
      }),
    );
    dispatch(
      setStateEdit({
        valid: true,
        nameOpiton: '',
      }),
    );
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={ModalSendInfoCss.container}>
      <Formik
        initialValues={{
          phone: '',
          name: '',
          address: '',
          infoContact: '',
        }}
        onSubmit={() => {}}
        validationSchema={SignupSchema}>
        {({
          handleChange,
          setFieldTouched,
          values,
          errors,
          touched,
          isValid,
          dirty,
        }) => (
          <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <View whiteBGColor bRadius20 p_20>
              <Text fontSize28 textCenter fontWeight700 mainColor>
                {componentSendInfo}
              </Text>
              <View>
                <Text blackColor>{componentCustomersFullName}:</Text>
                <View
                  style={ModalSendInfoCss.inputEdit}
                  borderMainColor
                  whiteBGColor>
                  <TextInput
                    style={[
                      ModalSendInfoCss.TextModalInput,
                      {
                        color: blackcolor,
                      },
                    ]}
                    onChangeText={handleChange('name')}
                    onBlur={() => setFieldTouched('name')}
                    value={values.name}
                  />
                </View>
                {errors.name && touched.name ? (
                  <Text paddingLeft6 redText paddingBottom5>
                    {errors.name}
                  </Text>
                ) : null}
              </View>
              <View>
                <Text blackColor>{componentCustomersPhone}:</Text>
                <View
                  style={ModalSendInfoCss.inputEdit}
                  borderMainColor
                  whiteBGColor>
                  <TextInput
                    style={ModalSendInfoCss.TextModalInput}
                    onChangeText={handleChange('phone')}
                    onBlur={() => setFieldTouched('phone')}
                    value={values.phone}
                    keyboardType={'numeric'}
                  />
                </View>
                {errors.phone && touched.phone ? (
                  <Text redText paddingLeft6 paddingBottom5>
                    {errors.phone}
                  </Text>
                ) : null}
              </View>
              <View>
                <Text blackColor>{staffScreenAddressStaff}:</Text>
                <View
                  style={ModalSendInfoCss.inputEdit}
                  borderMainColor
                  whiteBGColor>
                  <TextInput
                    style={ModalSendInfoCss.TextModalInput}
                    onChangeText={handleChange('address')}
                    onBlur={() => setFieldTouched('address')}
                    value={values.address}
                  />
                </View>
                {errors.address && touched.address ? (
                  <Text redText paddingLeft6 paddingBottom5>
                    {errors.address}
                  </Text>
                ) : null}
              </View>
              <View>
                <Text blackColor>{componentSendInfoContent}:</Text>
                <View
                  style={ModalSendInfoCss.inputContact}
                  borderMainColor
                  whiteBGColor>
                  <TextInput
                    multiline
                    numberOfLines={4}
                    onChangeText={handleChange('infoContact')}
                    onBlur={() => setFieldTouched('infoContact')}
                    value={values.infoContact}
                    style={ModalSendInfoCss.TextModalInput}
                  />
                </View>
                {errors.infoContact && touched.infoContact ? (
                  <Text redText paddingLeft6 paddingBottom5>
                    {errors.infoContact}
                  </Text>
                ) : null}
              </View>
              <View flDirectionRow jContentCenter mVertical6>
                <TouchableOpacity
                  style={[
                    ModalSendInfoCss.btnSendInfo,
                    {
                      backgroundColor:
                        !isValid || !dirty ? graycolor : maincolor,
                    },
                  ]}
                  onPress={() => getInfoContact(values)}
                  disabled={!isValid || !dirty}>
                  <Text whiteColor fontSize18 fontWeight600>
                    {send}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() =>
                    dispatch(
                      setStateEdit({
                        valid: true,
                        nameOpiton: '',
                      }),
                    )
                  }
                  style={ModalSendInfoCss.btnModalClose}>
                  <Text whiteColor fontSize18 fontWeight600>
                    {staffScreenCancelStaff}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </TouchableWithoutFeedback>
        )}
      </Formik>
    </KeyboardAvoidingView>
  );
};

export default ModalAddCategory;
