import {ThunkDispatch} from '@reduxjs/toolkit';
import React, {useEffect, useState} from 'react';
import {
  <PERSON>ert,
  BackHandler,
  Platform,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
} from 'react-native';

import {useDispatch} from 'react-redux';
import {rootInterface} from '../../interface';
import {Text, View} from '../index';

import {setStateEdit} from '../../Redux/Slide';
import {heightWindow, scale} from '../../utils/style/Reponsive';
import {useAppSelector} from '../../hooks';
import {languages} from '../../../src/constants';

interface IItemPayment {
  id: string;
  title: string;
  quantity: number;
  price: number;
  vat: string;
  TotalPrice: number;
}
interface IDetailPayments {
  [key: string]: string | undefined;
  detailStafScreenStaffDetailBill?: string;
  componentDetailPaymentId?: string;
  close?: string;
  detailStafScreenStaffPaidBill?: string;
  componentDetailPaymentDate?: string;
  componentDetailPaymentHour?: string;
  componentDetailPaymentNoted?: string;
  componentDetailPaymentNoReason?: string;
  componentDetailPaymentDiscountAmount?: string;
  componentDetailPaymentUsedInStone?: string;
  tableScreenNameTable?: string;
  componentDetailPaymentTaskAway?: string;
  componentDetailPaymentListFoodUsed?: string;
  componentDetailPaymentName?: string;
  componentDetailPaymentQuantity?: string;
  componentDetailPaymentPrice?: string;
  componentDetailPaymentVAT?: string;
  componentDetailPaymentTotalPrice?: string;
  agree?: string;
  cancel?: string;
  messageApp?: string;
  backToPage?: string;
}
const DetailPaymentStaff = (props: rootInterface['detailPayment']) => {
  const xmlData = useAppSelector(state => state.getData.xmlData?.screen);
  const {maincolor, backgroundcolor} = xmlData;
  const {items, reason, id, user_name, dayPayment, hourPayment} = props.item;
  const [detailPayment, setDetailPayment] = useState<any>();
  const [extraProductList, setExtraProductList] = useState<any>([]);
  const isCheckLang = useAppSelector(state => state.getData.isCheckLang);
  const xmlDataLang = useAppSelector(state => state.getData.xmlData);
  const selectedLang = (languages[isCheckLang as number] || {})?.lang;
  const checkLangue = (): IDetailPayments | null => {
    const langData = languages[isCheckLang as number];
    if (langData) {
      const result: IDetailPayments = {};
      langData.fields.forEach((field: string) => {
        result[field] = xmlDataLang?.[selectedLang]?.[field];
      });
      return result;
    }
    return null;
  };
  const langData = checkLangue();
  const {
    detailStafScreenStaffDetailBill,
    componentDetailPaymentId,
    close,
    detailStafScreenStaffPaidBill,
    componentDetailPaymentDate,
    componentDetailPaymentHour,
    componentDetailPaymentNoted,
    componentDetailPaymentNoReason,
    componentDetailPaymentDiscountAmount,
    componentDetailPaymentUsedInStone,
    tableScreenNameTable,
    componentDetailPaymentTaskAway,
    componentDetailPaymentListFoodUsed,
    componentDetailPaymentName,
    componentDetailPaymentQuantity,
    componentDetailPaymentPrice,
    componentDetailPaymentVAT,
    componentDetailPaymentTotalPrice,
    agree,
    cancel,
    messageApp,
    backToPage,
  }: any = langData;
  const dispatch = useDispatch<ThunkDispatch<boolean, string, any>>();
  const currencySymbol = useAppSelector(state => state.counter?.infoAdmin?.admin?.store?.currency_symbol) || "đ";
  useEffect(() => {
    const jsonObject = JSON.parse(items);
    const itemArray = Object.values(jsonObject.item);
    setDetailPayment(itemArray);
    const list = itemArray.reduce((arr: any[], item) => {
      if ((item as any).extra_product_list) {
        return arr.concat((item as any).extra_product_list);
      } else {
        return arr;
      }
    }, []);

    setExtraProductList(list);

    //fix bugs previous screen crash app
    const backAction = () => {
      Alert.alert(messageApp, backToPage, [
        {
          text: cancel,
          onPress: () => null,
          style: 'cancel',
        },
        {
          text: agree,
          onPress: () => dispatch(setStateEdit({valid: true, nameOpiton: ''})),
        },
      ]);
      return true;
    };
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );
    return () => backHandler.remove();
  }, [agree, cancel, dispatch, items, messageApp]);

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: backgroundcolor,
      }}>
      <View
        flDirectionRow
        jContentBetween
        style={{
          ...Platform.select({
            ios: {
              marginTop: scale(60),
            },
            android: {
              marginTop: scale(16),
            },
          }),
          marginHorizontal: 10,
        }}>
        <View>
          <Text fontSize28 textCenter fontWeight700 mainColor>
            {detailStafScreenStaffDetailBill}
          </Text>
        </View>
        <TouchableOpacity
          onPress={() => dispatch(setStateEdit({valid: true, nameOpiton: ''}))}
          style={{
            borderRadius: scale(8),
            backgroundColor: maincolor,
            justifyContent: 'center',
            width: '20%',
            marginRight: 15,
          }}>
          <Text textCenter fontSize20 fontWeight600 whiteColor>
            {close}
          </Text>
        </TouchableOpacity>
      </View>
      <View mHorizontal10>
        <View mTop10>
          <View flDirectionRow w_100 mVertical4>
            <View w_30>
              <Text fontSize18 blackColor>
                {componentDetailPaymentId}:
              </Text>
            </View>
            <View aItemsFlexEnd w_65>
              <Text fontSize18 blackColor>
                {id}
              </Text>
            </View>
          </View>
          <View flDirectionRow>
            <View w_55>
              <Text fontSize18 blackColor>
                {detailStafScreenStaffPaidBill}:
              </Text>
            </View>
            <View aItemsFlexEnd w_40>
              <Text fontSize18 blackColor>
                {user_name}
              </Text>
            </View>
          </View>
          <View flDirectionRow mVertical4>
            <View w_45>
              <Text fontSize18 blackColor>
                {componentDetailPaymentDate}:
              </Text>
            </View>
            <View aItemsFlexEnd w_50>
              <Text fontSize18 blackColor>
                {dayPayment}
              </Text>
            </View>
          </View>
          <View flDirectionRow>
            <View w_45>
              <Text fontSize18 blackColor>
                {componentDetailPaymentHour}:
              </Text>
            </View>
            <View aItemsFlexEnd w_50>
              <Text fontSize18 blackColor>
                {hourPayment}
              </Text>
            </View>
          </View>
          <View flDirectionRow mVertical4>
            <View w_45>
              <Text fontSize18 blackColor>
                {componentDetailPaymentNoted}:
              </Text>
            </View>
            <View aItemsFlexEnd w_50>
              <Text fontSize16 blackColor>
                {reason == '0' ? componentDetailPaymentNoReason : reason}
              </Text>
            </View>
          </View>
          <View flDirectionRow mVertical4>
            <View w_45>
              <Text fontSize18 blackColor>
                {componentDetailPaymentDiscountAmount}:
              </Text>
            </View>
            {/* <View aItemsFlexEnd w_50>
              <Text fontSize16 blackColor>
                {discountPayment !== undefined && discountPayment > 0
                  ? discountPayment
                  : 0}
                {currencySymbol}
              </Text>
            </View> */}
          </View>
          {/* {tableName !== undefined ? (
            <View flDirectionRow w_100>
              <View w_65>
                <Text fontSize16 blackColor fontWeight900>
                  {componentDetailPaymentUsedInStone}:
                </Text>
              </View>
              <View aItemsFlexEnd w_30>
                <Text fontWeight900 fontSize16 blackColor>
                  {tableScreenNameTable}
                  {` `}
                  {tableName}
                </Text>
              </View>
            </View>
          ) : (
            <View mVertical4>
              <Text fontWeight900 fontSize18 blackColor>
                {componentDetailPaymentTaskAway}
              </Text>
            </View>
          )} */}
          <View w_100 mRight8>
            <Text fontSize22 fontWeight700 mainColor textCenter>
              {componentDetailPaymentListFoodUsed}
            </Text>
          </View>
          <View flDirectionRow w_100 jContentBetween mVertical4>
            <View w_20>
              <Text fontSize16 fontWeight700 blackColor>
                {componentDetailPaymentName}
              </Text>
            </View>
            <View w_10>
              <Text fontSize16 fontWeight700 textCenter blackColor>
                {componentDetailPaymentQuantity}
              </Text>
            </View>
            <View w_25>
              <Text fontSize16 fontWeight700 textCenter blackColor>
                {componentDetailPaymentPrice}
              </Text>
            </View>
            <View w_15>
              <Text fontSize16 fontWeight700 textCenter blackColor>
                {componentDetailPaymentVAT}
              </Text>
            </View>
            <View w_30>
              <Text fontSize16 fontWeight700 textCenter blackColor>
                {componentDetailPaymentTotalPrice}
              </Text>
            </View>
          </View>
          <ScrollView
            style={{
              height: heightWindow * 0.6,
            }}
            showsVerticalScrollIndicator={false}>
            {detailPayment &&
              detailPayment.map((itemPayment: IItemPayment) => {
                return (
                  <View key={itemPayment.id}>
                    <View flDirectionRow mVertical4 w_100>
                      <View w_20>
                        <Text fontSize16 textCenter blackColor textLeft>
                          {itemPayment.title}
                        </Text>
                      </View>
                      <View w_10 aItemsCenter jContentCenter>
                        <Text fontSize16 textCenter blackColor>
                          {itemPayment.quantity}
                        </Text>
                      </View>
                      <View w_25 aItemsCenter jContentCenter>
                        <Text fontSize16 textCenter blackColor>
                          {itemPayment.price
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                          {currencySymbol}
                        </Text>
                      </View>
                      <View w_15 aItemsCenter jContentCenter>
                        <Text fontSize16 textCenter blackColor>
                          {itemPayment.vat}%
                        </Text>
                      </View>
                      <View w_25 aItemsCenter jContentCenter>
                        <Text fontSize16 textCenter blackColor>
                          {(
                            itemPayment.price * itemPayment.quantity +
                            (itemPayment.price *
                              itemPayment.quantity *
                              parseInt(itemPayment.vat)) /
                              100
                          )
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                          {currencySymbol}
                        </Text>
                      </View>
                    </View>
                  </View>
                );
              })}
            {extraProductList.length > 0 &&
              extraProductList.map((itemPayment: IItemPayment) => {
                return (
                  <View key={itemPayment.id}>
                    <View flDirectionRow mVertical4 w_100>
                      <View w_20>
                        <Text fontSize16 textCenter blackColor textLeft>
                          {itemPayment.title}
                        </Text>
                      </View>
                      <View w_10 aItemsCenter jContentCenter>
                        <Text fontSize16 textCenter blackColor>
                          {itemPayment.quantity === undefined
                            ? 1
                            : itemPayment.quantity}
                        </Text>
                      </View>
                      <View w_25 aItemsCenter jContentCenter>
                        <Text fontSize16 textCenter blackColor>
                          {itemPayment.price
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                          {currencySymbol}
                        </Text>
                      </View>
                      <View w_15 aItemsCenter jContentCenter>
                        <Text fontSize16 textCenter blackColor>
                          {itemPayment.vat}%
                        </Text>
                      </View>
                      <View w_25 aItemsCenter jContentCenter>
                        <Text fontSize16 textCenter blackColor>
                          {(
                            itemPayment.price *
                              (itemPayment.quantity === undefined
                                ? 1
                                : itemPayment.quantity) +
                            (itemPayment.price *
                              (itemPayment.quantity === undefined
                                ? 1
                                : itemPayment.quantity) *
                              parseInt(itemPayment.vat)) /
                              100
                          )
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                          {currencySymbol}
                        </Text>
                      </View>
                    </View>
                  </View>
                );
              })}
          </ScrollView>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default React.memo(DetailPaymentStaff);
