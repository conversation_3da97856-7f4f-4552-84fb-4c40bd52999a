import React, {useMemo} from 'react';
import {<PERSON><PERSON>, Alert, <PERSON>rollView, Platform, View} from 'react-native';

import {utils, WorkBook, write} from 'xlsx';

import RNFetchBlob from 'react-native-blob-util';
import RNFS from 'react-native-fs';
import {rootInterface} from '../../interface';
import {useAppSelector} from '../../hooks';

const Export = (props: rootInterface['exportFileData']) => {
  const {startDay, endDay, dataFood, TotalPrice} = props;
  const {storename, address, name} = useAppSelector(
    state => state.counter.infoAdmin.admin,
  );

  const date = new Date();

  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();

  const data = useMemo(() => {
    return {
      data: [
        ['Báo cáo doanh thu'],
        [`<PERSON> tiết doanh thu từ ngày ${startDay} đến ngày ${endDay}`],
        dataFood.map((item: {title: string}) => {
          return [item.title];
        }),
        dataFood.map((item: {quantity: string}) => {
          return [item.quantity];
        }),
        dataFood.map((item: {totalPrice: number}) => {
          return [Math.round(item.totalPrice) + 'đ'];
        }),
        ['Tổng cộng', `${Math.round(TotalPrice)}đ`],
      ],
    };
  }, [dataFood]);
  const dataTax = useMemo(() => {
    return {
      data: [
        [
          `HỘ, CÁ NHÂN KINH DOANH:${storename ? storename : ''}`,
          '',
          '',
          '',
          'Mẫu số S4-HKD',
        ],
        [
          `Địa chỉ:${address ? address : ''}`,
          '',
          '',
          '(Ban hành kèm theo Thông tư số 88/2021/TT-BTC ngày 11 tháng 10 năm 2021 của Bộ trưởng Bộ Tài chính)',
        ],
        [''],
        ['', '', 'SỔ CHI TIẾT DOANH THU BÁN HÀNG HÓA, DỊCH VỤ'],
        ['', '', '', `Tên địa điểm kinh doanh: ${name ? name : ''}  `],
        ['', '', '', '', `Năm: ${year}`],
        [],
        [
          'Ngày,tháng ghi sổ',
          'Chứng từ',
          '',
          'Diễn giải',
          'Doanh thu bán hàng hóa, dịch vụ',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
        ],
        [
          `Ngày ${day} tháng ${month}`,
          'Số hiệu',
          'Ngày,tháng',
          '',
          'Hoạt động kinh doanh khác',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
        ],
        ['A', 'B', 'C', 'D', '10', '', '', '', '', '', '', '', '', '', '', ''],
        [],
        dataFood.map((item: {title: string}) => {
          return [item.title];
        }),
        dataFood.map((item: {quantity: string}) => {
          return [item.quantity];
        }),
        dataFood.map((item: {totalPrice: number}) => {
          return [Math.round(item.totalPrice) + 'đ'];
        }),
        [
          'Tổng cộng',
          `${Math.round(TotalPrice)}đ`,
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
          '',
        ],
        ['- Sổ này có 01 trang, đánh số từ trang 01 đến trang 01'],
        [`- Ngày mở sổ: ${day} tháng ${month}  năm ${year} `],
        [
          '',
          '',
          '',
          '',
          '',
          `Ngày ${day} tháng ${month}  năm ${year} `,
          '',
          '',
          '',
          '',
          '',
          '',
        ],
        [
          '',
          'NGƯỜI LẬP BIỂU',
          '',
          '',
          'NGƯỜI ĐẠI DIỆN HỘ KINH DOANH/CÁ NHÂN KINH DOANH',
        ],
        ['', '(Ký, họ tên)', '', '', '', '(Ký, họ tên, đóng dấu)'],
        ['', '', '', '', '', ''],
        ['', '', '', '', '', ''],
        ['', '', '', '', '', ''],
        ['', '', '', '', '', ''],
        [
          '',
          `${storename ? storename : ''}`,
          '',
          '',
          '',
          `${storename ? storename : ''}`,
        ],
      ],
    };
  }, [dataFood]);

  const writeWorkbook = async (wb: WorkBook) => {
    const path =
      Platform.OS === 'android'
        ? RNFS.DownloadDirectoryPath
        : RNFS.MainBundlePath;
    const wbout = write(wb, {type: 'buffer', bookType: 'xlsx'});
    const file =
      path + `/Chi tiết doanh thu từ ngày  ${startDay} đến ngày ${endDay}.xlsx`;
    await RNFetchBlob.fs.writeFile(file, Array.from(wbout), 'ascii');
    return file;
  };
  const writeWorkbookTax = async (wb: WorkBook) => {
    const path =
      Platform.OS === 'android'
        ? RNFS.DownloadDirectoryPath
        : RNFS.MainBundlePath;
    const wbout = write(wb, {type: 'buffer', bookType: 'xlsx'});
    const file =
      path + `/Báo cáo thuế từ ngày  ${startDay} đến ngày ${endDay}.xlsx`;
    await RNFetchBlob.fs.writeFile(file, Array.from(wbout), 'ascii');
    return file;
  };

  const exportFile = async () => {
    try {
      /* convert AOA back to worksheet */
      const ws = utils.aoa_to_sheet(data.data);
      /* build new workbook */
      const wb = utils.book_new();
      utils.book_append_sheet(wb, ws, 'SheetJS', true);
      /* write file */
      const res = await writeWorkbook(wb);
      Alert.alert(
        'Tải file thành công',
        `Chi tiết doanh thu từ ngày  ${startDay} đến ngày ${endDay}.xlsx`,
      );
    } catch (err: unknown) {
      Alert.alert('exportFile Error', 'Error ' + (err as Error).message);
    }
  };
  const exportFileTax = async () => {
    try {
      /* convert AOA back to worksheet */
      const ws = utils.aoa_to_sheet(dataTax.data);
      /* build new workbook */
      const wb = utils.book_new();
      utils.book_append_sheet(wb, ws, 'SheetJS', true);
      /* write file */
      const res = await writeWorkbookTax(wb);
      Alert.alert(
        'Tải file thành công',
        `Báo cáo thuế từ ngày  ${startDay} đến ngày ${endDay}.xlsx`,
      );
    } catch (err: unknown) {
      Alert.alert('exportFile Error', 'Error ' + (err as Error).message);
    }
  };

  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      showsHorizontalScrollIndicator={false}>
      <View>
        <Button
          onPress={exportFile}
          title="Xuất báo cáo doanh thu"
          color="#841584"
        />
      </View>
      <View
        style={{
          marginTop: 10,
        }}>
        <Button
          onPress={exportFileTax}
          title="Xuất khai báo thuế"
          color="#841584"
        />
      </View>
    </ScrollView>
  );
};

export default React.memo(Export);
