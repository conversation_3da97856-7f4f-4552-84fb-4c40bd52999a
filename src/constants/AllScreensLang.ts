const languages: {
    [key: number]: { lang: string; fields: string[] };
} = {
    0: {
        lang: 'Viet',
        fields: [
            'componentProductPrice',
            'componentAddProductPriceNotVAT',
            'minValidMessageNumberPhone',
            'maxValidMessageNumberPhone',
            'fieldIsBlank',
            'matchesNumberPhone',
            'minValidMessagePassword',
            'maxValidMessagePassword',
            'titleApp',
            'qlbh',
            'nameApp',
            'phone',
            'pass',
            'login',
            'registry',
            'send',
            'contact',
            'phoneContact',
            'email',
            'currencySymbol',
            'agree',
            'cancel',
            'confirmPasswordMessage',
            'minValidMessageName',
            'maxValidMessageName',
            'minValidMessageAddress',
            'maxValidMessageAddress',
            'maxValidMessagePSendInfo',
            'minValidMessagePSendInfo',
            'backToPage',
            'close',
            'tokenExpiresed',
            'messageApp',
            'createCustomersFail',
            'createCustomersSuccess',
            'createCustomersNumberPhoneExist',
            'createCustomerNameFillInBlank',
            'vietVangPhoneNumber',
            'agree',
            'cancel',
            'networkIsError',
            'upLoadImgFail',
            'loginFail',
            'fillInBlank',
            'networkError',
            'messageDisable',
            'addCategorySuccess',
            'fillInBlankCategory',
            'categoryNameExist',
            'addCategoryFail',
            'editCategoryFail',
            'addCategoryExistButDelete',
            'deleteCategorySuccess',
            'categoryNameNotValid',
            'deleteCategoryFail',
            'editCategorySuccess',
            'editCategoryBlank',
            'addProductSuccess',
            'productNameExist',
            'notFullValid',
            'findNotCategory',
            'notSelectedCategory',
            'addProductFail',
            'alertVat',
            'addProductExistButDeleted',
            'deleteProductSuccess',
            'deleteProductFail',
            'editProductSuccess',
            'editNotFullValid',
            'editProductFail',
            'sendInfoSuccess',
            'sendInfoFail',
            'addStaffSuccess',
            'addStaffFaill',
            'infoStaffExist',
            'staffExistButDeleted',
            'notFullValidStaff',
            'notFullValidNameStaff',
            'changeInfoStaffSuccess',
            'phoneNumberExist',
            'phoneNumberNotValid',
            'notFullValidAddress',
            'changeInfoStaffFail',
            'deleteStaffSuccess',
            'deleteStaffFail',
            'changePasswordSuccess',
            'changePasswordFail',
            'addTableSuccess',
            'addTableExist',
            'addTableExistButDeleted',
            'addTableFail',
            'cancelOrderSuccess',
            'cancelOrderFail',
            'deleteTableSuccess',
            'deleteTableFail',
            'deleteMutilTableSuccess',
            'deleteMutilTableSuccess',
            'doNotChooseTableDelete',
            'deleteMutilTableFail',



            'homeScreenProduct',
            'homeScreenRevenue',
            'homeScreenTable',
            'homeScreenStaff',
            'homeScreenInfomation',
            'homeScreenCustomers',
            'homeScreenRevenueToday',
            'homeScreenOrdersNumbersToday',
            'homeScreenOrders',
            'homeScreenBestSalesToday',
            'homeScreenAllOrders',
            'homeScreenHighestSalesToday',
            'homeScreenDeleteAccount',
            'homeScreenDeleteAccountText1',
            'homeScreenDeleteAccountText2',

            'homeScreenLogout',
            'homeScreenWareHouse',
            'homeScreenTaxReceipt',
            'productsScreenAddProduct',
            'productsScreenListCategories',
            'productsScreenCategoriesTitle',
            'productsScreenListProducts',
            'productsScreenFindProduct',
            'productsScreenCategoryFind',
            'productsScreenDishType',
            'productsScreenMainCourse',
            'productsScreenSideDish',
            'SuccessfullyAddedNewSideDish',
            'FailedToDddNewSideDish',
            'handleProductScreenListSideDish',
            'tableScreenAddTable',
            'tableScreenDeleteTable',
            'tableScreenDeleteTables',
            'tableScreenListTables',
            'tableScreenTableNumber',
            'tableScreenNameFood',
            'tableScreenQuantityFood',
            'tableScreenPriceFood',
            'tableScreenCancelOrders',
            'tableScreenExitTable',
            'tableScreenWarning',
            'tableScreenNameTable',
            'tableScreenMessDeleteTables',

            'staffScreenCreateStaffs',
            'staffScreenNameStaff',
            'staffScreenAddressStaff',
            'staffScreenPhoneStaff',
            'staffScreenPasswordStaff',
            'staffScreenPasswordConfirmStaff',
            'staffScreenStatusStaff',
            'staffScreenAddStaff',
            'staffScreenCancelStaff',
            'staffScreenStatusStaffWork',

            'handleProductScreenTitle',
            'handleProductScreenName',
            'handleProductScreenVat',
            'handleProductScreenPrice',
            'handleProductScreenAddImage',
            'handleProductScreenUpdateImage',
            'handleProductScreenUpdate',
            'handleProductScreenDelete',
            'handleProductScreenDeleteConfirm',
            'productsScreenCategoriesTitle',

            'detailStafScreenChangePass',
            'detailStafScreenHidestatistics',
            'detailStafScreenShowstatistics',
            'detailStafScreenStaffNoRevenue',
            'detailStafScreenStaffArrangeBill',
            'detailStafScreenStaffHideBillDeleted',
            'detailStafScreenStaffShowBillDeleted',
            'detailStafScreenStaffFindBill',
            'detailStafScreenStaffPaidBill',
            'detailStafScreenStaffDeleteBill',
            'detailStafScreenStaffDeleteBillConfirm',
            'detailStafScreenStaffInvoiceStatus',
            'detailStafScreenStaffNoChange',
            'detailStafScreenStaffChanged',
            'detailStafScreenStaffChangedReason',
            'detailStafScreenStaffPriceNoVAT',
            'detailStafScreenStaffPaymented',
            'detailStafScreenStaffDetailBill',
            'detailStafScreenStaffDayCreated',
            'detailStafScreenStaffDayDeleted',
            'detailStafScreenStaffTotalRevenue',
            'detailStafScreenStaffUpdateInfo',
            'detailStafScreenStaffDeleteStaff',
            'detailStafScreenStaffDeleteStaffConfirm',
            'detailStafScreenStaffUpdate',
            'detailStafScreenStaffDeleteSuccess',
            'detailStafScreenStaffDeleteFail',

            'customersScreenTitle',
            'customersScreenListCustommers',
            'customersScreenNewCustomer',
            'customersScreenOldCustomer',
            'customersScreenPaymentMax',
            'customersScreenPaymentMin',
            'customersScreenFindCustomer',
            'customersScreenSortList',

            'chartScreenmonday',
            'chartScreentuesday',
            'chartScreenwednesday',
            'chartScreenthursday',
            'chartScreenfriday',
            'chartScreensaturday',
            'chartScreensunday',
            'chartScreenlegendWeek',
            'chartScreenFirstWeek',
            'chartScreenSecondWeek',
            'chartScreenThirdWeek',
            'chartScreenFourtWeek',
            'chartScreenFifthWeek',
            'chartScreenlegenMonth',
            'chartScreenMessageReason',
            'chartScreenRevenueDay',
            'chartScreenRevenueWeek',
            'chartScreenRevenueMonth',
            'chartScreenDetail',
            'chartScreenLastMonth',
            'chartScreenDayRevenue',
            'chartScreenSeeRevenue',
            'chartScreenEndDay',
            'chartScreenStartDay',
            'chartScreenSeeDetailCustomizableDay',
            'chartScreenRevenuemanagement',
            'chartScreenOther',
            'componentCustomersInfoCustomer',
            'componentCustomersFullName',
            'componentCustomersPhone',
            'componentCustomersCreateDay',
            'componentCustomersTotalPrice',
            'componentCustomersRemainingPoints',
            'componentCustomersPoints',
            'componentCustomersPointsUsed',
            'componentCustomersPointsListPointUsed',

            'componentDetailPaymentId',
            'componentDetailPaymentDate',
            'componentDetailPaymentHour',
            'componentDetailPaymentNoted',
            'componentDetailPaymentNoReason',
            'componentDetailPaymentDiscountAmount',
            'componentDetailPaymentUsedInStone',
            'componentDetailPaymentTaskAway',
            'componentDetailPaymentListFoodUsed',
            'componentDetailPaymentName',
            'componentDetailPaymentQuantity',
            'componentDetailPaymentPrice',
            'componentDetailPaymentVAT',
            'componentDetailPaymentTotalPrice',

            'componentCategoriesTitle',
            'componentCategoriesWarning',
            'componentCategoriesCreateCategory',
            'componentCategoriesCreate',
            'componentCategoriesWarningDelete',

            'componentCustomersWarningPhone',
            'componentCustomersName',
            'componentCustomersCreate',

            'componentAddImagesUpdate',
            'componentAddImagesUpload',
            'componentAddImagesTitle',
            'componentAddImagesSuccess',

            'componentAddProductChooseCategory',
            'componentAddProductCategories',
            'componentAddProductName',
            'componentAddProductPrice',
            'componentAddProductVat',
            'componentAddProductImage',
            'componentAddProductCreate',

            'componentCreateFooterSuccess',
            'componentExistFooter',
            'componentFailFooter',
            'componentFooterTitle',
            'componentCreateTitle',
            'componentCloseTitle',
            'componentChangeProductImage',

            'componentSendInfoContent',
            'componentLogoTitle',
            'componentLogoUploadLogo',
            'componentUpdateLogoSuccess',
            'componentSendInfo',
            'InfoScreenDetailStore',
            'InfoScreenNameStore',
            'InfoScreenNameBossStore',
            'InfoScreenNoName',
            'InfoScreenExpired',
            'InfoScreenQtiStaff',
            'InfoScreenTraniner',
            'InfoScreenAddress',
            'InfoScreenContent',
            'InfoScreenUpdateFooter',
            'InfoScreenCreateFooter',
            'InfoScreenUpdateImageFooter',

            'RestoreCategories',
            'CategoryExist',
            'RestoreTableSuccess',
            'RestoreStaffSuccess',
            'RestoreProductSuccess',
            'PriceOnlyInt',

            'highestRevenue',
            'lowestRevenue',
            'latestInvoice',
            'oldestInvoice',

            'onlineStatus',
            'offlineStatus',
            'stopStatus',
            'detailFoodStartToEndFrom',
            'detailFoodStartToEndTo',
            'detailFoodStartToEndDetailRevenue',
            'updateFooterSuccess',
            'updateFooterFail',

            'DeliveryNote',
            'ProductNotSelected',
            'deliveryNote',
            'ExportBill',
            'ExportWarehouseQuantityFood',
            'ProductNotCreated',
            'Cash',
            'Transfer',
            'Bank',
            'Export',
            'Return',
            'CancelOutput',
            'WarehouseSuccess',
            'WarehouseReturnSuccess',
            'WarehouseCancelSuccess',
            'WarehouseExport',
            'WarehouseExportError',
            'RepresentativeName',
            'AgencyPhoneNumber',
            'AgencyName',
            'AgencyHaveNotSelected',
            'AgenciesHaveNotSelected',
            'CodeFormInput',
            'CodeFormExport',
            'PriceExport',
            'PriceExportCancel',
            'DiscountPrice',
            'ProductNotSelected',
            'TotalPrice',
            'PaymentMethod',
            'NoteExport',
            'TotalPriceProduct',
            'CreateDeliveryExport',
            'CreateDeliveryBack',
            'CreateDeliveryCancel',
            'CreateDeliveryImport',
            'AddDeliveryExport',
            'AddDeliveryError',
            'CancelDelivery',
            'DetailProduct',
            'ProductCode',
            'ProductNameInventory',
            'PriceInventory',
            'QuantityInventory',
            'AddDelivery',
            'QuantityDelivery',
            'CreateDelivery',
            'WarningDiscount',
            'AddDeliveryBillExport',
            'PriceImport',
            'WarehouseExportTitle',
            'WarehouseFindProduct',
            'WarehouseTaxProduct',
            'BillExportExist',
            'TitleInfoAgency',
            'CompanyName',
            'AgencyNameInfo',
            'AgencyTaxCode',
            'AgencyAddress',
            'AgencyRepresentative',
            'AgencyEmail',
            'AgencyPhoneNumberInfo',
            'AgencyNote',
            'CreateAgencySuccess',
            'CreateAgencyFail',
            'Surcharge',
            'messLoginFail401',
            'messLoginFail204',
            'listInvoice',
            'enterInvoice',
            'dateCreateInvoice',
            'totalPriceInvoice',
            'paymentCodeInvoice',
            'electronicInvoiceIndividual',
            'electronicInvoiceCompany',
            'electronicInvoiceTaxCodeEmpty',
            'electronicInvoiceSuccess',
            'electronicInvoiceFail',
            'electronicInvoiceTaxCodeValid',
            'electronicInvoiceTaxCodeInvalid',
            'electronicInvoiceSuccessRegister',
            'electronicInvoiceEmailInvalid',
            'electronicInvoiceError',
            'electronicInvoiceDetail',
            'electronicInvoiceNumber',
            'electronicInvoiceCode',
            'electronicInvoiceDate',
            'electronicInvoiceTotal',
            'electronicInvoiceTaxCode',
            'electronicInvoiceCustomerName',
            'electronicInvoiceCompanyName',     
            'electronicInvoiceEmail',
            'electronicInvoiceAddress',
            'electronicInvoiceProductName',
            'electronicInvoiceProductQuantity',
            'electronicInvoiceProductPrice',
            'electronicInvoiceProductVat',
            'electronicInvoiceEnterTaxCode',
            'electronicInvoiceListProduct'

        ],
    },
    1: {
        lang: 'Eng',
        fields: [
            'minValidMessageNumberPhone',
            'maxValidMessageNumberPhone',
            'fieldIsBlank',
            'matchesNumberPhone',
            'minValidMessagePassword',
            'maxValidMessagePassword',
            'titleApp',
            'qlbh',
            'nameApp',
            'phone',
            'pass',
            'login',
            'registry',
            'send',
            'contact',
            'phoneContact',
            'email',
            'currencySymbol',
            'agree',
            'cancel',
            'confirmPasswordMessage',
            'minValidMessageName',
            'maxValidMessageName',
            'minValidMessageAddress',
            'maxValidMessageAddress',
            'maxValidMessagePSendInfo',
            'minValidMessagePSendInfo',
            'backToPage',
            'close',
            'tokenExpiresed',
            'messageApp',
            'createCustomersFail',
            'createCustomersSuccess',
            'createCustomersNumberPhoneExist',
            'createCustomerNameFillInBlank',
            'vietVangPhoneNumber',
            'agree',
            'cancel',
            'networkIsError',
            'upLoadImgFail',
            'loginFail',
            'fillInBlank',
            'networkError',
            'messageDisable',
            'addCategorySuccess',
            'fillInBlankCategory',
            'categoryNameExist',
            'addCategoryFail',
            'editCategoryFail',
            'addCategoryExistButDelete',
            'deleteCategorySuccess',
            'categoryNameNotValid',
            'deleteCategoryFail',
            'editCategorySuccess',
            'editCategoryBlank',
            'addProductSuccess',
            'productNameExist',
            'notFullValid',
            'findNotCategory',
            'notSelectedCategory',
            'addProductFail',
            'alertVat',
            'addProductExistButDeleted',
            'deleteProductSuccess',
            'deleteProductFail',
            'editProductSuccess',
            'editNotFullValid',
            'editProductFail',
            'sendInfoSuccess',
            'sendInfoFail',
            'addStaffSuccess',
            'addStaffFaill',
            'infoStaffExist',
            'staffExistButDeleted',
            'notFullValidStaff',
            'notFullValidNameStaff',
            'changeInfoStaffSuccess',
            'phoneNumberExist',
            'phoneNumberNotValid',
            'notFullValidAddress',
            'changeInfoStaffFail',
            'deleteStaffSuccess',
            'deleteStaffFail',
            'changePasswordSuccess',
            'changePasswordFail',
            'addTableSuccess',
            'addTableExist',
            'addTableExistButDeleted',
            'addTableFail',
            'cancelOrderSuccess',
            'cancelOrderFail',
            'deleteTableSuccess',
            'deleteTableFail',
            'deleteMutilTableSuccess',
            'deleteMutilTableSuccess',
            'doNotChooseTableDelete',
            'deleteMutilTableFail',



            'homeScreenProduct',
            'homeScreenRevenue',
            'homeScreenTable',
            'homeScreenStaff',
            'homeScreenInfomation',
            'homeScreenCustomers',
            'homeScreenRevenueToday',
            'homeScreenOrdersNumbersToday',
            'homeScreenOrders',
            'homeScreenBestSalesToday',
            'homeScreenAllOrders',
            'homeScreenHighestSalesToday',
            'homeScreenDeleteAccount',
            'homeScreenDeleteAccountText1',
            'homeScreenDeleteAccountText2',
            'homeScreenLogout',
            'homeScreenWareHouse',
            'homeScreenTaxReceipt',
            'productsScreenAddProduct',
            'productsScreenListCategories',
            'productsScreenCategoriesTitle',
            'componentChangeProductImage',
            'productsScreenListProducts',
            'productsScreenFindProduct',
            'productsScreenCategoryFind',
            'productsScreenDishType',
            'productsScreenMainCourse',
            'productsScreenSideDish',
            'SuccessfullyAddedNewSideDish',
            'FailedToDddNewSideDish',
            'handleProductScreenListSideDish',
            'tableScreenAddTable',
            'tableScreenDeleteTable',
            'tableScreenDeleteTables',
            'tableScreenListTables',
            'tableScreenTableNumber',
            'tableScreenNameFood',
            'tableScreenQuantityFood',
            'tableScreenPriceFood',
            'tableScreenCancelOrders',
            'tableScreenExitTable',
            'tableScreenWarning',
            'tableScreenNameTable',
            'tableScreenMessDeleteTables',
            'staffScreenCreateStaffs',
            'staffScreenNameStaff',
            'staffScreenAddressStaff',
            'staffScreenPhoneStaff',
            'staffScreenPasswordStaff',
            'staffScreenPasswordConfirmStaff',
            'staffScreenStatusStaff',
            'staffScreenAddStaff',
            'staffScreenCancelStaff',
            'staffScreenStatusStaffWork',

            'handleProductScreenTitle',
            'productsScreenCategoriesTitle',
            'handleProductScreenName',
            'handleProductScreenVat',
            'handleProductScreenPrice',
            'handleProductScreenAddImage',
            'handleProductScreenUpdateImage',
            'handleProductScreenUpdate',
            'handleProductScreenDelete',
            'handleProductScreenDeleteConfirm',

            'detailStafScreenChangePass',
            'detailStafScreenHidestatistics',
            'detailStafScreenShowstatistics',
            'detailStafScreenStaffNoRevenue',
            'detailStafScreenStaffArrangeBill',
            'detailStafScreenStaffHideBillDeleted',
            'detailStafScreenStaffShowBillDeleted',
            'detailStafScreenStaffFindBill',
            'detailStafScreenStaffPaidBill',
            'detailStafScreenStaffDeleteBill',
            'detailStafScreenStaffDeleteBillConfirm',
            'detailStafScreenStaffInvoiceStatus',
            'detailStafScreenStaffNoChange',
            'detailStafScreenStaffChanged',
            'detailStafScreenStaffChangedReason',
            'detailStafScreenStaffPriceNoVAT',
            'detailStafScreenStaffPaymented',
            'detailStafScreenStaffDetailBill',
            'detailStafScreenStaffDayCreated',
            'detailStafScreenStaffDayDeleted',
            'detailStafScreenStaffTotalRevenue',
            'detailStafScreenStaffUpdateInfo',
            'detailStafScreenStaffDeleteStaff',
            'detailStafScreenStaffDeleteStaffConfirm',
            'detailStafScreenStaffUpdate',
            'detailStafScreenStaffDeleteSuccess',
            'detailStafScreenStaffDeleteFail',

            'customersScreenTitle',
            'customersScreenListCustommers',
            'customersScreenNewCustomer',
            'customersScreenOldCustomer',
            'customersScreenPaymentMax',
            'customersScreenPaymentMin',
            'customersScreenFindCustomer',
            'customersScreenSortList',

            'chartScreenmonday',
            'chartScreentuesday',
            'chartScreenwednesday',
            'chartScreenthursday',
            'chartScreenfriday',
            'chartScreensaturday',
            'chartScreensunday',
            'chartScreenlegendWeek',
            'chartScreenFirstWeek',
            'chartScreenSecondWeek',
            'chartScreenThirdWeek',
            'chartScreenFourtWeek',
            'chartScreenFifthWeek',
            'chartScreenlegenMonth',
            'chartScreenMessageReason',
            'chartScreenRevenueDay',
            'chartScreenRevenueWeek',
            'chartScreenRevenueMonth',
            'chartScreenDetail',
            'chartScreenLastMonth',
            'chartScreenDayRevenue',
            'chartScreenSeeRevenue',
            'chartScreenEndDay',
            'chartScreenStartDay',
            'chartScreenSeeDetailCustomizableDay',
            'chartScreenRevenuemanagement',
            'chartScreenOther',
            'componentCustomersInfoCustomer',
            'componentCustomersFullName',
            'componentCustomersPhone',
            'componentCustomersCreateDay',
            'componentCustomersTotalPrice',
            'componentCustomersRemainingPoints',
            'componentCustomersPoints',
            'componentCustomersPointsUsed',
            'componentCustomersPointsListPointUsed',

            'componentDetailPaymentId',
            'componentDetailPaymentDate',
            'componentDetailPaymentHour',
            'componentDetailPaymentNoted',
            'componentDetailPaymentNoReason',
            'componentDetailPaymentDiscountAmount',
            'componentDetailPaymentUsedInStone',
            'componentDetailPaymentTaskAway',
            'componentDetailPaymentListFoodUsed',
            'componentDetailPaymentName',
            'componentDetailPaymentQuantity',
            'componentDetailPaymentPrice',
            'componentDetailPaymentVAT',
            'componentDetailPaymentTotalPrice',

            'componentCategoriesTitle',
            'componentCategoriesWarning',
            'componentCategoriesCreateCategory',
            'componentCategoriesCreate',
            'componentCategoriesWarningDelete',

            'componentCustomersWarningPhone',
            'componentCustomersName',
            'componentCustomersCreate',

            'componentAddImagesUpdate',
            'componentAddImagesUpload',
            'componentAddImagesTitle',
            'componentAddImagesSuccess',

            'componentAddProductChooseCategory',
            'componentAddProductCategories',
            'componentAddProductName',
            'componentAddProductPrice',
            'componentAddProductVat',
            'componentAddProductImage',
            'componentAddProductCreate',
            'componentAddProductPriceNotVAT',
            'componentProductPrice',

            'componentCreateFooterSuccess',
            'componentExistFooter',
            'componentFailFooter',
            'componentFooterTitle',
            'componentCreateTitle',
            'componentCloseTitle',

            'componentSendInfoContent',
            'componentLogoTitle',
            'componentLogoUploadLogo',
            'componentUpdateLogoSuccess',
            'componentSendInfo',
            'InfoScreenDetailStore',
            'InfoScreenNameStore',
            'InfoScreenNameBossStore',
            'InfoScreenNoName',
            'InfoScreenExpired',
            'InfoScreenQtiStaff',
            'InfoScreenTraniner',
            'InfoScreenAddress',
            'InfoScreenContent',
            'InfoScreenUpdateFooter',
            'InfoScreenCreateFooter',
            'InfoScreenUpdateImageFooter',

            'RestoreCategories',
            'CategoryExist',
            'RestoreTableSuccess',
            'RestoreStaffSuccess',
            'RestoreProductSuccess',
            'PriceOnlyInt',

            'highestRevenue',
            'lowestRevenue',
            'latestInvoice',
            'oldestInvoice',

            'onlineStatus',
            'offlineStatus',
            'stopStatus',
            'detailFoodStartToEndFrom',
            'detailFoodStartToEndTo',
            'detailFoodStartToEndDetailRevenue',
            'updateFooterSuccess',
            'updateFooterFail',

            'DeliveryNote',
            'ProductNotSelected',
            'deliveryNote',
            'ExportBill',
            'ExportWarehouseQuantityFood',
            'ProductNotCreated',
            'Cash',
            'Transfer',
            'Bank',
            'Export',
            'Return',
            'CancelOutput',
            'WarehouseSuccess',
            'WarehouseReturnSuccess',
            'WarehouseCancelSuccess',
            'WarehouseExport',
            'WarehouseExportError',
            'RepresentativeName',
            'AgencyPhoneNumber',
            'AgencyName',
            'AgencyHaveNotSelected',
            'AgenciesHaveNotSelected',
            'CodeFormInput',
            'CodeFormExport',
            'PriceExport',
            'PriceExportCancel',
            'DiscountPrice',
            'ProductNotSelected',
            'TotalPrice',
            'PaymentMethod',
            'NoteExport',
            'TotalPriceProduct',
            'CreateDeliveryExport',
            'CreateDeliveryBack',
            'CreateDeliveryCancel',
            'CreateDeliveryImport',
            'AddDeliveryExport',
            'AddDeliveryError',
            'CancelDelivery',
            'DetailProduct',
            'ProductCode',
            'ProductNameInventory',
            'PriceInventory',
            'QuantityInventory',
            'AddDelivery',
            'QuantityDelivery',
            'CreateDelivery',
            'WarningDiscount',
            'AddDeliveryBillExport',
            'PriceImport',
            'WarehouseExportTitle',
            'WarehouseFindProduct',
            'WarehouseTaxProduct',
            'BillExportExist',
            'TitleInfoAgency',
            'CompanyName',
            'AgencyNameInfo',
            'AgencyTaxCode',
            'AgencyAddress',
            'AgencyRepresentative',
            'AgencyEmail',
            'AgencyPhoneNumberInfo',
            'AgencyNote',
            'CreateAgencySuccess',
            'CreateAgencyFail',
            'Surcharge',
            'messLoginFail401',
            'messLoginFail204',
            'listInvoice',
            'enterInvoice',
            'dateCreateInvoice',
            'totalPriceInvoice',
            'paymentCodeInvoice',
            'electronicInvoiceIndividual',
            'electronicInvoiceCompany',
            'electronicInvoiceTaxCodeEmpty',
            'electronicInvoiceSuccess',
            'electronicInvoiceFail',
            'electronicInvoiceTaxCodeValid',
            'electronicInvoiceTaxCodeInvalid',
            'electronicInvoiceSuccessRegister',
            'electronicInvoiceEmailInvalid',
            'electronicInvoiceError',
            'electronicInvoiceDetail',
            'electronicInvoiceNumber',
            'electronicInvoiceCode',
            'electronicInvoiceDate',
            'electronicInvoiceTotal',
            'electronicInvoiceTaxCode',
            'electronicInvoiceCustomerName',
            'electronicInvoiceCompanyName',     
            'electronicInvoiceEmail',
            'electronicInvoiceAddress',
            'electronicInvoiceProductName',
            'electronicInvoiceProductQuantity',
            'electronicInvoiceProductPrice',
            'electronicInvoiceProductVat',
            'electronicInvoiceRegister',
            'electronicInvoiceEnterTaxCode',
            'electronicInvoiceListProduct'

        ],
    },
    2: {
        lang: 'Jap',
        fields: [
            'componentductPrice',
            'componentAddProductPriceNotVAT',
            'minValidMessageNumberPhone',
            'maxValidMessageNumberPhone',
            'fieldIsBlank',
            'matchesNumberPhone',
            'minValidMessagePassword',
            'maxValidMessagePassword',
            'titleApp',
            'qlbh',
            'nameApp',
            'phone',
            'pass',
            'login',
            'registry',
            'send',
            'contact',
            'phoneContact',
            'email',
            'currencySymbol',
            'agree',
            'cancel',
            'confirmPasswordMessage',
            'minValidMessageName',
            'maxValidMessageName',
            'minValidMessageAddress',
            'maxValidMessageAddress',
            'maxValidMessagePSendInfo',
            'minValidMessagePSendInfo',
            'backToPage',
            'close',
            'tokenExpiresed',
            'messageApp',
            'createCustomersFail',
            'createCustomersSuccess',
            'createCustomersNumberPhoneExist',
            'createCustomerNameFillInBlank',
            'vietVangPhoneNumber',
            'agree',
            'cancel',
            'networkIsError',
            'upLoadImgFail',
            'loginFail',
            'fillInBlank',
            'networkError',
            'messageDisable',
            'addCategorySuccess',
            'fillInBlankCategory',
            'categoryNameExist',
            'addCategoryFail',
            'editCategoryFail',
            'addCategoryExistButDelete',
            'deleteCategorySuccess',
            'categoryNameNotValid',
            'deleteCategoryFail',
            'editCategorySuccess',
            'editCategoryBlank',
            'addProductSuccess',
            'productNameExist',
            'notFullValid',
            'findNotCategory',
            'notSelectedCategory',
            'addProductFail',
            'alertVat',
            'addProductExistButDeleted',
            'deleteProductSuccess',
            'deleteProductFail',
            'editProductSuccess',
            'editNotFullValid',
            'editProductFail',
            'sendInfoSuccess',
            'sendInfoFail',
            'addStaffSuccess',
            'addStaffFaill',
            'infoStaffExist',
            'staffExistButDeleted',
            'notFullValidStaff',
            'notFullValidNameStaff',
            'changeInfoStaffSuccess',
            'phoneNumberExist',
            'phoneNumberNotValid',
            'notFullValidAddress',
            'changeInfoStaffFail',
            'deleteStaffSuccess',
            'deleteStaffFail',
            'changePasswordSuccess',
            'changePasswordFail',
            'addTableSuccess',
            'addTableExist',
            'addTableExistButDeleted',
            'addTableFail',
            'cancelOrderSuccess',
            'cancelOrderFail',
            'deleteTableSuccess',
            'deleteTableFail',
            'deleteMutilTableSuccess',
            'deleteMutilTableSuccess',
            'doNotChooseTableDelete',
            'deleteMutilTableFail',
            'componentChangeProductImage',


            'homeScreenProduct',
            'homeScreenRevenue',
            'homeScreenTable',
            'homeScreenStaff',
            'homeScreenInfomation',
            'homeScreenCustomers',
            'homeScreenRevenueToday',
            'homeScreenOrdersNumbersToday',
            'homeScreenOrders',
            'homeScreenBestSalesToday',
            'homeScreenAllOrders',
            'homeScreenHighestSalesToday',
            'homeScreenDeleteAccount',
            'homeScreenDeleteAccountText1',
            'homeScreenDeleteAccountText2',
            'homeScreenLogout',
            'homeScreenWareHouse',
            'homeScreenTaxReceipt',

            'productsScreenAddProduct',
            'productsScreenListCategories',
            'productsScreenCategoriesTitle',
            'productsScreenListProducts',
            'productsScreenFindProduct',
            'productsScreenCategoryFind',
            'productsScreenDishType',
            'productsScreenMainCourse',
            'productsScreenSideDish',
            'SuccessfullyAddedNewSideDish',
            'FailedToDddNewSideDish',
            'handleProductScreenListSideDish',
            'tableScreenAddTable',
            'tableScreenDeleteTable',
            'tableScreenDeleteTables',
            'tableScreenListTables',
            'tableScreenTableNumber',
            'tableScreenNameFood',
            'tableScreenQuantityFood',
            'tableScreenPriceFood',
            'tableScreenCancelOrders',
            'tableScreenExitTable',
            'tableScreenWarning',
            'tableScreenNameTable',
            'tableScreenMessDeleteTables',
            
            'staffScreenCreateStaffs',
            'staffScreenNameStaff',
            'staffScreenAddressStaff',
            'staffScreenPhoneStaff',
            'staffScreenPasswordStaff',
            'staffScreenPasswordConfirmStaff',
            'staffScreenStatusStaff',
            'staffScreenAddStaff',
            'staffScreenCancelStaff',
            'staffScreenStatusStaffWork',

            'handleProductScreenTitle',
            'productsScreenCategoriesTitle',
            'handleProductScreenName',
            'handleProductScreenVat',
            'handleProductScreenPrice',
            'handleProductScreenAddImage',
            'handleProductScreenUpdateImage',
            'handleProductScreenUpdate',
            'handleProductScreenDelete',
            'handleProductScreenDeleteConfirm',

            'detailStafScreenChangePass',
            'detailStafScreenHidestatistics',
            'detailStafScreenShowstatistics',
            'detailStafScreenStaffNoRevenue',
            'detailStafScreenStaffArrangeBill',
            'detailStafScreenStaffHideBillDeleted',
            'detailStafScreenStaffShowBillDeleted',
            'detailStafScreenStaffFindBill',
            'detailStafScreenStaffPaidBill',
            'detailStafScreenStaffDeleteBill',
            'detailStafScreenStaffDeleteBillConfirm',
            'detailStafScreenStaffInvoiceStatus',
            'detailStafScreenStaffNoChange',
            'detailStafScreenStaffChanged',
            'detailStafScreenStaffChangedReason',
            'detailStafScreenStaffPriceNoVAT',
            'detailStafScreenStaffPaymented',
            'detailStafScreenStaffDetailBill',
            'detailStafScreenStaffDayCreated',
            'detailStafScreenStaffDayDeleted',
            'detailStafScreenStaffTotalRevenue',
            'detailStafScreenStaffUpdateInfo',
            'detailStafScreenStaffDeleteStaff',
            'detailStafScreenStaffDeleteStaffConfirm',
            'detailStafScreenStaffUpdate',
            'detailStafScreenStaffDeleteSuccess',
            'detailStafScreenStaffDeleteFail',

            'customersScreenTitle',
            'customersScreenListCustommers',
            'customersScreenNewCustomer',
            'customersScreenOldCustomer',
            'customersScreenPaymentMax',
            'customersScreenPaymentMin',
            'customersScreenFindCustomer',
            'customersScreenSortList',

            'chartScreenmonday',
            'chartScreentuesday',
            'chartScreenwednesday',
            'chartScreenthursday',
            'chartScreenfriday',
            'chartScreensaturday',
            'chartScreensunday',
            'chartScreenlegendWeek',
            'chartScreenFirstWeek',
            'chartScreenSecondWeek',
            'chartScreenThirdWeek',
            'chartScreenFourtWeek',
            'chartScreenFifthWeek',
            'chartScreenlegenMonth',
            'chartScreenMessageReason',
            'chartScreenRevenueDay',
            'chartScreenRevenueWeek',
            'chartScreenRevenueMonth',
            'chartScreenDetail',
            'chartScreenLastMonth',
            'chartScreenDayRevenue',
            'chartScreenSeeRevenue',
            'chartScreenEndDay',
            'chartScreenStartDay',
            'chartScreenSeeDetailCustomizableDay',
            'chartScreenRevenuemanagement',
            'chartScreenOther',
            'componentCustomersInfoCustomer',
            'componentCustomersFullName',
            'componentCustomersPhone',
            'componentCustomersCreateDay',
            'componentCustomersTotalPrice',
            'componentCustomersRemainingPoints',
            'componentCustomersPoints',
            'componentCustomersPointsUsed',
            'componentCustomersPointsListPointUsed',

            'componentDetailPaymentId',
            'componentDetailPaymentDate',
            'componentDetailPaymentHour',
            'componentDetailPaymentNoted',
            'componentDetailPaymentNoReason',
            'componentDetailPaymentDiscountAmount',
            'componentDetailPaymentUsedInStone',
            'componentDetailPaymentTaskAway',
            'componentDetailPaymentListFoodUsed',
            'componentDetailPaymentName',
            'componentDetailPaymentQuantity',
            'componentDetailPaymentPrice',
            'componentDetailPaymentVAT',
            'componentDetailPaymentTotalPrice',

            'componentCategoriesTitle',
            'componentCategoriesWarning',
            'componentCategoriesCreateCategory',
            'componentCategoriesCreate',
            'componentCategoriesWarningDelete',

            'componentCustomersWarningPhone',
            'componentCustomersName',
            'componentCustomersCreate',

            'componentAddImagesUpdate',
            'componentAddImagesUpload',
            'componentAddImagesTitle',
            'componentAddImagesSuccess',

            'componentAddProductChooseCategory',
            'componentAddProductCategories',
            'componentAddProductName',
            'componentAddProductPrice',
            'componentAddProductVat',
            'componentAddProductImage',
            'componentAddProductCreate',
'componentProductPrice',
            'componentCreateFooterSuccess',
            'componentExistFooter',
            'componentFailFooter',
            'componentFooterTitle',
            'componentCreateTitle',
            'componentCloseTitle',

            'componentSendInfoContent',
            'componentLogoTitle',
            'componentLogoUploadLogo',
            'componentUpdateLogoSuccess',
            'componentSendInfo',
            'InfoScreenDetailStore',
            'InfoScreenNameStore',
            'InfoScreenNameBossStore',
            'InfoScreenNoName',
            'InfoScreenExpired',
            'InfoScreenQtiStaff',
            'InfoScreenTraniner',
            'InfoScreenAddress',
            'InfoScreenContent',
            'InfoScreenUpdateFooter',
            'InfoScreenCreateFooter',
            'InfoScreenUpdateImageFooter',

            'RestoreCategories',
            'CategoryExist',
            'RestoreTableSuccess',
            'RestoreStaffSuccess',
            'RestoreProductSuccess',
            'PriceOnlyInt',

            'highestRevenue',
            'lowestRevenue',
            'latestInvoice',
            'oldestInvoice',

            'onlineStatus',
            'offlineStatus',
            'stopStatus',
            'detailFoodStartToEndFrom',
            'detailFoodStartToEndTo',
            'detailFoodStartToEndDetailRevenue',
            'updateFooterSuccess',
            'updateFooterFail',
            'DeliveryNote',
            'ProductNotSelected',
            'deliveryNote',
            'ExportBill',
            'ExportWarehouseQuantityFood',
            'ProductNotCreated',
            'Cash',
            'Transfer',
            'Bank',
            'Export',
            'Return',
            'CancelOutput',
            'WarehouseSuccess',
            'WarehouseReturnSuccess',
            'WarehouseCancelSuccess',
            'WarehouseExport',
            'WarehouseExportError',
            'RepresentativeName',
            'AgencyPhoneNumber',
            'AgencyName',
            'AgencyHaveNotSelected',
            'AgenciesHaveNotSelected',
            'CodeFormInput',
            'CodeFormExport',
            'PriceExport',
            'PriceExportCancel',
            'DiscountPrice',
            'ProductNotSelected',
            'TotalPrice',
            'PaymentMethod',
            'NoteExport',
            'TotalPriceProduct',
            'CreateDeliveryExport',
            'CreateDeliveryBack',
            'CreateDeliveryCancel',
            'CreateDeliveryImport',
            'AddDeliveryExport',
            'AddDeliveryError',
            'CancelDelivery',
            'DetailProduct',
            'ProductCode',
            'ProductNameInventory',
            'PriceInventory',
            'QuantityInventory',
            'AddDelivery',
            'QuantityDelivery',
            'CreateDelivery',
            'WarningDiscount',
            'AddDeliveryBillExport',
            'PriceImport',
            'WarehouseExportTitle',
            'WarehouseFindProduct',
            'WarehouseTaxProduct',
            'BillExportExist',
            'TitleInfoAgency',
            'CompanyName',
            'AgencyNameInfo',
            'AgencyTaxCode',
            'AgencyAddress',
            'AgencyRepresentative',
            'AgencyEmail',
            'AgencyPhoneNumberInfo',
            'AgencyNote',
            'CreateAgencySuccess',
            'CreateAgencyFail',
            'Surcharge',
            'messLoginFail401',
            'messLoginFail204',
            'listInvoice',
            'enterInvoice',
            'dateCreateInvoice',
            'totalPriceInvoice',
            'paymentCodeInvoice',
            'electronicInvoiceIndividual',
            'electronicInvoiceCompany',
            'electronicInvoiceTaxCodeEmpty',
            'electronicInvoiceSuccess',
            'electronicInvoiceFail',
            'electronicInvoiceTaxCodeValid',
            'electronicInvoiceTaxCodeInvalid',
            'electronicInvoiceSuccessRegister',
            'electronicInvoiceEmailInvalid',
            'electronicInvoiceError',
            'electronicInvoiceDetail',
            'electronicInvoiceNumber',
            'electronicInvoiceCode',
            'electronicInvoiceDate',
            'electronicInvoiceTotal',
            'electronicInvoiceTaxCode',
            'electronicInvoiceCustomerName',
            'electronicInvoiceCompanyName',     
            'electronicInvoiceEmail',
            'electronicInvoiceAddress',
            'electronicInvoiceProductName',
            'electronicInvoiceProductQuantity',
            'electronicInvoiceProductPrice',
            'electronicInvoiceProductVat',
            'electronicInvoiceRegister',
             'electronicInvoiceEnterTaxCode',
            'electronicInvoiceListProduct'
        ],
    },
};

export default languages