const defaultLangue = {
    screen: {
        backgroundcolor: '#FFFFFF',
        blackcolor: '#000000',
        diamondmember: '#66CDAA',
        goldmember: '#EEAD0E',
        graycolor: '#808080',
        maincolor: '#FF6D22',
        modalbackground: 'RGBA(0,0,0,0.5)',
        platiummember: '#DA70D6',
        sivermember: '#A0A0A0',
        useroffline: '#FF6347',
        useronline: '#00FA9A',
        userstop: '#DCDCDC',
        matchNumberPhone: /^(84|0[1|2|3|4|5|6|7|8|9])+([0-9]{8})\b/g,
        fieldIsBlank: 'Không được bỏ trống',
        minValidNumber: 4,
        maxValidNumberPhone: 11,
        maxValidNumber: 40,
        minValidPassword: 4,
        maxValidPassword: 20,
        maxValidSendInfo: 200,
    }, Viet: {
        titleApp: "<PERSON><PERSON><PERSON> mềm",
        qlbh: "<PERSON>u<PERSON><PERSON> lý bán hàng",
        nameApp: "<PERSON> POS", phone: "<PERSON><PERSON> điện thoại", pass: "<PERSON>ậ<PERSON> khẩu",
        login: "Đ<PERSON>ng nhập",
        registry: "Đăng ký đối tác kinh doanh",
        contact: "Liên hệ với chúng tôi",
        phoneContact: "SĐT: 02862651411",
        email: "Email: <EMAIL>",
        minValidMessageNumberPhone: "Số điện thoại quá ngắn",
        maxValidMessageNumberPhone: "Số điện thoại quá dài",
        fieldIsBlank: "Không được bỏ trống",
        matchesNumberPhone: "Nhập đúng định dạng số điện thoại",
        minValidMessagePassword: "Mật khẩu quá ngắn",
        maxValidMessagePassword: "Mật khẩu quá dài",
        confirmPasswordMessage: "Mật khẩu xác thực phải giống với mật khẩu",
        minValidMessageName: "Họ và tên lớn hơn 4 kí tự",
        maxValidMessageName: "Họ và tên bé hơn 40 kí tự",
        minValidMessageAddress: "Địa chỉ lớn hơn 4 kí tự",
        maxValidMessageAddress: "Địa chỉ bé hơn 40 kí tự",
        minValidMessagePSendInfo: "Nội dung liên lạc quá ngắn",
        maxValidMessagePSendInfo: "Nội dung liên lạc quá dài",
        send: "Gửi",
        currencySymbol: "đ",
        backToPage: "Bạn muốn quay lại trang trước",
        close: "Đóng",
        tokenExpiresed: "Phiên đăng nhập của bạn đã hết hạn .Vui lòng đăng nhập lại",
        messageApp: "Thông báo",
        createCustomersFail: "Gửi thông tin liên hệ thất bại",
        createCustomersSuccess: "Thêm khách hàng thành công",
        createCustomersNumberPhoneExist: "Số điện thoại khách hàng đã tồn tại",
        createCustomerNameFillInBlank: "Vui lòng điền tên khách hàng",
        vietVangPhoneNumber: "02862651411",
        cancel: "Huỷ bỏ",
        networkIsError: "Có lỗi xảy ra",
        loginFail: "Sai mật khẩu hoặc tài khoản",
        fillInBlank: "Không đc bỏ trống",
        networkError: "Không có mạng vui lòng đăng nhập lại sau",
        messageDisable: "Gửi yêu cầu xoá tài khoản admin thành công .Bạn đã bị đăng xuất",
        addCategorySuccess: "Thêm danh mục thành công",
        fillInBlankCategory: "Chưa điền tên danh mục",
        categoryNameExist: "Tên danh mục đã tồn tại",
        addCategoryFail: "Lỗi thêm danh mục",
        editCategoryFail: "Cập nhật danh mục không thành công",
        addCategoryExistButDelete: "Danh mục này đã tồn tại và hiện tại đang bị xóa bạn có muốn khôi phục",
        deleteCategorySuccess: "Xoá danh mục thành công",
        categoryNameNotValid: "Danh mục không hợp lệ",
        deleteCategoryFail: "Xoá danh mục không thành công",
        editCategorySuccess: "Cập nhật danh mục thành công",
        editCategoryBlank: "Không được bỏ trống tên danh mục",
        addProductSuccess: "Thêm sản phẩm thành công",
        productNameExist: "Tên sản phẩm đã tồn tại",
        notFullValid: "Chưa điền đầy đủ thông tin",
        findNotCategory: "Không tìm thấy danh mục",
        notSelectedCategory: "Vui lòng thêm danh mục",
        addProductFail: "Tạo sản phẩm thất bại",
        alertVat: "Nhập VAT từ 0 đến 100 ",
        addProductExistButDeleted: "Sản phẩm này đã tồn tại và hiện tại đang bị xóa bạn có muốn khôi phục ",
        deleteProductSuccess: "Xoá sản phẩm thành công ",
        deleteProductFail: "Có lỗi xảy ra vui lòng thử lại sau ",
        editProductSuccess: "Cập nhật thông tin sản phẩm thành công ",
        editNotFullValid: "Chưa điền đầy đủ hoặc sai thông tin ",
        editProductFail: "Cập nhật thông tin sản phẩm thất bại ",
        sendInfoSuccess: "Cảm ơn bạn đã gửi thông tin.Chúng tôi sẽ liên lạc lại sau ",
        sendInfoFail: "Gửi thông tin thất bại, không được bỏ trống ",
        addStaffSuccess: "Thêm nhân viên thành công ",
        addStaffFaill: "Thêm nhân viên thất bại ",
        infoStaffExist: "Số điện thoại này đã được đăng ký vui lòng đăng ký số điện thoại khác ",
        staffExistButDeleted: "Thông tin nhân viên đã tồn tại nhưng hiện tại đã bị xoá bạn có muốn khôi phục ",
        notFullValidStaff: "Không được bỏ trống thông tin ",
        notFullValidNameStaff: "Không được bỏ trống tên nhân viên ",
        changeInfoStaffSuccess: "Cập nhật thông tin nhân viên thành công ",
        phoneNumberExist: "Số điện thoại đã tồn tại ",
        phoneNumberNotValid: "Số điện thoại phải là 10 số ",
        notFullValidAddress: "Không được bỏ trống địa chỉ ",
        changeInfoStaffFail: "Có lỗi xảy ra vui lòng thử lại sau ",
        deleteStaffSuccess: "Xoá nhân viên thành công ",
        deleteStaffFail: "Nhân viên đang trực tuyến không thể xoá.Vui lòng đổi thành offline để xoá ",
        changePasswordSuccess: "Thay đổi mật khẩu nhân viên thành công ",
        changePasswordFail: "Thay đổi mật khẩu nhân viên thất bại ",
        addTableSuccess: "Thêm bàn thành công",
        addTableExist: "Bàn đã tồn tại ",
        addTableExistButDeleted: " Bàn này đã tồn tại và hiện tại đang bị xóa bạn có muốn khôi phục lại ",
        addTableFail: " Thêm bàn thất bại ",
        cancelOrderSuccess: " Huỷ bàn thành công ",
        cancelOrderFail: " Huỷ bàn thất bại ",
        deleteTableSuccess: " Xoá bàn thành công ",
        deleteTableFail: " Xoá bàn thất bại ",
        deleteMutilTableSuccess: " Xoá các bàn thành công ",
        doNotChooseTableDelete: " Chưa chọn bàn để xoá ",
        deleteMutilTableFail: " Xoá các bàn thất bại ",
        homeScreenProduct: " Sản phẩm ",
        homeScreenRevenue: " Doanh thu ",
        homeScreenTable: " Quản lý bàn ",
        homeScreenStaff: " Nhân viên ",
        homeScreenInfomation: " Thông tin thêm ",
        homeScreenCustomers: " Khách hàng ",
        homeScreenWareHouse: " Quản lý kho ",
        homeScreenRevenueToday: " Tổng doanh thu hôm nay ",
        homeScreenOrdersNumbersToday: " Số đơn hàng đã thanh toán trong ngày ",
        homeScreenOrders: " đơn ",
        homeScreenBestSalesToday: " Sản phẩm bán chạy nhất hôm nay ",
        homeScreenAllOrders: " Tổng số lượng ",
        homeScreenHighestSalesToday: " Sản phẩm có doanh thu cao nhất hôm nay ",
        homeScreenLogout: " Đăng xuất khỏi tài khoản ",
        homeScreenDeleteAccount: " Xác nhận xóa tài khoản ra khỏi hệ thống Delta Pos.Lưu ý:Sau khi xóa, dữ liệu của bạn không thể khôi phục lại",
        productsScreenAddProduct: " Thêm sản phẩm ",
        productsScreenListCategories: " Danh mục sản phẩm ",
        productsScreenListProducts: " Danh sách các sản phẩm ",
        productsScreenFindProduct: " Tìm tên sản phẩm ",
        productsScreenCategoryFind: " Chưa chọn danh mục ",
        tableScreenAddTable: " Thêm bàn",
        tableScreenNameTable: "Tên bàn ",
        tableScreenDeleteTable: "Xoá bàn ",
        tableScreenDeleteTables: "Xoá nhiều bàn ",
        tableScreenListTables: "Danh sách bàn tại quán ",
        tableScreenTableNumber: "Bàn số ",
        tableScreenNameFood: "Tên món ăn ",
        tableScreenQuantityFood: "Số lượng ",
        tableScreenPriceFood: "Giá tiền ",
        tableScreenCancelOrders: "Huỷ đơn ",
        tableScreenExitTable: "Thoát ",
        tableScreenWarning: "Lưu ý:Những sản phẩm đã đặt sẽ bị huỷ hết ",
        staffScreenCreateStaffs: "Thêm nhân viên ",
        staffScreenNameStaff: "Tên nhân viên ",
        staffScreenAddressStaff: "Địa chỉ ",
        staffScreenPhoneStaff: "Số điện thoại ",
        staffScreenPasswordStaff: "Mật khẩu ",
        staffScreenPasswordConfirmStaff: "Xác nhận mật khẩu ",
        staffScreenStatusStaff: "Trạng thái ",
        staffScreenStatusStaffWork: "Trạng thái hoạt động ",
        staffScreenAddStaff: "Thêm ",
        staffScreenCancelStaff: "Huỷ ",
        handleProductScreenTitle: "Danh mục sản phẩm ",
        handleProductScreenName: "Tên sản phẩm ",
        handleProductScreenVat: "Thuế ",
        handleProductScreenPrice: "Giá tiền ",
        handleProductScreenAddImage: "Thêm hình ảnh ",
        handleProductScreenUpdateImage: "Thay đổi hình ảnh",
        handleProductScreenUpdate: "Cập nhật",
        handleProductScreenDelete: "Xoá",
        handleProductScreenDeleteConfirm: "Xác nhận xoá sản phẩm",
        detailStafScreenChangePass: "Thay đổi mật khẩu",
        detailStafScreenHidestatistics: "Ẩn thống kê",
        detailStafScreenShowstatistics: "Xem thống kê",
        detailStafScreenStaffNoRevenue: "Nhân viên chưa có doanh thu",
        detailStafScreenStaffArrangeBill: "Sắp xếp hoá đơn ↑↓",
        detailStafScreenStaffHideBillDeleted: "Ẩn hoá đơn đã xoá",
        detailStafScreenStaffShowBillDeleted: "Xem hoá đơn đã xoá",
        detailStafScreenStaffFindBill: "Tra cứu số hoá đơn",
        detailStafScreenStaffPaidBill: "Tên nhân viên thanh toán",
        detailStafScreenStaffDeleteBill: "Huỷ hoá đơn này",
        detailStafScreenStaffDeleteBillConfirm: "Xác nhận huỷ hoá đơn",
        detailStafScreenStaffInvoiceStatus: "Tình trạng hoá đơn",
        detailStafScreenStaffNoChange: "Không có thay đổi",
        detailStafScreenStaffChanged: "Đã thay đổi",
        detailStafScreenStaffChangedReason: "Lý do thay đổi",
        detailStafScreenStaffPriceNoVAT: "Tổng tiền chưa bao gồm VAT",
        detailStafScreenStaffPaymented: "Khách trả",
        detailStafScreenStaffDetailBill: "Chi tiết hoá đơn",
        detailStafScreenStaffDayCreated: "Ngày tạo",
        detailStafScreenStaffDayDeleted: "Ngày xoá",
        detailStafScreenStaffTotalRevenue: "Tổng doanh thu",
        detailStafScreenStaffUpdateInfo: "Cập nhật thông tin",
        detailStafScreenStaffDeleteStaff: "Xoá nhân viên",
        detailStafScreenStaffDeleteStaffConfirm: "Xác nhận xoá nhân viên",
        detailStafScreenStaffUpdate: "Thay đổi",
        detailStafScreenStaffDeleteSuccess: "Xoá bill thành công",
        detailStafScreenStaffDeleteFail: "Xoá bill thất bại",
        detailFoodStartToEndFrom: "Từ ngày",
        detailFoodStartToEndTo: "đến",
        detailFoodStartToEndDetailRevenue: "Chi tiết doanh thu",
        customersScreenTitle: "Thêm khách hàng",
        customersScreenListCustommers: "Danh sách khách hàng",
        customersScreenNewCustomer: "Khách hàng mới",
        customersScreenOldCustomer: "Khách hàng cũ",
        customersScreenPaymentMax: "Điểm tích lũy nhiều",
        customersScreenPaymentMin: "Điểm tích lũy ít",
        customersScreenFindCustomer: "Tìm kiếm khách hàng",
        customersScreenSortList: "Sắp xếp danh sách",
        chartScreenmonday: "Thứ 2",
        chartScreentuesday: "Thứ 3",
        chartScreenwednesday: "Thứ 4",
        chartScreenthursday: "Thứ 5",
        chartScreenfriday: "Thứ 6",
        chartScreensaturday: "Thứ 7",
        chartScreensunday: "CN",
        chartScreenlegendWeek: "Doanh thu trong tuần",
        chartScreenFirstWeek: "Tuần 1",
        chartScreenSecondWeek: "Tuần 2",
        chartScreenThirdWeek: "Tuần 3",
        chartScreenFourtWeek: "Tuần 4",
        chartScreenFifthWeek: "Tuần 5",
        chartScreenlegenMonth: "Doanh thu trong tháng",
        chartScreenLastMonth: "Doanh thu tháng trước",
        chartScreenMessageReason: "Ngày bắt đầu phải bé hơn ngày kết thúc",
        chartScreenRevenueDay: "Ngày",
        chartScreenRevenueWeek: "Tuần",
        chartScreenRevenueMonth: "Tháng",
        chartScreenRevenuemanagement: "Quản lý doanh thu",
        chartScreenDetail: "Xem chi tiết",
        chartScreenSeeDetailCustomizableDay: "Xem doanh thu theo ngày tuỳ chọn",
        chartScreenStartDay: "Ngày bắt đầu",
        chartScreenEndDay: "Ngày kết thúc",
        chartScreenSeeRevenue: "Xem doanh thu",
        chartScreenDayRevenue: "Doanh thu ngày",
        componentCustomersInfoCustomer: "Thông tin khách hàng",
        componentCustomersFullName: "Họ và tên",
        componentCustomersCreateDay: "Ngày đăng ký",
        componentCustomersTotalPrice: "Tổng số tiền đã tích luỹ",
        componentCustomersRemainingPoints: "Số điểm còn lại",
        componentCustomersPoints: "điểm",
        componentCustomersPointsUsed: "Lịch sử dùng điểm",
        componentCustomersPointsListPointUsed: "Danh sách điểm đã dùng",
        componentDetailPaymentId: "Số hoá đơn",
        componentDetailPaymentDate: "Ngày thanh toán",
        componentDetailPaymentHour: "Giờ thanh toán",
        componentDetailPaymentNoted: "Ghi chú",
        componentDetailPaymentNoReason: "Không có lý do",
        componentDetailPaymentDiscountAmount: "Số tiền giảm",
        componentDetailPaymentUsedInStone: "Khách ăn tại quán",
        componentDetailPaymentTaskAway: "Mang về",
        componentDetailPaymentListFoodUsed: "Danh sách sản phẩm đã thanh toán",
        componentDetailPaymentName: "Tên",
        componentDetailPaymentQuantity: "SL",
        componentDetailPaymentPrice: "Giá tiền",
        componentDetailPaymentVAT: "Thuế",
        componentDetailPaymentTotalPrice: "Tổng tiền",
        componentCategoriesTitle: "Quản lý danh mục",
        componentCategoriesWarning: "Không được bỏ trống tên danh mục",
        componentCategoriesCreateCategory: "Thêm danh mục",
        componentCategoriesCreate: "Thêm",
        componentCategoriesWarningDelete: "Lưu ý: Những sản phẩm trong danh mục này sẽ bị xoá hết",
        componentCustomersWarningPhone: "Số điện thoại này đã được đăng ký",
        componentCustomersName: "Tên khách hàng",
        componentCustomersPhone: "Số điện thoại",
        componentCustomersCreate: "Thêm khách hàng",
        componentAddImagesSuccess: "Cập nhật ảnh footer thành công",
        componentAddImagesTitle: "Ảnh footer",
        componentAddImagesUpload: "Tải ảnh footer",
        componentAddImagesUpdate: "Cập nhật",
        componentAddProductChooseCategoy: "Chọn danh mục",
        componentAddProductCategories: "Danh mục trống",
        componentAddProductName: "Tên",
        componentAddProductPrice: "Giá tiền",
        componentAddProductVat: "Thuế",
        componentAddProductImage: "Thêm hình ảnh",
        componentAddProductCreate: "Thêm sản phẩm",
        componentCreateFooterSuccess: "Tạo footer thành công",
        componentExistFooter: "Cửa hàng đã tồn tại footer",
        componentFailFooter: "Tạo footer thất bại",
        componentFooterTitle: "Footer máy in",
        componentCreateTitle: "Tạo",
        componentCloseTitle: "Đóng",
        componentSendInfo: "Gửi thông tin liên hệ",
        componentSendInfoContent: "Nội dung cần liên lạc",
        componentUpdateLogoSuccess: "Cập nhật logo thành công",
        componentLogoTitle: "Logo",
        componentLogoUploadLogo: "Tải ảnh logo",
        InfoScreenDetailStore: "Chi tiết cửa hàng",
        InfoScreenNameStore: "Tên cửa hàng",
        InfoScreenNameBossStore: "Tên chủ cửa hàng",
        InfoScreenNoName: "Chưa đặt tên",
        InfoScreenExpired: "Ngày hết hạn",
        InfoScreenQtiStaff: "Số lượng nhân viên",
        InfoScreenTraniner: "Bản thử nghiệm",
        InfoScreenAddress: "Địa chỉ cửa hàng",
        InfoScreenContent: "Nội dung",
        InfoScreenUpdateFooter: "Cập nhật footer máy in",
        InfoScreenCreateFooter: "Thêm footer máy in",
        InfoScreenUpdateImageFooter: "Cập nhật ảnh footer máy in",
        RestoreCategories: "Khôi phục danh mục thành công",
        CategoryExist: "Tên danh mục đã tồn tại",
        RestoreTableSuccess: "Khôi phục bàn thành công",
        RestoreStaffSuccess: "Khôi phục nhân viên thành công",
        RestoreProductSuccess: "Khôi phục sản phẩm thành công",
        PriceOnlyInt: "Giá tiền chỉ nhập số nguyên",
        highestRevenue: "Doanh thu cao nhất ↓",
        lowestRevenue: "Doanh thu thấp nhất ↑",
        latestInvoice: "Hoá đơn mới nhất ↓",
        oldestInvoice: "Hoá đơn cũ nhất ↑",
        onlineStatus: "Online",
        offlineStatus: "Offline",
        stopStatus: "Stop",
        updateFooterSuccess: "Thay đổi footer thành công",
        updateFooterFail: "Thay đổi footer thất bại",
        deliveryNote: "Phiếu xuất kho",
        ExportBill: "Phiếu nhập kho",
        ExportWarehouseQuantityFood: "Số lượng",
        ProductNotCreated: "Chưa tạo sản phẩm",
        Cash: "Tiền mặt",
        Transfer: "Chuyển khoản",
        Bank: "Thẻ ngân hàng",
        Export: "Xuất hàng",
        Return: "Hoàn trả đại lý",
        CancelOutput: "Huỷ hàng",
        WarehouseSuccess: "Xuất kho thành công",
        WarehouseReturnSuccess: "Trả hàng thành công",
        WarehouseCancelSuccess: "Huỷ hàng thành công",
        WarehouseExport: "Số lượng sản phẩm không đủ để xuất kho",
        WarehouseExportError: "Có lỗi xẩy ra vui lòng thử lại",
        RepresentativeName: "Tên người đại diện",
        AgencyPhoneNumber: "SĐT người đại diện",
        AgencyName: "Tên đại lý",
        AgencyHaveNotSelected: "Chưa chọn đại lý",
        AgenciesHaveNotSelected: "Chưa tạo danh sách đại lý",
        CodeFormInput: "Mã phiếu nhập",
        CodeFormExport: "Mã phiếu xuất",
        PriceExport: "Giá xuất kho",
        PriceExportCancel: "Giá xuất huỷ",
        DiscountPrice: "Giảm giá",
        ProductNotSelected: "Chưa chọn sản phẩm",
        TotalPrice: "Tổng tiền thanh toán",
        PaymentMethod: "Phương thức thanh toán",
        NoteExport: "Ghi chú",
        TotalPriceProduct: "Tổng tiền sản phẩm",
        CreateDeliveryExport: "Tạo phiếu xuất",
        CreateDeliveryBack: "Tạo phiếu trả",
        CreateDeliveryCancel: "Tạo phiếu huỷ",
        CreateDeliveryImport: "Tạo phiếu nhập",
        AddDeliveryExport: "Thêm tồn kho thành công",
        AddDeliveryError: "Có lỗi xảy ra, vui lòng thử lại sau",
        CancelDelivery: "Huỷ",
        DetailProduct: "Chi tiết sản phẩm",
        ProductCode: "Mã số sản phẩm",
        ProductNameInventory: "Tên sản phẩm",
        PriceInventory: "Gía tiền",
        QuantityInventory: "Số lượng tồn kho",
        AddDelivery: "Thêm tồn kho",
        QuantityDelivery: "Số lượng",
        CreateDelivery: "Tạo",
        WarningDiscount: "Số tiền giảm giá cao hơn tổng tiền",
        AddDeliveryBillExport: "Thêm phiếu tồn kho thành công",
        PriceImport: "Giá nhập",
        WarehouseExportTitle: "Xuất Kho",
        WarehouseFindProduct: "Tìm kiếm sản phẩm",
        WarehouseTaxProduct: "Thuế",
        BillExportExist: "Mã phiếu nhập đã tồn tại",
        Surcharge: "Phụ thu",
        listInvoice: "Danh sách hoá đơn",
        enterInvoice: "Nhập số hoá đơn",
        DateCreateInvoice: "Ngày tạo",
        totalPriceInvoice: "Tổng tiền",
        paymentCodeInvoice:"Payment Code",
        electronicInvoiceIndividual:"Cá nhân",
        electronicInvoiceCompany:"Doanh nghiệp",
        electronicInvoiceTaxCodeEmpty:"Mã số thuế không được để trống",
        electronicInvoiceSuccess:"Thành công",
        electronicInvoiceFail:"Thất bại",
        electronicInvoiceTaxCodeValid:"Mã số thuế hợp lệ",
        electronicInvoiceTaxCodeInvalid:"Mã số thuế không tồn tại",
        electronicInvoiceSuccessRegister:"Đăng ký hoá đơn điện tử thành công",
        electronicInvoiceEmailInvalid:"Email không đúng định dạng",
        electronicInvoiceError:"Đã có lỗi xảy ra",
        electronicInvoiceDetail:"Chi tiết hoá đơn",
        electronicInvoiceNumber:"Số hoá đơn",
        electronicInvoiceCode:"Mã hoá đơn",
        electronicInvoiceDate:"Ngày thanh toán",
        electronicInvoiceTotal:"Tổng tiền",
        electronicInvoiceTaxCode:"Nhập mã số thuế",
        electronicInvoiceCustomerName:"Tên khách hàng",
        electronicInvoiceCompanyName:"Tên công ty",
        electronicInvoiceEmail:"Email",
        electronicInvoiceAddress:"Địa chỉ",
        electronicInvoiceProductName:"Tên sản phẩm",
        electronicInvoiceProductQuantity:"Sl",
        electronicInvoiceProductPrice:"Giá tiền",
        electronicInvoiceProductVat:"Vat",
        electronicInvoiceRegister:"Đăng ký hoá đơn điện tử",
        electronicInvoiceEnterTaxCode:"Nhập mã số thuế",
        electronicInvoiceListProduct:"Danh sách sản phẩm trong hoá đơn"
    }
}
export default defaultLangue