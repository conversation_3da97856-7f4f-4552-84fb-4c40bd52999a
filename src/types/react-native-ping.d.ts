declare module 'react-native-ping' {
  export function start(
    ipAddress: string,
    options?: { timeout?: number }
  ): Promise<number>;

  export function ping(
    ipAddress: string,
    options?: { timeout?: number }
  ): Promise<number>;

  export function getTrafficStats(): Promise<any>;

  export function getGatewayIp(): Promise<string>;

  export function getDnsServerIp(): Promise<string>;

  export function getHostIp(): Promise<string>;

  export function getNetworkType(): Promise<string>;

  export function getMacAddress(): Promise<string>;
}
